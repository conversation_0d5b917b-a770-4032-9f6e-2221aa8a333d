{"ast": null, "code": "function _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nimport { ReferenceDot } from '../cartesian/ReferenceDot';\nimport { ReferenceLine } from '../cartesian/ReferenceLine';\nimport { ReferenceArea } from '../cartesian/ReferenceArea';\nimport { ifOverflowMatches } from './IfOverflowMatches';\nimport { findAllByType } from './ReactUtils';\nimport { isNumber } from './DataUtils';\nexport var detectReferenceElementsDomain = function detectReferenceElementsDomain(children, domain, axisId, axisType, specifiedTicks) {\n  var lines = findAllByType(children, ReferenceLine);\n  var dots = findAllByType(children, ReferenceDot);\n  var elements = [].concat(_toConsumableArray(lines), _toConsumableArray(dots));\n  var areas = findAllByType(children, ReferenceArea);\n  var idKey = \"\".concat(axisType, \"Id\");\n  var valueKey = axisType[0];\n  var finalDomain = domain;\n  if (elements.length) {\n    finalDomain = elements.reduce(function (result, el) {\n      if (el.props[idKey] === axisId && ifOverflowMatches(el.props, 'extendDomain') && isNumber(el.props[valueKey])) {\n        var value = el.props[valueKey];\n        return [Math.min(result[0], value), Math.max(result[1], value)];\n      }\n      return result;\n    }, finalDomain);\n  }\n  if (areas.length) {\n    var key1 = \"\".concat(valueKey, \"1\");\n    var key2 = \"\".concat(valueKey, \"2\");\n    finalDomain = areas.reduce(function (result, el) {\n      if (el.props[idKey] === axisId && ifOverflowMatches(el.props, 'extendDomain') && isNumber(el.props[key1]) && isNumber(el.props[key2])) {\n        var value1 = el.props[key1];\n        var value2 = el.props[key2];\n        return [Math.min(result[0], value1, value2), Math.max(result[1], value1, value2)];\n      }\n      return result;\n    }, finalDomain);\n  }\n  if (specifiedTicks && specifiedTicks.length) {\n    finalDomain = specifiedTicks.reduce(function (result, tick) {\n      if (isNumber(tick)) {\n        return [Math.min(result[0], tick), Math.max(result[1], tick)];\n      }\n      return result;\n    }, finalDomain);\n  }\n  return finalDomain;\n};", "map": {"version": 3, "names": ["_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "call", "slice", "constructor", "name", "Array", "from", "test", "iter", "Symbol", "iterator", "isArray", "len", "length", "i", "arr2", "ReferenceDot", "ReferenceLine", "ReferenceArea", "ifOverflowMatches", "findAllByType", "isNumber", "detectReferenceElementsDomain", "children", "domain", "axisId", "axisType", "specifiedTicks", "lines", "dots", "elements", "concat", "areas", "id<PERSON><PERSON>", "valueKey", "finalDomain", "reduce", "result", "el", "props", "value", "Math", "min", "max", "key1", "key2", "value1", "value2", "tick"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/recharts/es6/util/DetectReferenceElementsDomain.js"], "sourcesContent": ["function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nimport { ReferenceDot } from '../cartesian/ReferenceDot';\nimport { ReferenceLine } from '../cartesian/ReferenceLine';\nimport { ReferenceArea } from '../cartesian/ReferenceArea';\nimport { ifOverflowMatches } from './IfOverflowMatches';\nimport { findAllByType } from './ReactUtils';\nimport { isNumber } from './DataUtils';\nexport var detectReferenceElementsDomain = function detectReferenceElementsDomain(children, domain, axisId, axisType, specifiedTicks) {\n  var lines = findAllByType(children, ReferenceLine);\n  var dots = findAllByType(children, ReferenceDot);\n  var elements = [].concat(_toConsumableArray(lines), _toConsumableArray(dots));\n  var areas = findAllByType(children, ReferenceArea);\n  var idKey = \"\".concat(axisType, \"Id\");\n  var valueKey = axisType[0];\n  var finalDomain = domain;\n  if (elements.length) {\n    finalDomain = elements.reduce(function (result, el) {\n      if (el.props[idKey] === axisId && ifOverflowMatches(el.props, 'extendDomain') && isNumber(el.props[valueKey])) {\n        var value = el.props[valueKey];\n        return [Math.min(result[0], value), Math.max(result[1], value)];\n      }\n      return result;\n    }, finalDomain);\n  }\n  if (areas.length) {\n    var key1 = \"\".concat(valueKey, \"1\");\n    var key2 = \"\".concat(valueKey, \"2\");\n    finalDomain = areas.reduce(function (result, el) {\n      if (el.props[idKey] === axisId && ifOverflowMatches(el.props, 'extendDomain') && isNumber(el.props[key1]) && isNumber(el.props[key2])) {\n        var value1 = el.props[key1];\n        var value2 = el.props[key2];\n        return [Math.min(result[0], value1, value2), Math.max(result[1], value1, value2)];\n      }\n      return result;\n    }, finalDomain);\n  }\n  if (specifiedTicks && specifiedTicks.length) {\n    finalDomain = specifiedTicks.reduce(function (result, tick) {\n      if (isNumber(tick)) {\n        return [Math.min(result[0], tick), Math.max(result[1], tick)];\n      }\n      return result;\n    }, finalDomain);\n  }\n  return finalDomain;\n};"], "mappings": "AAAA,SAASA,kBAAkBA,CAACC,GAAG,EAAE;EAAE,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AAAE;AACxJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAC7L,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIL,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACS,WAAW,EAAEN,CAAC,GAAGH,CAAC,CAACS,WAAW,CAACC,IAAI;EAAE,IAAIP,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOQ,KAAK,CAACC,IAAI,CAACZ,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACU,IAAI,CAACV,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAC/Z,SAASL,gBAAgBA,CAACkB,IAAI,EAAE;EAAE,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAID,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIF,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC;AAAE;AAC7J,SAASnB,kBAAkBA,CAACD,GAAG,EAAE;EAAE,IAAIiB,KAAK,CAACM,OAAO,CAACvB,GAAG,CAAC,EAAE,OAAOQ,iBAAiB,CAACR,GAAG,CAAC;AAAE;AAC1F,SAASQ,iBAAiBA,CAACR,GAAG,EAAEwB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGxB,GAAG,CAACyB,MAAM,EAAED,GAAG,GAAGxB,GAAG,CAACyB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,IAAIV,KAAK,CAACO,GAAG,CAAC,EAAEE,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAEC,IAAI,CAACD,CAAC,CAAC,GAAG1B,GAAG,CAAC0B,CAAC,CAAC;EAAE,OAAOC,IAAI;AAAE;AAClL,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,QAAQ,QAAQ,aAAa;AACtC,OAAO,IAAIC,6BAA6B,GAAG,SAASA,6BAA6BA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAE;EACpI,IAAIC,KAAK,GAAGR,aAAa,CAACG,QAAQ,EAAEN,aAAa,CAAC;EAClD,IAAIY,IAAI,GAAGT,aAAa,CAACG,QAAQ,EAAEP,YAAY,CAAC;EAChD,IAAIc,QAAQ,GAAG,EAAE,CAACC,MAAM,CAAC5C,kBAAkB,CAACyC,KAAK,CAAC,EAAEzC,kBAAkB,CAAC0C,IAAI,CAAC,CAAC;EAC7E,IAAIG,KAAK,GAAGZ,aAAa,CAACG,QAAQ,EAAEL,aAAa,CAAC;EAClD,IAAIe,KAAK,GAAG,EAAE,CAACF,MAAM,CAACL,QAAQ,EAAE,IAAI,CAAC;EACrC,IAAIQ,QAAQ,GAAGR,QAAQ,CAAC,CAAC,CAAC;EAC1B,IAAIS,WAAW,GAAGX,MAAM;EACxB,IAAIM,QAAQ,CAACjB,MAAM,EAAE;IACnBsB,WAAW,GAAGL,QAAQ,CAACM,MAAM,CAAC,UAAUC,MAAM,EAAEC,EAAE,EAAE;MAClD,IAAIA,EAAE,CAACC,KAAK,CAACN,KAAK,CAAC,KAAKR,MAAM,IAAIN,iBAAiB,CAACmB,EAAE,CAACC,KAAK,EAAE,cAAc,CAAC,IAAIlB,QAAQ,CAACiB,EAAE,CAACC,KAAK,CAACL,QAAQ,CAAC,CAAC,EAAE;QAC7G,IAAIM,KAAK,GAAGF,EAAE,CAACC,KAAK,CAACL,QAAQ,CAAC;QAC9B,OAAO,CAACO,IAAI,CAACC,GAAG,CAACL,MAAM,CAAC,CAAC,CAAC,EAAEG,KAAK,CAAC,EAAEC,IAAI,CAACE,GAAG,CAACN,MAAM,CAAC,CAAC,CAAC,EAAEG,KAAK,CAAC,CAAC;MACjE;MACA,OAAOH,MAAM;IACf,CAAC,EAAEF,WAAW,CAAC;EACjB;EACA,IAAIH,KAAK,CAACnB,MAAM,EAAE;IAChB,IAAI+B,IAAI,GAAG,EAAE,CAACb,MAAM,CAACG,QAAQ,EAAE,GAAG,CAAC;IACnC,IAAIW,IAAI,GAAG,EAAE,CAACd,MAAM,CAACG,QAAQ,EAAE,GAAG,CAAC;IACnCC,WAAW,GAAGH,KAAK,CAACI,MAAM,CAAC,UAAUC,MAAM,EAAEC,EAAE,EAAE;MAC/C,IAAIA,EAAE,CAACC,KAAK,CAACN,KAAK,CAAC,KAAKR,MAAM,IAAIN,iBAAiB,CAACmB,EAAE,CAACC,KAAK,EAAE,cAAc,CAAC,IAAIlB,QAAQ,CAACiB,EAAE,CAACC,KAAK,CAACK,IAAI,CAAC,CAAC,IAAIvB,QAAQ,CAACiB,EAAE,CAACC,KAAK,CAACM,IAAI,CAAC,CAAC,EAAE;QACrI,IAAIC,MAAM,GAAGR,EAAE,CAACC,KAAK,CAACK,IAAI,CAAC;QAC3B,IAAIG,MAAM,GAAGT,EAAE,CAACC,KAAK,CAACM,IAAI,CAAC;QAC3B,OAAO,CAACJ,IAAI,CAACC,GAAG,CAACL,MAAM,CAAC,CAAC,CAAC,EAAES,MAAM,EAAEC,MAAM,CAAC,EAAEN,IAAI,CAACE,GAAG,CAACN,MAAM,CAAC,CAAC,CAAC,EAAES,MAAM,EAAEC,MAAM,CAAC,CAAC;MACnF;MACA,OAAOV,MAAM;IACf,CAAC,EAAEF,WAAW,CAAC;EACjB;EACA,IAAIR,cAAc,IAAIA,cAAc,CAACd,MAAM,EAAE;IAC3CsB,WAAW,GAAGR,cAAc,CAACS,MAAM,CAAC,UAAUC,MAAM,EAAEW,IAAI,EAAE;MAC1D,IAAI3B,QAAQ,CAAC2B,IAAI,CAAC,EAAE;QAClB,OAAO,CAACP,IAAI,CAACC,GAAG,CAACL,MAAM,CAAC,CAAC,CAAC,EAAEW,IAAI,CAAC,EAAEP,IAAI,CAACE,GAAG,CAACN,MAAM,CAAC,CAAC,CAAC,EAAEW,IAAI,CAAC,CAAC;MAC/D;MACA,OAAOX,MAAM;IACf,CAAC,EAAEF,WAAW,CAAC;EACjB;EACA,OAAOA,WAAW;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}