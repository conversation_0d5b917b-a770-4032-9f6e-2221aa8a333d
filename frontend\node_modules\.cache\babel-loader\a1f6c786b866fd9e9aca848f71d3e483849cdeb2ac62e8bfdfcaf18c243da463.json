{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nvar MULTIPLY_OR_DIVIDE_REGEX = /(-?\\d+(?:\\.\\d+)?[a-zA-Z%]*)([*/])(-?\\d+(?:\\.\\d+)?[a-zA-Z%]*)/;\nvar ADD_OR_SUBTRACT_REGEX = /(-?\\d+(?:\\.\\d+)?[a-zA-Z%]*)([+-])(-?\\d+(?:\\.\\d+)?[a-zA-Z%]*)/;\nvar CSS_LENGTH_UNIT_REGEX = /^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/;\nvar NUM_SPLIT_REGEX = /(-?\\d+(?:\\.\\d+)?)([a-zA-Z%]+)?/;\nvar CONVERSION_RATES = {\n  cm: 96 / 2.54,\n  mm: 96 / 25.4,\n  pt: 96 / 72,\n  pc: 96 / 6,\n  \"in\": 96,\n  Q: 96 / (2.54 * 40),\n  px: 1\n};\nvar FIXED_CSS_LENGTH_UNITS = Object.keys(CONVERSION_RATES);\nvar STR_NAN = 'NaN';\nfunction convertToPx(value, unit) {\n  return value * CONVERSION_RATES[unit];\n}\nvar DecimalCSS = /*#__PURE__*/function () {\n  function DecimalCSS(num, unit) {\n    _classCallCheck(this, DecimalCSS);\n    this.num = num;\n    this.unit = unit;\n    this.num = num;\n    this.unit = unit;\n    if (Number.isNaN(num)) {\n      this.unit = '';\n    }\n    if (unit !== '' && !CSS_LENGTH_UNIT_REGEX.test(unit)) {\n      this.num = NaN;\n      this.unit = '';\n    }\n    if (FIXED_CSS_LENGTH_UNITS.includes(unit)) {\n      this.num = convertToPx(num, unit);\n      this.unit = 'px';\n    }\n  }\n  return _createClass(DecimalCSS, [{\n    key: \"add\",\n    value: function add(other) {\n      if (this.unit !== other.unit) {\n        return new DecimalCSS(NaN, '');\n      }\n      return new DecimalCSS(this.num + other.num, this.unit);\n    }\n  }, {\n    key: \"subtract\",\n    value: function subtract(other) {\n      if (this.unit !== other.unit) {\n        return new DecimalCSS(NaN, '');\n      }\n      return new DecimalCSS(this.num - other.num, this.unit);\n    }\n  }, {\n    key: \"multiply\",\n    value: function multiply(other) {\n      if (this.unit !== '' && other.unit !== '' && this.unit !== other.unit) {\n        return new DecimalCSS(NaN, '');\n      }\n      return new DecimalCSS(this.num * other.num, this.unit || other.unit);\n    }\n  }, {\n    key: \"divide\",\n    value: function divide(other) {\n      if (this.unit !== '' && other.unit !== '' && this.unit !== other.unit) {\n        return new DecimalCSS(NaN, '');\n      }\n      return new DecimalCSS(this.num / other.num, this.unit || other.unit);\n    }\n  }, {\n    key: \"toString\",\n    value: function toString() {\n      return \"\".concat(this.num).concat(this.unit);\n    }\n  }, {\n    key: \"isNaN\",\n    value: function isNaN() {\n      return Number.isNaN(this.num);\n    }\n  }], [{\n    key: \"parse\",\n    value: function parse(str) {\n      var _NUM_SPLIT_REGEX$exec;\n      var _ref = (_NUM_SPLIT_REGEX$exec = NUM_SPLIT_REGEX.exec(str)) !== null && _NUM_SPLIT_REGEX$exec !== void 0 ? _NUM_SPLIT_REGEX$exec : [],\n        _ref2 = _slicedToArray(_ref, 3),\n        numStr = _ref2[1],\n        unit = _ref2[2];\n      return new DecimalCSS(parseFloat(numStr), unit !== null && unit !== void 0 ? unit : '');\n    }\n  }]);\n}();\nfunction calculateArithmetic(expr) {\n  if (expr.includes(STR_NAN)) {\n    return STR_NAN;\n  }\n  var newExpr = expr;\n  while (newExpr.includes('*') || newExpr.includes('/')) {\n    var _MULTIPLY_OR_DIVIDE_R;\n    var _ref3 = (_MULTIPLY_OR_DIVIDE_R = MULTIPLY_OR_DIVIDE_REGEX.exec(newExpr)) !== null && _MULTIPLY_OR_DIVIDE_R !== void 0 ? _MULTIPLY_OR_DIVIDE_R : [],\n      _ref4 = _slicedToArray(_ref3, 4),\n      leftOperand = _ref4[1],\n      operator = _ref4[2],\n      rightOperand = _ref4[3];\n    var lTs = DecimalCSS.parse(leftOperand !== null && leftOperand !== void 0 ? leftOperand : '');\n    var rTs = DecimalCSS.parse(rightOperand !== null && rightOperand !== void 0 ? rightOperand : '');\n    var result = operator === '*' ? lTs.multiply(rTs) : lTs.divide(rTs);\n    if (result.isNaN()) {\n      return STR_NAN;\n    }\n    newExpr = newExpr.replace(MULTIPLY_OR_DIVIDE_REGEX, result.toString());\n  }\n  while (newExpr.includes('+') || /.-\\d+(?:\\.\\d+)?/.test(newExpr)) {\n    var _ADD_OR_SUBTRACT_REGE;\n    var _ref5 = (_ADD_OR_SUBTRACT_REGE = ADD_OR_SUBTRACT_REGEX.exec(newExpr)) !== null && _ADD_OR_SUBTRACT_REGE !== void 0 ? _ADD_OR_SUBTRACT_REGE : [],\n      _ref6 = _slicedToArray(_ref5, 4),\n      _leftOperand = _ref6[1],\n      _operator = _ref6[2],\n      _rightOperand = _ref6[3];\n    var _lTs = DecimalCSS.parse(_leftOperand !== null && _leftOperand !== void 0 ? _leftOperand : '');\n    var _rTs = DecimalCSS.parse(_rightOperand !== null && _rightOperand !== void 0 ? _rightOperand : '');\n    var _result = _operator === '+' ? _lTs.add(_rTs) : _lTs.subtract(_rTs);\n    if (_result.isNaN()) {\n      return STR_NAN;\n    }\n    newExpr = newExpr.replace(ADD_OR_SUBTRACT_REGEX, _result.toString());\n  }\n  return newExpr;\n}\nvar PARENTHESES_REGEX = /\\(([^()]*)\\)/;\nfunction calculateParentheses(expr) {\n  var newExpr = expr;\n  while (newExpr.includes('(')) {\n    var _PARENTHESES_REGEX$ex = PARENTHESES_REGEX.exec(newExpr),\n      _PARENTHESES_REGEX$ex2 = _slicedToArray(_PARENTHESES_REGEX$ex, 2),\n      parentheticalExpression = _PARENTHESES_REGEX$ex2[1];\n    newExpr = newExpr.replace(PARENTHESES_REGEX, calculateArithmetic(parentheticalExpression));\n  }\n  return newExpr;\n}\nfunction evaluateExpression(expression) {\n  var newExpr = expression.replace(/\\s+/g, '');\n  newExpr = calculateParentheses(newExpr);\n  newExpr = calculateArithmetic(newExpr);\n  return newExpr;\n}\nexport function safeEvaluateExpression(expression) {\n  try {\n    return evaluateExpression(expression);\n  } catch (e) {\n    /* istanbul ignore next */\n    return STR_NAN;\n  }\n}\nexport function reduceCSSCalc(expression) {\n  var result = safeEvaluateExpression(expression.slice(5, -1));\n  if (result === STR_NAN) {\n    // notify the user\n    return '';\n  }\n  return result;\n}", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_slicedToArray", "arr", "i", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "minLen", "_arrayLikeToArray", "n", "Object", "toString", "call", "slice", "name", "Array", "from", "test", "len", "length", "arr2", "r", "l", "t", "e", "u", "a", "f", "next", "done", "push", "value", "isArray", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "target", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "protoProps", "staticProps", "_toPrimitive", "toPrimitive", "String", "Number", "MULTIPLY_OR_DIVIDE_REGEX", "ADD_OR_SUBTRACT_REGEX", "CSS_LENGTH_UNIT_REGEX", "NUM_SPLIT_REGEX", "CONVERSION_RATES", "cm", "mm", "pt", "pc", "Q", "px", "FIXED_CSS_LENGTH_UNITS", "keys", "STR_NAN", "convertToPx", "unit", "DecimalCSS", "num", "isNaN", "NaN", "includes", "add", "other", "subtract", "multiply", "divide", "concat", "parse", "str", "_NUM_SPLIT_REGEX$exec", "_ref", "exec", "_ref2", "numStr", "parseFloat", "calculateArithmetic", "expr", "newExpr", "_MULTIPLY_OR_DIVIDE_R", "_ref3", "_ref4", "leftOperand", "operator", "rightOperand", "lTs", "rTs", "result", "replace", "_ADD_OR_SUBTRACT_REGE", "_ref5", "_ref6", "_leftOperand", "_operator", "_rightOperand", "_lTs", "_rTs", "_result", "PARENTHESES_REGEX", "calculateParentheses", "_PARENTHESES_REGEX$ex", "_PARENTHESES_REGEX$ex2", "parentheticalExpression", "evaluateExpression", "expression", "safeEvaluateExpression", "reduceCSSCalc"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/recharts/es6/util/ReduceCSSCalc.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar MULTIPLY_OR_DIVIDE_REGEX = /(-?\\d+(?:\\.\\d+)?[a-zA-Z%]*)([*/])(-?\\d+(?:\\.\\d+)?[a-zA-Z%]*)/;\nvar ADD_OR_SUBTRACT_REGEX = /(-?\\d+(?:\\.\\d+)?[a-zA-Z%]*)([+-])(-?\\d+(?:\\.\\d+)?[a-zA-Z%]*)/;\nvar CSS_LENGTH_UNIT_REGEX = /^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/;\nvar NUM_SPLIT_REGEX = /(-?\\d+(?:\\.\\d+)?)([a-zA-Z%]+)?/;\nvar CONVERSION_RATES = {\n  cm: 96 / 2.54,\n  mm: 96 / 25.4,\n  pt: 96 / 72,\n  pc: 96 / 6,\n  \"in\": 96,\n  Q: 96 / (2.54 * 40),\n  px: 1\n};\nvar FIXED_CSS_LENGTH_UNITS = Object.keys(CONVERSION_RATES);\nvar STR_NAN = 'NaN';\nfunction convertToPx(value, unit) {\n  return value * CONVERSION_RATES[unit];\n}\nvar DecimalCSS = /*#__PURE__*/function () {\n  function DecimalCSS(num, unit) {\n    _classCallCheck(this, DecimalCSS);\n    this.num = num;\n    this.unit = unit;\n    this.num = num;\n    this.unit = unit;\n    if (Number.isNaN(num)) {\n      this.unit = '';\n    }\n    if (unit !== '' && !CSS_LENGTH_UNIT_REGEX.test(unit)) {\n      this.num = NaN;\n      this.unit = '';\n    }\n    if (FIXED_CSS_LENGTH_UNITS.includes(unit)) {\n      this.num = convertToPx(num, unit);\n      this.unit = 'px';\n    }\n  }\n  return _createClass(DecimalCSS, [{\n    key: \"add\",\n    value: function add(other) {\n      if (this.unit !== other.unit) {\n        return new DecimalCSS(NaN, '');\n      }\n      return new DecimalCSS(this.num + other.num, this.unit);\n    }\n  }, {\n    key: \"subtract\",\n    value: function subtract(other) {\n      if (this.unit !== other.unit) {\n        return new DecimalCSS(NaN, '');\n      }\n      return new DecimalCSS(this.num - other.num, this.unit);\n    }\n  }, {\n    key: \"multiply\",\n    value: function multiply(other) {\n      if (this.unit !== '' && other.unit !== '' && this.unit !== other.unit) {\n        return new DecimalCSS(NaN, '');\n      }\n      return new DecimalCSS(this.num * other.num, this.unit || other.unit);\n    }\n  }, {\n    key: \"divide\",\n    value: function divide(other) {\n      if (this.unit !== '' && other.unit !== '' && this.unit !== other.unit) {\n        return new DecimalCSS(NaN, '');\n      }\n      return new DecimalCSS(this.num / other.num, this.unit || other.unit);\n    }\n  }, {\n    key: \"toString\",\n    value: function toString() {\n      return \"\".concat(this.num).concat(this.unit);\n    }\n  }, {\n    key: \"isNaN\",\n    value: function isNaN() {\n      return Number.isNaN(this.num);\n    }\n  }], [{\n    key: \"parse\",\n    value: function parse(str) {\n      var _NUM_SPLIT_REGEX$exec;\n      var _ref = (_NUM_SPLIT_REGEX$exec = NUM_SPLIT_REGEX.exec(str)) !== null && _NUM_SPLIT_REGEX$exec !== void 0 ? _NUM_SPLIT_REGEX$exec : [],\n        _ref2 = _slicedToArray(_ref, 3),\n        numStr = _ref2[1],\n        unit = _ref2[2];\n      return new DecimalCSS(parseFloat(numStr), unit !== null && unit !== void 0 ? unit : '');\n    }\n  }]);\n}();\nfunction calculateArithmetic(expr) {\n  if (expr.includes(STR_NAN)) {\n    return STR_NAN;\n  }\n  var newExpr = expr;\n  while (newExpr.includes('*') || newExpr.includes('/')) {\n    var _MULTIPLY_OR_DIVIDE_R;\n    var _ref3 = (_MULTIPLY_OR_DIVIDE_R = MULTIPLY_OR_DIVIDE_REGEX.exec(newExpr)) !== null && _MULTIPLY_OR_DIVIDE_R !== void 0 ? _MULTIPLY_OR_DIVIDE_R : [],\n      _ref4 = _slicedToArray(_ref3, 4),\n      leftOperand = _ref4[1],\n      operator = _ref4[2],\n      rightOperand = _ref4[3];\n    var lTs = DecimalCSS.parse(leftOperand !== null && leftOperand !== void 0 ? leftOperand : '');\n    var rTs = DecimalCSS.parse(rightOperand !== null && rightOperand !== void 0 ? rightOperand : '');\n    var result = operator === '*' ? lTs.multiply(rTs) : lTs.divide(rTs);\n    if (result.isNaN()) {\n      return STR_NAN;\n    }\n    newExpr = newExpr.replace(MULTIPLY_OR_DIVIDE_REGEX, result.toString());\n  }\n  while (newExpr.includes('+') || /.-\\d+(?:\\.\\d+)?/.test(newExpr)) {\n    var _ADD_OR_SUBTRACT_REGE;\n    var _ref5 = (_ADD_OR_SUBTRACT_REGE = ADD_OR_SUBTRACT_REGEX.exec(newExpr)) !== null && _ADD_OR_SUBTRACT_REGE !== void 0 ? _ADD_OR_SUBTRACT_REGE : [],\n      _ref6 = _slicedToArray(_ref5, 4),\n      _leftOperand = _ref6[1],\n      _operator = _ref6[2],\n      _rightOperand = _ref6[3];\n    var _lTs = DecimalCSS.parse(_leftOperand !== null && _leftOperand !== void 0 ? _leftOperand : '');\n    var _rTs = DecimalCSS.parse(_rightOperand !== null && _rightOperand !== void 0 ? _rightOperand : '');\n    var _result = _operator === '+' ? _lTs.add(_rTs) : _lTs.subtract(_rTs);\n    if (_result.isNaN()) {\n      return STR_NAN;\n    }\n    newExpr = newExpr.replace(ADD_OR_SUBTRACT_REGEX, _result.toString());\n  }\n  return newExpr;\n}\nvar PARENTHESES_REGEX = /\\(([^()]*)\\)/;\nfunction calculateParentheses(expr) {\n  var newExpr = expr;\n  while (newExpr.includes('(')) {\n    var _PARENTHESES_REGEX$ex = PARENTHESES_REGEX.exec(newExpr),\n      _PARENTHESES_REGEX$ex2 = _slicedToArray(_PARENTHESES_REGEX$ex, 2),\n      parentheticalExpression = _PARENTHESES_REGEX$ex2[1];\n    newExpr = newExpr.replace(PARENTHESES_REGEX, calculateArithmetic(parentheticalExpression));\n  }\n  return newExpr;\n}\nfunction evaluateExpression(expression) {\n  var newExpr = expression.replace(/\\s+/g, '');\n  newExpr = calculateParentheses(newExpr);\n  newExpr = calculateArithmetic(newExpr);\n  return newExpr;\n}\nexport function safeEvaluateExpression(expression) {\n  try {\n    return evaluateExpression(expression);\n  } catch (e) {\n    /* istanbul ignore next */\n    return STR_NAN;\n  }\n}\nexport function reduceCSSCalc(expression) {\n  var result = safeEvaluateExpression(expression.slice(5, -1));\n  if (result === STR_NAN) {\n    // notify the user\n    return '';\n  }\n  return result;\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,cAAcA,CAACC,GAAG,EAAEC,CAAC,EAAE;EAAE,OAAOC,eAAe,CAACF,GAAG,CAAC,IAAIG,qBAAqB,CAACH,GAAG,EAAEC,CAAC,CAAC,IAAIG,2BAA2B,CAACJ,GAAG,EAAEC,CAAC,CAAC,IAAII,gBAAgB,CAAC,CAAC;AAAE;AAC7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAChM,SAASF,2BAA2BA,CAACV,CAAC,EAAEa,MAAM,EAAE;EAAE,IAAI,CAACb,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOc,iBAAiB,CAACd,CAAC,EAAEa,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACZ,SAAS,CAACa,QAAQ,CAACC,IAAI,CAAClB,CAAC,CAAC,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIf,CAAC,CAACG,WAAW,EAAEY,CAAC,GAAGf,CAAC,CAACG,WAAW,CAACiB,IAAI;EAAE,IAAIL,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOM,KAAK,CAACC,IAAI,CAACtB,CAAC,CAAC;EAAE,IAAIe,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACd,CAAC,EAAEa,MAAM,CAAC;AAAE;AAC/Z,SAASC,iBAAiBA,CAACR,GAAG,EAAEkB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGlB,GAAG,CAACmB,MAAM,EAAED,GAAG,GAAGlB,GAAG,CAACmB,MAAM;EAAE,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEmB,IAAI,GAAG,IAAIL,KAAK,CAACG,GAAG,CAAC,EAAEjB,CAAC,GAAGiB,GAAG,EAAEjB,CAAC,EAAE,EAAEmB,IAAI,CAACnB,CAAC,CAAC,GAAGD,GAAG,CAACC,CAAC,CAAC;EAAE,OAAOmB,IAAI;AAAE;AAClL,SAASjB,qBAAqBA,CAACkB,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAG,IAAI,IAAIF,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAO1B,MAAM,IAAI0B,CAAC,CAAC1B,MAAM,CAACC,QAAQ,CAAC,IAAIyB,CAAC,CAAC,YAAY,CAAC;EAAE,IAAI,IAAI,IAAIE,CAAC,EAAE;IAAE,IAAIC,CAAC;MAAEf,CAAC;MAAER,CAAC;MAAEwB,CAAC;MAAEC,CAAC,GAAG,EAAE;MAAEC,CAAC,GAAG,CAAC,CAAC;MAAEjC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAI;MAAE,IAAIO,CAAC,GAAG,CAACsB,CAAC,GAAGA,CAAC,CAACX,IAAI,CAACS,CAAC,CAAC,EAAEO,IAAI,EAAE,CAAC,KAAKN,CAAC,EAAE;QAAE,IAAIZ,MAAM,CAACa,CAAC,CAAC,KAAKA,CAAC,EAAE;QAAQI,CAAC,GAAG,CAAC,CAAC;MAAE,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACH,CAAC,GAAGvB,CAAC,CAACW,IAAI,CAACW,CAAC,CAAC,EAAEM,IAAI,CAAC,KAAKH,CAAC,CAACI,IAAI,CAACN,CAAC,CAACO,KAAK,CAAC,EAAEL,CAAC,CAACP,MAAM,KAAKG,CAAC,CAAC,EAAEK,CAAC,GAAG,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,OAAON,CAAC,EAAE;MAAE3B,CAAC,GAAG,CAAC,CAAC,EAAEe,CAAC,GAAGY,CAAC;IAAE,CAAC,SAAS;MAAE,IAAI;QAAE,IAAI,CAACM,CAAC,IAAI,IAAI,IAAIJ,CAAC,CAAC,QAAQ,CAAC,KAAKE,CAAC,GAAGF,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEb,MAAM,CAACe,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MAAQ,CAAC,SAAS;QAAE,IAAI/B,CAAC,EAAE,MAAMe,CAAC;MAAE;IAAE;IAAE,OAAOiB,CAAC;EAAE;AAAE;AACzhB,SAASxB,eAAeA,CAACF,GAAG,EAAE;EAAE,IAAIe,KAAK,CAACiB,OAAO,CAAChC,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AACpE,SAASiC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAI7B,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAAS8B,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,KAAK,CAACnB,MAAM,EAAElB,CAAC,EAAE,EAAE;IAAE,IAAIsC,UAAU,GAAGD,KAAK,CAACrC,CAAC,CAAC;IAAEsC,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEhC,MAAM,CAACiC,cAAc,CAACN,MAAM,EAAEO,cAAc,CAACL,UAAU,CAACM,GAAG,CAAC,EAAEN,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASO,YAAYA,CAACX,WAAW,EAAEY,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEX,iBAAiB,CAACD,WAAW,CAACrC,SAAS,EAAEiD,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEZ,iBAAiB,CAACD,WAAW,EAAEa,WAAW,CAAC;EAAEtC,MAAM,CAACiC,cAAc,CAACR,WAAW,EAAE,WAAW,EAAE;IAAEO,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOP,WAAW;AAAE;AAC5R,SAASS,cAAcA,CAACrB,CAAC,EAAE;EAAE,IAAItB,CAAC,GAAGgD,YAAY,CAAC1B,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI9B,OAAO,CAACQ,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASgD,YAAYA,CAAC1B,CAAC,EAAEF,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI5B,OAAO,CAAC8B,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIC,CAAC,GAAGD,CAAC,CAAC5B,MAAM,CAACuD,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK1B,CAAC,EAAE;IAAE,IAAIvB,CAAC,GAAGuB,CAAC,CAACZ,IAAI,CAACW,CAAC,EAAEF,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI5B,OAAO,CAACQ,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKe,CAAC,GAAG8B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AAC3T,IAAI8B,wBAAwB,GAAG,8DAA8D;AAC7F,IAAIC,qBAAqB,GAAG,8DAA8D;AAC1F,IAAIC,qBAAqB,GAAG,sDAAsD;AAClF,IAAIC,eAAe,GAAG,gCAAgC;AACtD,IAAIC,gBAAgB,GAAG;EACrBC,EAAE,EAAE,EAAE,GAAG,IAAI;EACbC,EAAE,EAAE,EAAE,GAAG,IAAI;EACbC,EAAE,EAAE,EAAE,GAAG,EAAE;EACXC,EAAE,EAAE,EAAE,GAAG,CAAC;EACV,IAAI,EAAE,EAAE;EACRC,CAAC,EAAE,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;EACnBC,EAAE,EAAE;AACN,CAAC;AACD,IAAIC,sBAAsB,GAAGtD,MAAM,CAACuD,IAAI,CAACR,gBAAgB,CAAC;AAC1D,IAAIS,OAAO,GAAG,KAAK;AACnB,SAASC,WAAWA,CAACpC,KAAK,EAAEqC,IAAI,EAAE;EAChC,OAAOrC,KAAK,GAAG0B,gBAAgB,CAACW,IAAI,CAAC;AACvC;AACA,IAAIC,UAAU,GAAG,aAAa,YAAY;EACxC,SAASA,UAAUA,CAACC,GAAG,EAAEF,IAAI,EAAE;IAC7BnC,eAAe,CAAC,IAAI,EAAEoC,UAAU,CAAC;IACjC,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,GAAG,GAAGA,GAAG;IACd,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAIhB,MAAM,CAACmB,KAAK,CAACD,GAAG,CAAC,EAAE;MACrB,IAAI,CAACF,IAAI,GAAG,EAAE;IAChB;IACA,IAAIA,IAAI,KAAK,EAAE,IAAI,CAACb,qBAAqB,CAACtC,IAAI,CAACmD,IAAI,CAAC,EAAE;MACpD,IAAI,CAACE,GAAG,GAAGE,GAAG;MACd,IAAI,CAACJ,IAAI,GAAG,EAAE;IAChB;IACA,IAAIJ,sBAAsB,CAACS,QAAQ,CAACL,IAAI,CAAC,EAAE;MACzC,IAAI,CAACE,GAAG,GAAGH,WAAW,CAACG,GAAG,EAAEF,IAAI,CAAC;MACjC,IAAI,CAACA,IAAI,GAAG,IAAI;IAClB;EACF;EACA,OAAOtB,YAAY,CAACuB,UAAU,EAAE,CAAC;IAC/BxB,GAAG,EAAE,KAAK;IACVd,KAAK,EAAE,SAAS2C,GAAGA,CAACC,KAAK,EAAE;MACzB,IAAI,IAAI,CAACP,IAAI,KAAKO,KAAK,CAACP,IAAI,EAAE;QAC5B,OAAO,IAAIC,UAAU,CAACG,GAAG,EAAE,EAAE,CAAC;MAChC;MACA,OAAO,IAAIH,UAAU,CAAC,IAAI,CAACC,GAAG,GAAGK,KAAK,CAACL,GAAG,EAAE,IAAI,CAACF,IAAI,CAAC;IACxD;EACF,CAAC,EAAE;IACDvB,GAAG,EAAE,UAAU;IACfd,KAAK,EAAE,SAAS6C,QAAQA,CAACD,KAAK,EAAE;MAC9B,IAAI,IAAI,CAACP,IAAI,KAAKO,KAAK,CAACP,IAAI,EAAE;QAC5B,OAAO,IAAIC,UAAU,CAACG,GAAG,EAAE,EAAE,CAAC;MAChC;MACA,OAAO,IAAIH,UAAU,CAAC,IAAI,CAACC,GAAG,GAAGK,KAAK,CAACL,GAAG,EAAE,IAAI,CAACF,IAAI,CAAC;IACxD;EACF,CAAC,EAAE;IACDvB,GAAG,EAAE,UAAU;IACfd,KAAK,EAAE,SAAS8C,QAAQA,CAACF,KAAK,EAAE;MAC9B,IAAI,IAAI,CAACP,IAAI,KAAK,EAAE,IAAIO,KAAK,CAACP,IAAI,KAAK,EAAE,IAAI,IAAI,CAACA,IAAI,KAAKO,KAAK,CAACP,IAAI,EAAE;QACrE,OAAO,IAAIC,UAAU,CAACG,GAAG,EAAE,EAAE,CAAC;MAChC;MACA,OAAO,IAAIH,UAAU,CAAC,IAAI,CAACC,GAAG,GAAGK,KAAK,CAACL,GAAG,EAAE,IAAI,CAACF,IAAI,IAAIO,KAAK,CAACP,IAAI,CAAC;IACtE;EACF,CAAC,EAAE;IACDvB,GAAG,EAAE,QAAQ;IACbd,KAAK,EAAE,SAAS+C,MAAMA,CAACH,KAAK,EAAE;MAC5B,IAAI,IAAI,CAACP,IAAI,KAAK,EAAE,IAAIO,KAAK,CAACP,IAAI,KAAK,EAAE,IAAI,IAAI,CAACA,IAAI,KAAKO,KAAK,CAACP,IAAI,EAAE;QACrE,OAAO,IAAIC,UAAU,CAACG,GAAG,EAAE,EAAE,CAAC;MAChC;MACA,OAAO,IAAIH,UAAU,CAAC,IAAI,CAACC,GAAG,GAAGK,KAAK,CAACL,GAAG,EAAE,IAAI,CAACF,IAAI,IAAIO,KAAK,CAACP,IAAI,CAAC;IACtE;EACF,CAAC,EAAE;IACDvB,GAAG,EAAE,UAAU;IACfd,KAAK,EAAE,SAASpB,QAAQA,CAAA,EAAG;MACzB,OAAO,EAAE,CAACoE,MAAM,CAAC,IAAI,CAACT,GAAG,CAAC,CAACS,MAAM,CAAC,IAAI,CAACX,IAAI,CAAC;IAC9C;EACF,CAAC,EAAE;IACDvB,GAAG,EAAE,OAAO;IACZd,KAAK,EAAE,SAASwC,KAAKA,CAAA,EAAG;MACtB,OAAOnB,MAAM,CAACmB,KAAK,CAAC,IAAI,CAACD,GAAG,CAAC;IAC/B;EACF,CAAC,CAAC,EAAE,CAAC;IACHzB,GAAG,EAAE,OAAO;IACZd,KAAK,EAAE,SAASiD,KAAKA,CAACC,GAAG,EAAE;MACzB,IAAIC,qBAAqB;MACzB,IAAIC,IAAI,GAAG,CAACD,qBAAqB,GAAG1B,eAAe,CAAC4B,IAAI,CAACH,GAAG,CAAC,MAAM,IAAI,IAAIC,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,EAAE;QACtIG,KAAK,GAAGtF,cAAc,CAACoF,IAAI,EAAE,CAAC,CAAC;QAC/BG,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC;QACjBjB,IAAI,GAAGiB,KAAK,CAAC,CAAC,CAAC;MACjB,OAAO,IAAIhB,UAAU,CAACkB,UAAU,CAACD,MAAM,CAAC,EAAElB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC;IACzF;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACH,SAASoB,mBAAmBA,CAACC,IAAI,EAAE;EACjC,IAAIA,IAAI,CAAChB,QAAQ,CAACP,OAAO,CAAC,EAAE;IAC1B,OAAOA,OAAO;EAChB;EACA,IAAIwB,OAAO,GAAGD,IAAI;EAClB,OAAOC,OAAO,CAACjB,QAAQ,CAAC,GAAG,CAAC,IAAIiB,OAAO,CAACjB,QAAQ,CAAC,GAAG,CAAC,EAAE;IACrD,IAAIkB,qBAAqB;IACzB,IAAIC,KAAK,GAAG,CAACD,qBAAqB,GAAGtC,wBAAwB,CAAC+B,IAAI,CAACM,OAAO,CAAC,MAAM,IAAI,IAAIC,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,EAAE;MACpJE,KAAK,GAAG9F,cAAc,CAAC6F,KAAK,EAAE,CAAC,CAAC;MAChCE,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC;MACtBE,QAAQ,GAAGF,KAAK,CAAC,CAAC,CAAC;MACnBG,YAAY,GAAGH,KAAK,CAAC,CAAC,CAAC;IACzB,IAAII,GAAG,GAAG5B,UAAU,CAACW,KAAK,CAACc,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,EAAE,CAAC;IAC7F,IAAII,GAAG,GAAG7B,UAAU,CAACW,KAAK,CAACgB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,EAAE,CAAC;IAChG,IAAIG,MAAM,GAAGJ,QAAQ,KAAK,GAAG,GAAGE,GAAG,CAACpB,QAAQ,CAACqB,GAAG,CAAC,GAAGD,GAAG,CAACnB,MAAM,CAACoB,GAAG,CAAC;IACnE,IAAIC,MAAM,CAAC5B,KAAK,CAAC,CAAC,EAAE;MAClB,OAAOL,OAAO;IAChB;IACAwB,OAAO,GAAGA,OAAO,CAACU,OAAO,CAAC/C,wBAAwB,EAAE8C,MAAM,CAACxF,QAAQ,CAAC,CAAC,CAAC;EACxE;EACA,OAAO+E,OAAO,CAACjB,QAAQ,CAAC,GAAG,CAAC,IAAI,iBAAiB,CAACxD,IAAI,CAACyE,OAAO,CAAC,EAAE;IAC/D,IAAIW,qBAAqB;IACzB,IAAIC,KAAK,GAAG,CAACD,qBAAqB,GAAG/C,qBAAqB,CAAC8B,IAAI,CAACM,OAAO,CAAC,MAAM,IAAI,IAAIW,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,EAAE;MACjJE,KAAK,GAAGxG,cAAc,CAACuG,KAAK,EAAE,CAAC,CAAC;MAChCE,YAAY,GAAGD,KAAK,CAAC,CAAC,CAAC;MACvBE,SAAS,GAAGF,KAAK,CAAC,CAAC,CAAC;MACpBG,aAAa,GAAGH,KAAK,CAAC,CAAC,CAAC;IAC1B,IAAII,IAAI,GAAGtC,UAAU,CAACW,KAAK,CAACwB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,EAAE,CAAC;IACjG,IAAII,IAAI,GAAGvC,UAAU,CAACW,KAAK,CAAC0B,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,EAAE,CAAC;IACpG,IAAIG,OAAO,GAAGJ,SAAS,KAAK,GAAG,GAAGE,IAAI,CAACjC,GAAG,CAACkC,IAAI,CAAC,GAAGD,IAAI,CAAC/B,QAAQ,CAACgC,IAAI,CAAC;IACtE,IAAIC,OAAO,CAACtC,KAAK,CAAC,CAAC,EAAE;MACnB,OAAOL,OAAO;IAChB;IACAwB,OAAO,GAAGA,OAAO,CAACU,OAAO,CAAC9C,qBAAqB,EAAEuD,OAAO,CAAClG,QAAQ,CAAC,CAAC,CAAC;EACtE;EACA,OAAO+E,OAAO;AAChB;AACA,IAAIoB,iBAAiB,GAAG,cAAc;AACtC,SAASC,oBAAoBA,CAACtB,IAAI,EAAE;EAClC,IAAIC,OAAO,GAAGD,IAAI;EAClB,OAAOC,OAAO,CAACjB,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC5B,IAAIuC,qBAAqB,GAAGF,iBAAiB,CAAC1B,IAAI,CAACM,OAAO,CAAC;MACzDuB,sBAAsB,GAAGlH,cAAc,CAACiH,qBAAqB,EAAE,CAAC,CAAC;MACjEE,uBAAuB,GAAGD,sBAAsB,CAAC,CAAC,CAAC;IACrDvB,OAAO,GAAGA,OAAO,CAACU,OAAO,CAACU,iBAAiB,EAAEtB,mBAAmB,CAAC0B,uBAAuB,CAAC,CAAC;EAC5F;EACA,OAAOxB,OAAO;AAChB;AACA,SAASyB,kBAAkBA,CAACC,UAAU,EAAE;EACtC,IAAI1B,OAAO,GAAG0B,UAAU,CAAChB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;EAC5CV,OAAO,GAAGqB,oBAAoB,CAACrB,OAAO,CAAC;EACvCA,OAAO,GAAGF,mBAAmB,CAACE,OAAO,CAAC;EACtC,OAAOA,OAAO;AAChB;AACA,OAAO,SAAS2B,sBAAsBA,CAACD,UAAU,EAAE;EACjD,IAAI;IACF,OAAOD,kBAAkB,CAACC,UAAU,CAAC;EACvC,CAAC,CAAC,OAAO5F,CAAC,EAAE;IACV;IACA,OAAO0C,OAAO;EAChB;AACF;AACA,OAAO,SAASoD,aAAaA,CAACF,UAAU,EAAE;EACxC,IAAIjB,MAAM,GAAGkB,sBAAsB,CAACD,UAAU,CAACvG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC5D,IAAIsF,MAAM,KAAKjC,OAAO,EAAE;IACtB;IACA,OAAO,EAAE;EACX;EACA,OAAOiC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}