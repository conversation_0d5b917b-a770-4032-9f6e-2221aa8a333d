{"ast": null, "code": "import Animate from './Animate';\nimport { configBezier, configSpring } from './easing';\nimport AnimateGroup from './AnimateGroup';\nexport { configSpring, configBezier, AnimateGroup };\nexport default Animate;", "map": {"version": 3, "names": ["Animate", "config<PERSON><PERSON><PERSON>", "configS<PERSON>ring", "AnimateGroup"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/react-smooth/es6/index.js"], "sourcesContent": ["import Animate from './Animate';\nimport { configBezier, configSpring } from './easing';\nimport AnimateGroup from './AnimateGroup';\nexport { configSpring, configBezier, AnimateGroup };\nexport default Animate;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAC/B,SAASC,YAAY,EAAEC,YAAY,QAAQ,UAAU;AACrD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASD,YAAY,EAAED,YAAY,EAAEE,YAAY;AACjD,eAAeH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}