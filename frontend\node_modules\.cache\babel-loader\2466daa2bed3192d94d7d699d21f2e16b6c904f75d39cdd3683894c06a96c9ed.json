{"ast": null, "code": "/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */\nfunction isKeyable(value) {\n  var type = typeof value;\n  return type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean' ? value !== '__proto__' : value === null;\n}\nmodule.exports = isKeyable;", "map": {"version": 3, "names": ["isKeyable", "value", "type", "module", "exports"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/lodash/_isKeyable.js"], "sourcesContent": ["/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */\nfunction isKeyable(value) {\n  var type = typeof value;\n  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')\n    ? (value !== '__proto__')\n    : (value === null);\n}\n\nmodule.exports = isKeyable;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,KAAK,EAAE;EACxB,IAAIC,IAAI,GAAG,OAAOD,KAAK;EACvB,OAAQC,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,SAAS,GAChFD,KAAK,KAAK,WAAW,GACrBA,KAAK,KAAK,IAAK;AACtB;AAEAE,MAAM,CAACC,OAAO,GAAGJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}