{"ast": null, "code": "var _excluded = [\"shape\", \"activeShape\", \"activeIndex\", \"cornerRadius\"],\n  _excluded2 = [\"value\", \"background\"];\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Render a group of radial bar\n */\nimport React, { PureComponent } from 'react';\nimport clsx from 'clsx';\nimport Animate from 'react-smooth';\nimport isEqual from 'lodash/isEqual';\nimport isFunction from 'lodash/isFunction';\nimport { parseCornerRadius, RadialBarSector } from '../util/RadialBarUtils';\nimport { Layer } from '../container/Layer';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { LabelList } from '../component/LabelList';\nimport { Cell } from '../component/Cell';\nimport { mathSign, interpolateNumber } from '../util/DataUtils';\nimport { getCateCoordinateOfBar, findPositionOfBar, getValueByDataKey, truncateByDomain, getBaseValueOfBar, getTooltipItem } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { polarToCartesian } from '../util/PolarUtils';\n// TODO: Cause of circular dependency. Needs refactoring of functions that need them.\n// import { AngleAxisProps, RadiusAxisProps } from './types';\n\nexport var RadialBar = /*#__PURE__*/function (_PureComponent) {\n  function RadialBar() {\n    var _this;\n    _classCallCheck(this, RadialBar);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, RadialBar, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      isAnimationFinished: false\n    });\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _inherits(RadialBar, _PureComponent);\n  return _createClass(RadialBar, [{\n    key: \"getDeltaAngle\",\n    value: function getDeltaAngle() {\n      var _this$props = this.props,\n        startAngle = _this$props.startAngle,\n        endAngle = _this$props.endAngle;\n      var sign = mathSign(endAngle - startAngle);\n      var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n      return sign * deltaAngle;\n    }\n  }, {\n    key: \"renderSectorsStatically\",\n    value: function renderSectorsStatically(sectors) {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        shape = _this$props2.shape,\n        activeShape = _this$props2.activeShape,\n        activeIndex = _this$props2.activeIndex,\n        cornerRadius = _this$props2.cornerRadius,\n        others = _objectWithoutProperties(_this$props2, _excluded);\n      var baseProps = filterProps(others, false);\n      return sectors.map(function (entry, i) {\n        var isActive = i === activeIndex;\n        var props = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, baseProps), {}, {\n          cornerRadius: parseCornerRadius(cornerRadius)\n        }, entry), adaptEventsOfChild(_this2.props, entry, i)), {}, {\n          className: \"recharts-radial-bar-sector \".concat(entry.className),\n          forceCornerRadius: others.forceCornerRadius,\n          cornerIsExternal: others.cornerIsExternal,\n          isActive: isActive,\n          option: isActive ? activeShape : shape\n        });\n        return /*#__PURE__*/React.createElement(RadialBarSector, _extends({}, props, {\n          key: \"sector-\".concat(i)\n        }));\n      });\n    }\n  }, {\n    key: \"renderSectorsWithAnimation\",\n    value: function renderSectorsWithAnimation() {\n      var _this3 = this;\n      var _this$props3 = this.props,\n        data = _this$props3.data,\n        isAnimationActive = _this$props3.isAnimationActive,\n        animationBegin = _this$props3.animationBegin,\n        animationDuration = _this$props3.animationDuration,\n        animationEasing = _this$props3.animationEasing,\n        animationId = _this$props3.animationId;\n      var prevData = this.state.prevData;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"radialBar-\".concat(animationId),\n        onAnimationStart: this.handleAnimationStart,\n        onAnimationEnd: this.handleAnimationEnd\n      }, function (_ref) {\n        var t = _ref.t;\n        var stepData = data.map(function (entry, index) {\n          var prev = prevData && prevData[index];\n          if (prev) {\n            var interpolatorStartAngle = interpolateNumber(prev.startAngle, entry.startAngle);\n            var interpolatorEndAngle = interpolateNumber(prev.endAngle, entry.endAngle);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              startAngle: interpolatorStartAngle(t),\n              endAngle: interpolatorEndAngle(t)\n            });\n          }\n          var endAngle = entry.endAngle,\n            startAngle = entry.startAngle;\n          var interpolator = interpolateNumber(startAngle, endAngle);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            endAngle: interpolator(t)\n          });\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderSectorsStatically(stepData));\n      });\n    }\n  }, {\n    key: \"renderSectors\",\n    value: function renderSectors() {\n      var _this$props4 = this.props,\n        data = _this$props4.data,\n        isAnimationActive = _this$props4.isAnimationActive;\n      var prevData = this.state.prevData;\n      if (isAnimationActive && data && data.length && (!prevData || !isEqual(prevData, data))) {\n        return this.renderSectorsWithAnimation();\n      }\n      return this.renderSectorsStatically(data);\n    }\n  }, {\n    key: \"renderBackground\",\n    value: function renderBackground(sectors) {\n      var _this4 = this;\n      var cornerRadius = this.props.cornerRadius;\n      var backgroundProps = filterProps(this.props.background, false);\n      return sectors.map(function (entry, i) {\n        var value = entry.value,\n          background = entry.background,\n          rest = _objectWithoutProperties(entry, _excluded2);\n        if (!background) {\n          return null;\n        }\n        var props = _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({\n          cornerRadius: parseCornerRadius(cornerRadius)\n        }, rest), {}, {\n          fill: '#eee'\n        }, background), backgroundProps), adaptEventsOfChild(_this4.props, entry, i)), {}, {\n          index: i,\n          className: clsx('recharts-radial-bar-background-sector', backgroundProps === null || backgroundProps === void 0 ? void 0 : backgroundProps.className),\n          option: background,\n          isActive: false\n        });\n        return /*#__PURE__*/React.createElement(RadialBarSector, _extends({}, props, {\n          key: \"sector-\".concat(i)\n        }));\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props5 = this.props,\n        hide = _this$props5.hide,\n        data = _this$props5.data,\n        className = _this$props5.className,\n        background = _this$props5.background,\n        isAnimationActive = _this$props5.isAnimationActive;\n      if (hide || !data || !data.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var layerClass = clsx('recharts-area', className);\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, background && /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-radial-bar-background\"\n      }, this.renderBackground(data)), /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-radial-bar-sectors\"\n      }, this.renderSectors()), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(_objectSpread({}, this.props), data));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curData: nextProps.data,\n          prevData: prevState.curData\n        };\n      }\n      if (nextProps.data !== prevState.curData) {\n        return {\n          curData: nextProps.data\n        };\n      }\n      return null;\n    }\n  }]);\n}(PureComponent);\n_defineProperty(RadialBar, \"displayName\", 'RadialBar');\n_defineProperty(RadialBar, \"defaultProps\", {\n  angleAxisId: 0,\n  radiusAxisId: 0,\n  minPointSize: 0,\n  hide: false,\n  legendType: 'rect',\n  data: [],\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  forceCornerRadius: false,\n  cornerIsExternal: false\n});\n_defineProperty(RadialBar, \"getComposedData\", function (_ref2) {\n  var item = _ref2.item,\n    props = _ref2.props,\n    radiusAxis = _ref2.radiusAxis,\n    radiusAxisTicks = _ref2.radiusAxisTicks,\n    angleAxis = _ref2.angleAxis,\n    angleAxisTicks = _ref2.angleAxisTicks,\n    displayedData = _ref2.displayedData,\n    dataKey = _ref2.dataKey,\n    stackedData = _ref2.stackedData,\n    barPosition = _ref2.barPosition,\n    bandSize = _ref2.bandSize,\n    dataStartIndex = _ref2.dataStartIndex;\n  var pos = findPositionOfBar(barPosition, item);\n  if (!pos) {\n    return null;\n  }\n  var cx = angleAxis.cx,\n    cy = angleAxis.cy;\n  var layout = props.layout;\n  var _item$props = item.props,\n    children = _item$props.children,\n    minPointSize = _item$props.minPointSize;\n  var numericAxis = layout === 'radial' ? angleAxis : radiusAxis;\n  var stackedDomain = stackedData ? numericAxis.scale.domain() : null;\n  var baseValue = getBaseValueOfBar({\n    numericAxis: numericAxis\n  });\n  var cells = findAllByType(children, Cell);\n  var sectors = displayedData.map(function (entry, index) {\n    var value, innerRadius, outerRadius, startAngle, endAngle, backgroundSector;\n    if (stackedData) {\n      value = truncateByDomain(stackedData[dataStartIndex + index], stackedDomain);\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      }\n    }\n    if (layout === 'radial') {\n      innerRadius = getCateCoordinateOfBar({\n        axis: radiusAxis,\n        ticks: radiusAxisTicks,\n        bandSize: bandSize,\n        offset: pos.offset,\n        entry: entry,\n        index: index\n      });\n      endAngle = angleAxis.scale(value[1]);\n      startAngle = angleAxis.scale(value[0]);\n      outerRadius = innerRadius + pos.size;\n      var deltaAngle = endAngle - startAngle;\n      if (Math.abs(minPointSize) > 0 && Math.abs(deltaAngle) < Math.abs(minPointSize)) {\n        var delta = mathSign(deltaAngle || minPointSize) * (Math.abs(minPointSize) - Math.abs(deltaAngle));\n        endAngle += delta;\n      }\n      backgroundSector = {\n        background: {\n          cx: cx,\n          cy: cy,\n          innerRadius: innerRadius,\n          outerRadius: outerRadius,\n          startAngle: props.startAngle,\n          endAngle: props.endAngle\n        }\n      };\n    } else {\n      innerRadius = radiusAxis.scale(value[0]);\n      outerRadius = radiusAxis.scale(value[1]);\n      startAngle = getCateCoordinateOfBar({\n        axis: angleAxis,\n        ticks: angleAxisTicks,\n        bandSize: bandSize,\n        offset: pos.offset,\n        entry: entry,\n        index: index\n      });\n      endAngle = startAngle + pos.size;\n      var deltaRadius = outerRadius - innerRadius;\n      if (Math.abs(minPointSize) > 0 && Math.abs(deltaRadius) < Math.abs(minPointSize)) {\n        var _delta = mathSign(deltaRadius || minPointSize) * (Math.abs(minPointSize) - Math.abs(deltaRadius));\n        outerRadius += _delta;\n      }\n    }\n    return _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, entry), backgroundSector), {}, {\n      payload: entry,\n      value: stackedData ? value : value[1],\n      cx: cx,\n      cy: cy,\n      innerRadius: innerRadius,\n      outerRadius: outerRadius,\n      startAngle: startAngle,\n      endAngle: endAngle\n    }, cells && cells[index] && cells[index].props), {}, {\n      tooltipPayload: [getTooltipItem(item, entry)],\n      tooltipPosition: polarToCartesian(cx, cy, (innerRadius + outerRadius) / 2, (startAngle + endAngle) / 2)\n    });\n  });\n  return {\n    data: sectors,\n    layout: layout\n  };\n});", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "ownKeys", "e", "r", "t", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_callSuper", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "self", "_assertThisInitialized", "ReferenceError", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "p", "obj", "_toPrimitive", "toPrimitive", "String", "Number", "React", "PureComponent", "clsx", "Animate", "isEqual", "isFunction", "parseCornerRadius", "RadialBarSector", "Layer", "findAllByType", "filterProps", "Global", "LabelList", "Cell", "mathSign", "interpolateNumber", "getCateCoordinateOfBar", "findPositionOfBar", "getValueByDataKey", "truncateByDomain", "getBaseValueOfBar", "getTooltipItem", "adaptEventsOfChild", "polarToCartesian", "<PERSON><PERSON><PERSON><PERSON>", "_PureComponent", "_this", "_len", "args", "Array", "_key", "concat", "isAnimationFinished", "onAnimationEnd", "setState", "onAnimationStart", "getDeltaAngle", "_this$props", "startAngle", "endAngle", "sign", "deltaAngle", "Math", "min", "abs", "renderSectorsStatically", "sectors", "_this2", "_this$props2", "shape", "activeShape", "activeIndex", "cornerRadius", "others", "baseProps", "map", "entry", "isActive", "className", "forceCornerRadius", "cornerIsExternal", "option", "createElement", "renderSectorsWithAnimation", "_this3", "_this$props3", "data", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "animationId", "prevData", "state", "begin", "duration", "easing", "from", "to", "handleAnimationStart", "handleAnimationEnd", "_ref", "stepData", "index", "prev", "interpolatorStartAngle", "interpolatorEndAngle", "interpolator", "renderSectors", "_this$props4", "renderBackground", "_this4", "backgroundProps", "background", "rest", "fill", "render", "_this$props5", "hide", "layerClass", "renderCallByParent", "getDerivedStateFromProps", "nextProps", "prevState", "prevAnimationId", "curData", "angleAxisId", "radiusAxisId", "minPointSize", "legendType", "isSsr", "_ref2", "item", "radiusAxis", "radiusAxisTicks", "angleAxis", "angleAxisTicks", "displayedData", "dataKey", "stackedData", "barPosition", "bandSize", "dataStartIndex", "pos", "cx", "cy", "layout", "_item$props", "children", "numericAxis", "stackedDomain", "scale", "domain", "baseValue", "cells", "innerRadius", "outerRadius", "backgroundSector", "isArray", "axis", "ticks", "offset", "size", "delta", "deltaRadius", "_delta", "payload", "tooltipPayload", "tooltipPosition"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/recharts/es6/polar/RadialBar.js"], "sourcesContent": ["var _excluded = [\"shape\", \"activeShape\", \"activeIndex\", \"cornerRadius\"],\n  _excluded2 = [\"value\", \"background\"];\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Render a group of radial bar\n */\nimport React, { PureComponent } from 'react';\nimport clsx from 'clsx';\nimport Animate from 'react-smooth';\nimport isEqual from 'lodash/isEqual';\nimport isFunction from 'lodash/isFunction';\nimport { parseCornerRadius, RadialBarSector } from '../util/RadialBarUtils';\nimport { Layer } from '../container/Layer';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { LabelList } from '../component/LabelList';\nimport { Cell } from '../component/Cell';\nimport { mathSign, interpolateNumber } from '../util/DataUtils';\nimport { getCateCoordinateOfBar, findPositionOfBar, getValueByDataKey, truncateByDomain, getBaseValueOfBar, getTooltipItem } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { polarToCartesian } from '../util/PolarUtils';\n// TODO: Cause of circular dependency. Needs refactoring of functions that need them.\n// import { AngleAxisProps, RadiusAxisProps } from './types';\n\nexport var RadialBar = /*#__PURE__*/function (_PureComponent) {\n  function RadialBar() {\n    var _this;\n    _classCallCheck(this, RadialBar);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, RadialBar, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      isAnimationFinished: false\n    });\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _inherits(RadialBar, _PureComponent);\n  return _createClass(RadialBar, [{\n    key: \"getDeltaAngle\",\n    value: function getDeltaAngle() {\n      var _this$props = this.props,\n        startAngle = _this$props.startAngle,\n        endAngle = _this$props.endAngle;\n      var sign = mathSign(endAngle - startAngle);\n      var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n      return sign * deltaAngle;\n    }\n  }, {\n    key: \"renderSectorsStatically\",\n    value: function renderSectorsStatically(sectors) {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        shape = _this$props2.shape,\n        activeShape = _this$props2.activeShape,\n        activeIndex = _this$props2.activeIndex,\n        cornerRadius = _this$props2.cornerRadius,\n        others = _objectWithoutProperties(_this$props2, _excluded);\n      var baseProps = filterProps(others, false);\n      return sectors.map(function (entry, i) {\n        var isActive = i === activeIndex;\n        var props = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, baseProps), {}, {\n          cornerRadius: parseCornerRadius(cornerRadius)\n        }, entry), adaptEventsOfChild(_this2.props, entry, i)), {}, {\n          className: \"recharts-radial-bar-sector \".concat(entry.className),\n          forceCornerRadius: others.forceCornerRadius,\n          cornerIsExternal: others.cornerIsExternal,\n          isActive: isActive,\n          option: isActive ? activeShape : shape\n        });\n        return /*#__PURE__*/React.createElement(RadialBarSector, _extends({}, props, {\n          key: \"sector-\".concat(i)\n        }));\n      });\n    }\n  }, {\n    key: \"renderSectorsWithAnimation\",\n    value: function renderSectorsWithAnimation() {\n      var _this3 = this;\n      var _this$props3 = this.props,\n        data = _this$props3.data,\n        isAnimationActive = _this$props3.isAnimationActive,\n        animationBegin = _this$props3.animationBegin,\n        animationDuration = _this$props3.animationDuration,\n        animationEasing = _this$props3.animationEasing,\n        animationId = _this$props3.animationId;\n      var prevData = this.state.prevData;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"radialBar-\".concat(animationId),\n        onAnimationStart: this.handleAnimationStart,\n        onAnimationEnd: this.handleAnimationEnd\n      }, function (_ref) {\n        var t = _ref.t;\n        var stepData = data.map(function (entry, index) {\n          var prev = prevData && prevData[index];\n          if (prev) {\n            var interpolatorStartAngle = interpolateNumber(prev.startAngle, entry.startAngle);\n            var interpolatorEndAngle = interpolateNumber(prev.endAngle, entry.endAngle);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              startAngle: interpolatorStartAngle(t),\n              endAngle: interpolatorEndAngle(t)\n            });\n          }\n          var endAngle = entry.endAngle,\n            startAngle = entry.startAngle;\n          var interpolator = interpolateNumber(startAngle, endAngle);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            endAngle: interpolator(t)\n          });\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderSectorsStatically(stepData));\n      });\n    }\n  }, {\n    key: \"renderSectors\",\n    value: function renderSectors() {\n      var _this$props4 = this.props,\n        data = _this$props4.data,\n        isAnimationActive = _this$props4.isAnimationActive;\n      var prevData = this.state.prevData;\n      if (isAnimationActive && data && data.length && (!prevData || !isEqual(prevData, data))) {\n        return this.renderSectorsWithAnimation();\n      }\n      return this.renderSectorsStatically(data);\n    }\n  }, {\n    key: \"renderBackground\",\n    value: function renderBackground(sectors) {\n      var _this4 = this;\n      var cornerRadius = this.props.cornerRadius;\n      var backgroundProps = filterProps(this.props.background, false);\n      return sectors.map(function (entry, i) {\n        var value = entry.value,\n          background = entry.background,\n          rest = _objectWithoutProperties(entry, _excluded2);\n        if (!background) {\n          return null;\n        }\n        var props = _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({\n          cornerRadius: parseCornerRadius(cornerRadius)\n        }, rest), {}, {\n          fill: '#eee'\n        }, background), backgroundProps), adaptEventsOfChild(_this4.props, entry, i)), {}, {\n          index: i,\n          className: clsx('recharts-radial-bar-background-sector', backgroundProps === null || backgroundProps === void 0 ? void 0 : backgroundProps.className),\n          option: background,\n          isActive: false\n        });\n        return /*#__PURE__*/React.createElement(RadialBarSector, _extends({}, props, {\n          key: \"sector-\".concat(i)\n        }));\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props5 = this.props,\n        hide = _this$props5.hide,\n        data = _this$props5.data,\n        className = _this$props5.className,\n        background = _this$props5.background,\n        isAnimationActive = _this$props5.isAnimationActive;\n      if (hide || !data || !data.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var layerClass = clsx('recharts-area', className);\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, background && /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-radial-bar-background\"\n      }, this.renderBackground(data)), /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-radial-bar-sectors\"\n      }, this.renderSectors()), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(_objectSpread({}, this.props), data));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curData: nextProps.data,\n          prevData: prevState.curData\n        };\n      }\n      if (nextProps.data !== prevState.curData) {\n        return {\n          curData: nextProps.data\n        };\n      }\n      return null;\n    }\n  }]);\n}(PureComponent);\n_defineProperty(RadialBar, \"displayName\", 'RadialBar');\n_defineProperty(RadialBar, \"defaultProps\", {\n  angleAxisId: 0,\n  radiusAxisId: 0,\n  minPointSize: 0,\n  hide: false,\n  legendType: 'rect',\n  data: [],\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  forceCornerRadius: false,\n  cornerIsExternal: false\n});\n_defineProperty(RadialBar, \"getComposedData\", function (_ref2) {\n  var item = _ref2.item,\n    props = _ref2.props,\n    radiusAxis = _ref2.radiusAxis,\n    radiusAxisTicks = _ref2.radiusAxisTicks,\n    angleAxis = _ref2.angleAxis,\n    angleAxisTicks = _ref2.angleAxisTicks,\n    displayedData = _ref2.displayedData,\n    dataKey = _ref2.dataKey,\n    stackedData = _ref2.stackedData,\n    barPosition = _ref2.barPosition,\n    bandSize = _ref2.bandSize,\n    dataStartIndex = _ref2.dataStartIndex;\n  var pos = findPositionOfBar(barPosition, item);\n  if (!pos) {\n    return null;\n  }\n  var cx = angleAxis.cx,\n    cy = angleAxis.cy;\n  var layout = props.layout;\n  var _item$props = item.props,\n    children = _item$props.children,\n    minPointSize = _item$props.minPointSize;\n  var numericAxis = layout === 'radial' ? angleAxis : radiusAxis;\n  var stackedDomain = stackedData ? numericAxis.scale.domain() : null;\n  var baseValue = getBaseValueOfBar({\n    numericAxis: numericAxis\n  });\n  var cells = findAllByType(children, Cell);\n  var sectors = displayedData.map(function (entry, index) {\n    var value, innerRadius, outerRadius, startAngle, endAngle, backgroundSector;\n    if (stackedData) {\n      value = truncateByDomain(stackedData[dataStartIndex + index], stackedDomain);\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      }\n    }\n    if (layout === 'radial') {\n      innerRadius = getCateCoordinateOfBar({\n        axis: radiusAxis,\n        ticks: radiusAxisTicks,\n        bandSize: bandSize,\n        offset: pos.offset,\n        entry: entry,\n        index: index\n      });\n      endAngle = angleAxis.scale(value[1]);\n      startAngle = angleAxis.scale(value[0]);\n      outerRadius = innerRadius + pos.size;\n      var deltaAngle = endAngle - startAngle;\n      if (Math.abs(minPointSize) > 0 && Math.abs(deltaAngle) < Math.abs(minPointSize)) {\n        var delta = mathSign(deltaAngle || minPointSize) * (Math.abs(minPointSize) - Math.abs(deltaAngle));\n        endAngle += delta;\n      }\n      backgroundSector = {\n        background: {\n          cx: cx,\n          cy: cy,\n          innerRadius: innerRadius,\n          outerRadius: outerRadius,\n          startAngle: props.startAngle,\n          endAngle: props.endAngle\n        }\n      };\n    } else {\n      innerRadius = radiusAxis.scale(value[0]);\n      outerRadius = radiusAxis.scale(value[1]);\n      startAngle = getCateCoordinateOfBar({\n        axis: angleAxis,\n        ticks: angleAxisTicks,\n        bandSize: bandSize,\n        offset: pos.offset,\n        entry: entry,\n        index: index\n      });\n      endAngle = startAngle + pos.size;\n      var deltaRadius = outerRadius - innerRadius;\n      if (Math.abs(minPointSize) > 0 && Math.abs(deltaRadius) < Math.abs(minPointSize)) {\n        var _delta = mathSign(deltaRadius || minPointSize) * (Math.abs(minPointSize) - Math.abs(deltaRadius));\n        outerRadius += _delta;\n      }\n    }\n    return _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, entry), backgroundSector), {}, {\n      payload: entry,\n      value: stackedData ? value : value[1],\n      cx: cx,\n      cy: cy,\n      innerRadius: innerRadius,\n      outerRadius: outerRadius,\n      startAngle: startAngle,\n      endAngle: endAngle\n    }, cells && cells[index] && cells[index].props), {}, {\n      tooltipPayload: [getTooltipItem(item, entry)],\n      tooltipPosition: polarToCartesian(cx, cy, (innerRadius + outerRadius) / 2, (startAngle + endAngle) / 2)\n    });\n  });\n  return {\n    data: sectors,\n    layout: layout\n  };\n});"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC;EACrEC,UAAU,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC;AACtC,SAASC,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGf,MAAM,CAACgB,IAAI,CAACH,CAAC,CAAC;EAAE,IAAIb,MAAM,CAACiB,qBAAqB,EAAE;IAAE,IAAIvB,CAAC,GAAGM,MAAM,CAACiB,qBAAqB,CAACJ,CAAC,CAAC;IAAEC,CAAC,KAAKpB,CAAC,GAAGA,CAAC,CAACwB,MAAM,CAAC,UAAUJ,CAAC,EAAE;MAAE,OAAOd,MAAM,CAACmB,wBAAwB,CAACN,CAAC,EAAEC,CAAC,CAAC,CAACM,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEL,CAAC,CAACM,IAAI,CAACV,KAAK,CAACI,CAAC,EAAErB,CAAC,CAAC;EAAE;EAAE,OAAOqB,CAAC;AAAE;AAC9P,SAASO,aAAaA,CAACT,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,CAACC,MAAM,EAAEQ,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIV,SAAS,CAACS,CAAC,CAAC,GAAGT,SAAS,CAACS,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACZ,MAAM,CAACe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACQ,OAAO,CAAC,UAAUT,CAAC,EAAE;MAAEU,eAAe,CAACX,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGd,MAAM,CAACyB,yBAAyB,GAAGzB,MAAM,CAAC0B,gBAAgB,CAACb,CAAC,EAAEb,MAAM,CAACyB,yBAAyB,CAACV,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACZ,MAAM,CAACe,CAAC,CAAC,CAAC,CAACQ,OAAO,CAAC,UAAUT,CAAC,EAAE;MAAEd,MAAM,CAAC2B,cAAc,CAACd,CAAC,EAAEC,CAAC,EAAEd,MAAM,CAACmB,wBAAwB,CAACJ,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASe,wBAAwBA,CAACrB,MAAM,EAAEsB,QAAQ,EAAE;EAAE,IAAItB,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG2B,6BAA6B,CAACvB,MAAM,EAAEsB,QAAQ,CAAC;EAAE,IAAIrB,GAAG,EAAEJ,CAAC;EAAE,IAAIJ,MAAM,CAACiB,qBAAqB,EAAE;IAAE,IAAIc,gBAAgB,GAAG/B,MAAM,CAACiB,qBAAqB,CAACV,MAAM,CAAC;IAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,gBAAgB,CAACzB,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAEI,GAAG,GAAGuB,gBAAgB,CAAC3B,CAAC,CAAC;MAAE,IAAIyB,QAAQ,CAACG,OAAO,CAACxB,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACR,MAAM,CAACF,SAAS,CAACmC,oBAAoB,CAACvB,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AAC3e,SAAS2B,6BAA6BA,CAACvB,MAAM,EAAEsB,QAAQ,EAAE;EAAE,IAAItB,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIK,GAAG,IAAID,MAAM,EAAE;IAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAE,IAAIqB,QAAQ,CAACG,OAAO,CAACxB,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AACtR,SAAS+B,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACnC,MAAM,EAAEoC,KAAK,EAAE;EAAE,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,KAAK,CAACjC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIoC,UAAU,GAAGD,KAAK,CAACnC,CAAC,CAAC;IAAEoC,UAAU,CAACpB,UAAU,GAAGoB,UAAU,CAACpB,UAAU,IAAI,KAAK;IAAEoB,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAE1C,MAAM,CAAC2B,cAAc,CAACxB,MAAM,EAAEwC,cAAc,CAACH,UAAU,CAAChC,GAAG,CAAC,EAAEgC,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAACtC,SAAS,EAAE+C,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAE9C,MAAM,CAAC2B,cAAc,CAACS,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,UAAUA,CAAChC,CAAC,EAAErB,CAAC,EAAEmB,CAAC,EAAE;EAAE,OAAOnB,CAAC,GAAGsD,eAAe,CAACtD,CAAC,CAAC,EAAEuD,0BAA0B,CAAClC,CAAC,EAAEmC,yBAAyB,CAAC,CAAC,GAAGC,OAAO,CAACC,SAAS,CAAC1D,CAAC,EAAEmB,CAAC,IAAI,EAAE,EAAEmC,eAAe,CAACjC,CAAC,CAAC,CAAClB,WAAW,CAAC,GAAGH,CAAC,CAACiB,KAAK,CAACI,CAAC,EAAEF,CAAC,CAAC,CAAC;AAAE;AAC1M,SAASoC,0BAA0BA,CAACI,IAAI,EAAE3C,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAI2B,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiB,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI;IAAE,IAAInC,CAAC,GAAG,CAACyC,OAAO,CAAC1D,SAAS,CAAC2D,OAAO,CAAC/C,IAAI,CAACyC,OAAO,CAACC,SAAS,CAACI,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,OAAOzC,CAAC,EAAE,CAAC;EAAE,OAAO,CAACmC,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IAAE,OAAO,CAAC,CAACnC,CAAC;EAAE,CAAC,EAAE,CAAC;AAAE;AAClP,SAASiC,eAAeA,CAACtD,CAAC,EAAE;EAAEsD,eAAe,GAAGhD,MAAM,CAAC0D,cAAc,GAAG1D,MAAM,CAAC2D,cAAc,CAACzD,IAAI,CAAC,CAAC,GAAG,SAAS8C,eAAeA,CAACtD,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACkE,SAAS,IAAI5D,MAAM,CAAC2D,cAAc,CAACjE,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOsD,eAAe,CAACtD,CAAC,CAAC;AAAE;AACnN,SAASmE,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAI1B,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEyB,QAAQ,CAAChE,SAAS,GAAGE,MAAM,CAACgE,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACjE,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEoE,KAAK,EAAEH,QAAQ;MAAEpB,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEzC,MAAM,CAAC2B,cAAc,CAACmC,QAAQ,EAAE,WAAW,EAAE;IAAEpB,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIqB,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACxE,CAAC,EAAEyE,CAAC,EAAE;EAAED,eAAe,GAAGlE,MAAM,CAAC0D,cAAc,GAAG1D,MAAM,CAAC0D,cAAc,CAACxD,IAAI,CAAC,CAAC,GAAG,SAASgE,eAAeA,CAACxE,CAAC,EAAEyE,CAAC,EAAE;IAAEzE,CAAC,CAACkE,SAAS,GAAGO,CAAC;IAAE,OAAOzE,CAAC;EAAE,CAAC;EAAE,OAAOwE,eAAe,CAACxE,CAAC,EAAEyE,CAAC,CAAC;AAAE;AACvM,SAAS3C,eAAeA,CAAC4C,GAAG,EAAE5D,GAAG,EAAEyD,KAAK,EAAE;EAAEzD,GAAG,GAAGmC,cAAc,CAACnC,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAI4D,GAAG,EAAE;IAAEpE,MAAM,CAAC2B,cAAc,CAACyC,GAAG,EAAE5D,GAAG,EAAE;MAAEyD,KAAK,EAAEA,KAAK;MAAE7C,UAAU,EAAE,IAAI;MAAEqB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE0B,GAAG,CAAC5D,GAAG,CAAC,GAAGyD,KAAK;EAAE;EAAE,OAAOG,GAAG;AAAE;AAC3O,SAASzB,cAAcA,CAAC5B,CAAC,EAAE;EAAE,IAAIX,CAAC,GAAGiE,YAAY,CAACtD,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAItB,OAAO,CAACW,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASiE,YAAYA,CAACtD,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIrB,OAAO,CAACsB,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACpB,MAAM,CAAC2E,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKzD,CAAC,EAAE;IAAE,IAAIT,CAAC,GAAGS,CAAC,CAACH,IAAI,CAACK,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIrB,OAAO,CAACW,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIiC,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKvB,CAAC,GAAGyD,MAAM,GAAGC,MAAM,EAAEzD,CAAC,CAAC;AAAE;AAC3T;AACA;AACA;AACA,OAAO0D,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,OAAO,MAAM,gBAAgB;AACpC,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,SAASC,iBAAiB,EAAEC,eAAe,QAAQ,wBAAwB;AAC3E,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,aAAa,EAAEC,WAAW,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,QAAQ,EAAEC,iBAAiB,QAAQ,mBAAmB;AAC/D,SAASC,sBAAsB,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,cAAc,QAAQ,oBAAoB;AACtJ,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD;AACA;;AAEA,OAAO,IAAIC,SAAS,GAAG,aAAa,UAAUC,cAAc,EAAE;EAC5D,SAASD,SAASA,CAAA,EAAG;IACnB,IAAIE,KAAK;IACTjE,eAAe,CAAC,IAAI,EAAE+D,SAAS,CAAC;IAChC,KAAK,IAAIG,IAAI,GAAG/F,SAAS,CAACC,MAAM,EAAE+F,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGlG,SAAS,CAACkG,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAGpD,UAAU,CAAC,IAAI,EAAEkD,SAAS,EAAE,EAAE,CAACO,MAAM,CAACH,IAAI,CAAC,CAAC;IACpD7E,eAAe,CAAC2E,KAAK,EAAE,OAAO,EAAE;MAC9BM,mBAAmB,EAAE;IACvB,CAAC,CAAC;IACFjF,eAAe,CAAC2E,KAAK,EAAE,oBAAoB,EAAE,YAAY;MACvD,IAAIO,cAAc,GAAGP,KAAK,CAAC5D,KAAK,CAACmE,cAAc;MAC/CP,KAAK,CAACQ,QAAQ,CAAC;QACbF,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAI3B,UAAU,CAAC4B,cAAc,CAAC,EAAE;QAC9BA,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACFlF,eAAe,CAAC2E,KAAK,EAAE,sBAAsB,EAAE,YAAY;MACzD,IAAIS,gBAAgB,GAAGT,KAAK,CAAC5D,KAAK,CAACqE,gBAAgB;MACnDT,KAAK,CAACQ,QAAQ,CAAC;QACbF,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAI3B,UAAU,CAAC8B,gBAAgB,CAAC,EAAE;QAChCA,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IACF,OAAOT,KAAK;EACd;EACAtC,SAAS,CAACoC,SAAS,EAAEC,cAAc,CAAC;EACpC,OAAOtD,YAAY,CAACqD,SAAS,EAAE,CAAC;IAC9BzF,GAAG,EAAE,eAAe;IACpByD,KAAK,EAAE,SAAS4C,aAAaA,CAAA,EAAG;MAC9B,IAAIC,WAAW,GAAG,IAAI,CAACvE,KAAK;QAC1BwE,UAAU,GAAGD,WAAW,CAACC,UAAU;QACnCC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;MACjC,IAAIC,IAAI,GAAG1B,QAAQ,CAACyB,QAAQ,GAAGD,UAAU,CAAC;MAC1C,IAAIG,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,QAAQ,GAAGD,UAAU,CAAC,EAAE,GAAG,CAAC;MAC/D,OAAOE,IAAI,GAAGC,UAAU;IAC1B;EACF,CAAC,EAAE;IACD1G,GAAG,EAAE,yBAAyB;IAC9ByD,KAAK,EAAE,SAASqD,uBAAuBA,CAACC,OAAO,EAAE;MAC/C,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAClF,KAAK;QAC3BmF,KAAK,GAAGD,YAAY,CAACC,KAAK;QAC1BC,WAAW,GAAGF,YAAY,CAACE,WAAW;QACtCC,WAAW,GAAGH,YAAY,CAACG,WAAW;QACtCC,YAAY,GAAGJ,YAAY,CAACI,YAAY;QACxCC,MAAM,GAAGlG,wBAAwB,CAAC6F,YAAY,EAAElI,SAAS,CAAC;MAC5D,IAAIwI,SAAS,GAAG5C,WAAW,CAAC2C,MAAM,EAAE,KAAK,CAAC;MAC1C,OAAOP,OAAO,CAACS,GAAG,CAAC,UAAUC,KAAK,EAAE7H,CAAC,EAAE;QACrC,IAAI8H,QAAQ,GAAG9H,CAAC,KAAKwH,WAAW;QAChC,IAAIrF,KAAK,GAAGjB,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyG,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UACtFF,YAAY,EAAE9C,iBAAiB,CAAC8C,YAAY;QAC9C,CAAC,EAAEI,KAAK,CAAC,EAAElC,kBAAkB,CAACyB,MAAM,CAACjF,KAAK,EAAE0F,KAAK,EAAE7H,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAC1D+H,SAAS,EAAE,6BAA6B,CAAC3B,MAAM,CAACyB,KAAK,CAACE,SAAS,CAAC;UAChEC,iBAAiB,EAAEN,MAAM,CAACM,iBAAiB;UAC3CC,gBAAgB,EAAEP,MAAM,CAACO,gBAAgB;UACzCH,QAAQ,EAAEA,QAAQ;UAClBI,MAAM,EAAEJ,QAAQ,GAAGP,WAAW,GAAGD;QACnC,CAAC,CAAC;QACF,OAAO,aAAajD,KAAK,CAAC8D,aAAa,CAACvD,eAAe,EAAEjF,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;UAC3E/B,GAAG,EAAE,SAAS,CAACgG,MAAM,CAACpG,CAAC;QACzB,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDI,GAAG,EAAE,4BAA4B;IACjCyD,KAAK,EAAE,SAASuE,0BAA0BA,CAAA,EAAG;MAC3C,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACnG,KAAK;QAC3BoG,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBC,iBAAiB,GAAGF,YAAY,CAACE,iBAAiB;QAClDC,cAAc,GAAGH,YAAY,CAACG,cAAc;QAC5CC,iBAAiB,GAAGJ,YAAY,CAACI,iBAAiB;QAClDC,eAAe,GAAGL,YAAY,CAACK,eAAe;QAC9CC,WAAW,GAAGN,YAAY,CAACM,WAAW;MACxC,IAAIC,QAAQ,GAAG,IAAI,CAACC,KAAK,CAACD,QAAQ;MAClC,OAAO,aAAaxE,KAAK,CAAC8D,aAAa,CAAC3D,OAAO,EAAE;QAC/CuE,KAAK,EAAEN,cAAc;QACrBO,QAAQ,EAAEN,iBAAiB;QAC3BZ,QAAQ,EAAEU,iBAAiB;QAC3BS,MAAM,EAAEN,eAAe;QACvBO,IAAI,EAAE;UACJvI,CAAC,EAAE;QACL,CAAC;QACDwI,EAAE,EAAE;UACFxI,CAAC,EAAE;QACL,CAAC;QACDP,GAAG,EAAE,YAAY,CAACgG,MAAM,CAACwC,WAAW,CAAC;QACrCpC,gBAAgB,EAAE,IAAI,CAAC4C,oBAAoB;QAC3C9C,cAAc,EAAE,IAAI,CAAC+C;MACvB,CAAC,EAAE,UAAUC,IAAI,EAAE;QACjB,IAAI3I,CAAC,GAAG2I,IAAI,CAAC3I,CAAC;QACd,IAAI4I,QAAQ,GAAGhB,IAAI,CAACX,GAAG,CAAC,UAAUC,KAAK,EAAE2B,KAAK,EAAE;UAC9C,IAAIC,IAAI,GAAGZ,QAAQ,IAAIA,QAAQ,CAACW,KAAK,CAAC;UACtC,IAAIC,IAAI,EAAE;YACR,IAAIC,sBAAsB,GAAGtE,iBAAiB,CAACqE,IAAI,CAAC9C,UAAU,EAAEkB,KAAK,CAAClB,UAAU,CAAC;YACjF,IAAIgD,oBAAoB,GAAGvE,iBAAiB,CAACqE,IAAI,CAAC7C,QAAQ,EAAEiB,KAAK,CAACjB,QAAQ,CAAC;YAC3E,OAAO1F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACjDlB,UAAU,EAAE+C,sBAAsB,CAAC/I,CAAC,CAAC;cACrCiG,QAAQ,EAAE+C,oBAAoB,CAAChJ,CAAC;YAClC,CAAC,CAAC;UACJ;UACA,IAAIiG,QAAQ,GAAGiB,KAAK,CAACjB,QAAQ;YAC3BD,UAAU,GAAGkB,KAAK,CAAClB,UAAU;UAC/B,IAAIiD,YAAY,GAAGxE,iBAAiB,CAACuB,UAAU,EAAEC,QAAQ,CAAC;UAC1D,OAAO1F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACjDjB,QAAQ,EAAEgD,YAAY,CAACjJ,CAAC;UAC1B,CAAC,CAAC;QACJ,CAAC,CAAC;QACF,OAAO,aAAa0D,KAAK,CAAC8D,aAAa,CAACtD,KAAK,EAAE,IAAI,EAAEwD,MAAM,CAACnB,uBAAuB,CAACqC,QAAQ,CAAC,CAAC;MAChG,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDnJ,GAAG,EAAE,eAAe;IACpByD,KAAK,EAAE,SAASgG,aAAaA,CAAA,EAAG;MAC9B,IAAIC,YAAY,GAAG,IAAI,CAAC3H,KAAK;QAC3BoG,IAAI,GAAGuB,YAAY,CAACvB,IAAI;QACxBC,iBAAiB,GAAGsB,YAAY,CAACtB,iBAAiB;MACpD,IAAIK,QAAQ,GAAG,IAAI,CAACC,KAAK,CAACD,QAAQ;MAClC,IAAIL,iBAAiB,IAAID,IAAI,IAAIA,IAAI,CAACrI,MAAM,KAAK,CAAC2I,QAAQ,IAAI,CAACpE,OAAO,CAACoE,QAAQ,EAAEN,IAAI,CAAC,CAAC,EAAE;QACvF,OAAO,IAAI,CAACH,0BAA0B,CAAC,CAAC;MAC1C;MACA,OAAO,IAAI,CAAClB,uBAAuB,CAACqB,IAAI,CAAC;IAC3C;EACF,CAAC,EAAE;IACDnI,GAAG,EAAE,kBAAkB;IACvByD,KAAK,EAAE,SAASkG,gBAAgBA,CAAC5C,OAAO,EAAE;MACxC,IAAI6C,MAAM,GAAG,IAAI;MACjB,IAAIvC,YAAY,GAAG,IAAI,CAACtF,KAAK,CAACsF,YAAY;MAC1C,IAAIwC,eAAe,GAAGlF,WAAW,CAAC,IAAI,CAAC5C,KAAK,CAAC+H,UAAU,EAAE,KAAK,CAAC;MAC/D,OAAO/C,OAAO,CAACS,GAAG,CAAC,UAAUC,KAAK,EAAE7H,CAAC,EAAE;QACrC,IAAI6D,KAAK,GAAGgE,KAAK,CAAChE,KAAK;UACrBqG,UAAU,GAAGrC,KAAK,CAACqC,UAAU;UAC7BC,IAAI,GAAG3I,wBAAwB,CAACqG,KAAK,EAAEzI,UAAU,CAAC;QACpD,IAAI,CAAC8K,UAAU,EAAE;UACf,OAAO,IAAI;QACb;QACA,IAAI/H,KAAK,GAAGjB,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;UAChFuG,YAAY,EAAE9C,iBAAiB,CAAC8C,YAAY;QAC9C,CAAC,EAAE0C,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACZC,IAAI,EAAE;QACR,CAAC,EAAEF,UAAU,CAAC,EAAED,eAAe,CAAC,EAAEtE,kBAAkB,CAACqE,MAAM,CAAC7H,KAAK,EAAE0F,KAAK,EAAE7H,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACjFwJ,KAAK,EAAExJ,CAAC;UACR+H,SAAS,EAAExD,IAAI,CAAC,uCAAuC,EAAE0F,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAClC,SAAS,CAAC;UACrJG,MAAM,EAAEgC,UAAU;UAClBpC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF,OAAO,aAAazD,KAAK,CAAC8D,aAAa,CAACvD,eAAe,EAAEjF,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;UAC3E/B,GAAG,EAAE,SAAS,CAACgG,MAAM,CAACpG,CAAC;QACzB,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDI,GAAG,EAAE,QAAQ;IACbyD,KAAK,EAAE,SAASwG,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAACnI,KAAK;QAC3BoI,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBhC,IAAI,GAAG+B,YAAY,CAAC/B,IAAI;QACxBR,SAAS,GAAGuC,YAAY,CAACvC,SAAS;QAClCmC,UAAU,GAAGI,YAAY,CAACJ,UAAU;QACpC1B,iBAAiB,GAAG8B,YAAY,CAAC9B,iBAAiB;MACpD,IAAI+B,IAAI,IAAI,CAAChC,IAAI,IAAI,CAACA,IAAI,CAACrI,MAAM,EAAE;QACjC,OAAO,IAAI;MACb;MACA,IAAImG,mBAAmB,GAAG,IAAI,CAACyC,KAAK,CAACzC,mBAAmB;MACxD,IAAImE,UAAU,GAAGjG,IAAI,CAAC,eAAe,EAAEwD,SAAS,CAAC;MACjD,OAAO,aAAa1D,KAAK,CAAC8D,aAAa,CAACtD,KAAK,EAAE;QAC7CkD,SAAS,EAAEyC;MACb,CAAC,EAAEN,UAAU,IAAI,aAAa7F,KAAK,CAAC8D,aAAa,CAACtD,KAAK,EAAE;QACvDkD,SAAS,EAAE;MACb,CAAC,EAAE,IAAI,CAACgC,gBAAgB,CAACxB,IAAI,CAAC,CAAC,EAAE,aAAalE,KAAK,CAAC8D,aAAa,CAACtD,KAAK,EAAE;QACvEkD,SAAS,EAAE;MACb,CAAC,EAAE,IAAI,CAAC8B,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAACrB,iBAAiB,IAAInC,mBAAmB,KAAKpB,SAAS,CAACwF,kBAAkB,CAACvJ,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiB,KAAK,CAAC,EAAEoG,IAAI,CAAC,CAAC;IAC7I;EACF,CAAC,CAAC,EAAE,CAAC;IACHnI,GAAG,EAAE,0BAA0B;IAC/ByD,KAAK,EAAE,SAAS6G,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAID,SAAS,CAAC/B,WAAW,KAAKgC,SAAS,CAACC,eAAe,EAAE;QACvD,OAAO;UACLA,eAAe,EAAEF,SAAS,CAAC/B,WAAW;UACtCkC,OAAO,EAAEH,SAAS,CAACpC,IAAI;UACvBM,QAAQ,EAAE+B,SAAS,CAACE;QACtB,CAAC;MACH;MACA,IAAIH,SAAS,CAACpC,IAAI,KAAKqC,SAAS,CAACE,OAAO,EAAE;QACxC,OAAO;UACLA,OAAO,EAAEH,SAAS,CAACpC;QACrB,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAACjE,aAAa,CAAC;AAChBlD,eAAe,CAACyE,SAAS,EAAE,aAAa,EAAE,WAAW,CAAC;AACtDzE,eAAe,CAACyE,SAAS,EAAE,cAAc,EAAE;EACzCkF,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,YAAY,EAAE,CAAC;EACfV,IAAI,EAAE,KAAK;EACXW,UAAU,EAAE,MAAM;EAClB3C,IAAI,EAAE,EAAE;EACRC,iBAAiB,EAAE,CAACxD,MAAM,CAACmG,KAAK;EAChC1C,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,MAAM;EACvBX,iBAAiB,EAAE,KAAK;EACxBC,gBAAgB,EAAE;AACpB,CAAC,CAAC;AACF7G,eAAe,CAACyE,SAAS,EAAE,iBAAiB,EAAE,UAAUuF,KAAK,EAAE;EAC7D,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACnBlJ,KAAK,GAAGiJ,KAAK,CAACjJ,KAAK;IACnBmJ,UAAU,GAAGF,KAAK,CAACE,UAAU;IAC7BC,eAAe,GAAGH,KAAK,CAACG,eAAe;IACvCC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,aAAa,GAAGN,KAAK,CAACM,aAAa;IACnCC,OAAO,GAAGP,KAAK,CAACO,OAAO;IACvBC,WAAW,GAAGR,KAAK,CAACQ,WAAW;IAC/BC,WAAW,GAAGT,KAAK,CAACS,WAAW;IAC/BC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,cAAc,GAAGX,KAAK,CAACW,cAAc;EACvC,IAAIC,GAAG,GAAG1G,iBAAiB,CAACuG,WAAW,EAAER,IAAI,CAAC;EAC9C,IAAI,CAACW,GAAG,EAAE;IACR,OAAO,IAAI;EACb;EACA,IAAIC,EAAE,GAAGT,SAAS,CAACS,EAAE;IACnBC,EAAE,GAAGV,SAAS,CAACU,EAAE;EACnB,IAAIC,MAAM,GAAGhK,KAAK,CAACgK,MAAM;EACzB,IAAIC,WAAW,GAAGf,IAAI,CAAClJ,KAAK;IAC1BkK,QAAQ,GAAGD,WAAW,CAACC,QAAQ;IAC/BpB,YAAY,GAAGmB,WAAW,CAACnB,YAAY;EACzC,IAAIqB,WAAW,GAAGH,MAAM,KAAK,QAAQ,GAAGX,SAAS,GAAGF,UAAU;EAC9D,IAAIiB,aAAa,GAAGX,WAAW,GAAGU,WAAW,CAACE,KAAK,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI;EACnE,IAAIC,SAAS,GAAGjH,iBAAiB,CAAC;IAChC6G,WAAW,EAAEA;EACf,CAAC,CAAC;EACF,IAAIK,KAAK,GAAG7H,aAAa,CAACuH,QAAQ,EAAEnH,IAAI,CAAC;EACzC,IAAIiC,OAAO,GAAGuE,aAAa,CAAC9D,GAAG,CAAC,UAAUC,KAAK,EAAE2B,KAAK,EAAE;IACtD,IAAI3F,KAAK,EAAE+I,WAAW,EAAEC,WAAW,EAAElG,UAAU,EAAEC,QAAQ,EAAEkG,gBAAgB;IAC3E,IAAIlB,WAAW,EAAE;MACf/H,KAAK,GAAG2B,gBAAgB,CAACoG,WAAW,CAACG,cAAc,GAAGvC,KAAK,CAAC,EAAE+C,aAAa,CAAC;IAC9E,CAAC,MAAM;MACL1I,KAAK,GAAG0B,iBAAiB,CAACsC,KAAK,EAAE8D,OAAO,CAAC;MACzC,IAAI,CAACzF,KAAK,CAAC6G,OAAO,CAAClJ,KAAK,CAAC,EAAE;QACzBA,KAAK,GAAG,CAAC6I,SAAS,EAAE7I,KAAK,CAAC;MAC5B;IACF;IACA,IAAIsI,MAAM,KAAK,QAAQ,EAAE;MACvBS,WAAW,GAAGvH,sBAAsB,CAAC;QACnC2H,IAAI,EAAE1B,UAAU;QAChB2B,KAAK,EAAE1B,eAAe;QACtBO,QAAQ,EAAEA,QAAQ;QAClBoB,MAAM,EAAElB,GAAG,CAACkB,MAAM;QAClBrF,KAAK,EAAEA,KAAK;QACZ2B,KAAK,EAAEA;MACT,CAAC,CAAC;MACF5C,QAAQ,GAAG4E,SAAS,CAACgB,KAAK,CAAC3I,KAAK,CAAC,CAAC,CAAC,CAAC;MACpC8C,UAAU,GAAG6E,SAAS,CAACgB,KAAK,CAAC3I,KAAK,CAAC,CAAC,CAAC,CAAC;MACtCgJ,WAAW,GAAGD,WAAW,GAAGZ,GAAG,CAACmB,IAAI;MACpC,IAAIrG,UAAU,GAAGF,QAAQ,GAAGD,UAAU;MACtC,IAAII,IAAI,CAACE,GAAG,CAACgE,YAAY,CAAC,GAAG,CAAC,IAAIlE,IAAI,CAACE,GAAG,CAACH,UAAU,CAAC,GAAGC,IAAI,CAACE,GAAG,CAACgE,YAAY,CAAC,EAAE;QAC/E,IAAImC,KAAK,GAAGjI,QAAQ,CAAC2B,UAAU,IAAImE,YAAY,CAAC,IAAIlE,IAAI,CAACE,GAAG,CAACgE,YAAY,CAAC,GAAGlE,IAAI,CAACE,GAAG,CAACH,UAAU,CAAC,CAAC;QAClGF,QAAQ,IAAIwG,KAAK;MACnB;MACAN,gBAAgB,GAAG;QACjB5C,UAAU,EAAE;UACV+B,EAAE,EAAEA,EAAE;UACNC,EAAE,EAAEA,EAAE;UACNU,WAAW,EAAEA,WAAW;UACxBC,WAAW,EAAEA,WAAW;UACxBlG,UAAU,EAAExE,KAAK,CAACwE,UAAU;UAC5BC,QAAQ,EAAEzE,KAAK,CAACyE;QAClB;MACF,CAAC;IACH,CAAC,MAAM;MACLgG,WAAW,GAAGtB,UAAU,CAACkB,KAAK,CAAC3I,KAAK,CAAC,CAAC,CAAC,CAAC;MACxCgJ,WAAW,GAAGvB,UAAU,CAACkB,KAAK,CAAC3I,KAAK,CAAC,CAAC,CAAC,CAAC;MACxC8C,UAAU,GAAGtB,sBAAsB,CAAC;QAClC2H,IAAI,EAAExB,SAAS;QACfyB,KAAK,EAAExB,cAAc;QACrBK,QAAQ,EAAEA,QAAQ;QAClBoB,MAAM,EAAElB,GAAG,CAACkB,MAAM;QAClBrF,KAAK,EAAEA,KAAK;QACZ2B,KAAK,EAAEA;MACT,CAAC,CAAC;MACF5C,QAAQ,GAAGD,UAAU,GAAGqF,GAAG,CAACmB,IAAI;MAChC,IAAIE,WAAW,GAAGR,WAAW,GAAGD,WAAW;MAC3C,IAAI7F,IAAI,CAACE,GAAG,CAACgE,YAAY,CAAC,GAAG,CAAC,IAAIlE,IAAI,CAACE,GAAG,CAACoG,WAAW,CAAC,GAAGtG,IAAI,CAACE,GAAG,CAACgE,YAAY,CAAC,EAAE;QAChF,IAAIqC,MAAM,GAAGnI,QAAQ,CAACkI,WAAW,IAAIpC,YAAY,CAAC,IAAIlE,IAAI,CAACE,GAAG,CAACgE,YAAY,CAAC,GAAGlE,IAAI,CAACE,GAAG,CAACoG,WAAW,CAAC,CAAC;QACrGR,WAAW,IAAIS,MAAM;MACvB;IACF;IACA,OAAOpM,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2G,KAAK,CAAC,EAAEiF,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;MAChGS,OAAO,EAAE1F,KAAK;MACdhE,KAAK,EAAE+H,WAAW,GAAG/H,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;MACrCoI,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA,EAAE;MACNU,WAAW,EAAEA,WAAW;MACxBC,WAAW,EAAEA,WAAW;MACxBlG,UAAU,EAAEA,UAAU;MACtBC,QAAQ,EAAEA;IACZ,CAAC,EAAE+F,KAAK,IAAIA,KAAK,CAACnD,KAAK,CAAC,IAAImD,KAAK,CAACnD,KAAK,CAAC,CAACrH,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACnDqL,cAAc,EAAE,CAAC9H,cAAc,CAAC2F,IAAI,EAAExD,KAAK,CAAC,CAAC;MAC7C4F,eAAe,EAAE7H,gBAAgB,CAACqG,EAAE,EAAEC,EAAE,EAAE,CAACU,WAAW,GAAGC,WAAW,IAAI,CAAC,EAAE,CAAClG,UAAU,GAAGC,QAAQ,IAAI,CAAC;IACxG,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO;IACL2B,IAAI,EAAEpB,OAAO;IACbgF,MAAM,EAAEA;EACV,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}