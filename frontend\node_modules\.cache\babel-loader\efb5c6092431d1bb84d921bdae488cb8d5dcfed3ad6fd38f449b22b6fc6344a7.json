{"ast": null, "code": "var isStrictComparable = require('./_isStrictComparable'),\n  keys = require('./keys');\n\n/**\n * Gets the property names, values, and compare flags of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the match data of `object`.\n */\nfunction getMatchData(object) {\n  var result = keys(object),\n    length = result.length;\n  while (length--) {\n    var key = result[length],\n      value = object[key];\n    result[length] = [key, value, isStrictComparable(value)];\n  }\n  return result;\n}\nmodule.exports = getMatchData;", "map": {"version": 3, "names": ["isStrictComparable", "require", "keys", "getMatchData", "object", "result", "length", "key", "value", "module", "exports"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/lodash/_getMatchData.js"], "sourcesContent": ["var isStrictComparable = require('./_isStrictComparable'),\n    keys = require('./keys');\n\n/**\n * Gets the property names, values, and compare flags of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the match data of `object`.\n */\nfunction getMatchData(object) {\n  var result = keys(object),\n      length = result.length;\n\n  while (length--) {\n    var key = result[length],\n        value = object[key];\n\n    result[length] = [key, value, isStrictComparable(value)];\n  }\n  return result;\n}\n\nmodule.exports = getMatchData;\n"], "mappings": "AAAA,IAAIA,kBAAkB,GAAGC,OAAO,CAAC,uBAAuB,CAAC;EACrDC,IAAI,GAAGD,OAAO,CAAC,QAAQ,CAAC;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,YAAYA,CAACC,MAAM,EAAE;EAC5B,IAAIC,MAAM,GAAGH,IAAI,CAACE,MAAM,CAAC;IACrBE,MAAM,GAAGD,MAAM,CAACC,MAAM;EAE1B,OAAOA,MAAM,EAAE,EAAE;IACf,IAAIC,GAAG,GAAGF,MAAM,CAACC,MAAM,CAAC;MACpBE,KAAK,GAAGJ,MAAM,CAACG,GAAG,CAAC;IAEvBF,MAAM,CAACC,MAAM,CAAC,GAAG,CAACC,GAAG,EAAEC,KAAK,EAAER,kBAAkB,CAACQ,KAAK,CAAC,CAAC;EAC1D;EACA,OAAOH,MAAM;AACf;AAEAI,MAAM,CAACC,OAAO,GAAGP,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}