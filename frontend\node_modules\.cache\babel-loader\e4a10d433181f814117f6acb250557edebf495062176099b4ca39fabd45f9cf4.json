{"ast": null, "code": "var root = require('./_root');\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\nmodule.exports = coreJsData;", "map": {"version": 3, "names": ["root", "require", "coreJsData", "module", "exports"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/lodash/_coreJsData.js"], "sourcesContent": ["var root = require('./_root');\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\n\nmodule.exports = coreJsData;\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,SAAS,CAAC;;AAE7B;AACA,IAAIC,UAAU,GAAGF,IAAI,CAAC,oBAAoB,CAAC;AAE3CG,MAAM,CAACC,OAAO,GAAGF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}