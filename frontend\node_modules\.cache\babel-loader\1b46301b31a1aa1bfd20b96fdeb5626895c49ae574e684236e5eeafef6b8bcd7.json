{"ast": null, "code": "import { ascending, bisect, quantileSorted as threshold } from \"d3-array\";\nimport { initRange } from \"./init.js\";\nexport default function quantile() {\n  var domain = [],\n    range = [],\n    thresholds = [],\n    unknown;\n  function rescale() {\n    var i = 0,\n      n = Math.max(1, range.length);\n    thresholds = new Array(n - 1);\n    while (++i < n) thresholds[i - 1] = threshold(domain, i / n);\n    return scale;\n  }\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : range[bisect(thresholds, x)];\n  }\n  scale.invertExtent = function (y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN] : [i > 0 ? thresholds[i - 1] : domain[0], i < thresholds.length ? thresholds[i] : domain[domain.length - 1]];\n  };\n  scale.domain = function (_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(ascending);\n    return rescale();\n  };\n  scale.range = function (_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  scale.quantiles = function () {\n    return thresholds.slice();\n  };\n  scale.copy = function () {\n    return quantile().domain(domain).range(range).unknown(unknown);\n  };\n  return initRange.apply(scale, arguments);\n}", "map": {"version": 3, "names": ["ascending", "bisect", "quantileSorted", "threshold", "initRange", "quantile", "domain", "range", "thresholds", "unknown", "rescale", "i", "n", "Math", "max", "length", "Array", "scale", "x", "isNaN", "invertExtent", "y", "indexOf", "NaN", "_", "arguments", "slice", "d", "push", "sort", "from", "quantiles", "copy", "apply"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/d3-scale/src/quantile.js"], "sourcesContent": ["import {ascending, bisect, quantileSorted as threshold} from \"d3-array\";\nimport {initRange} from \"./init.js\";\n\nexport default function quantile() {\n  var domain = [],\n      range = [],\n      thresholds = [],\n      unknown;\n\n  function rescale() {\n    var i = 0, n = Math.max(1, range.length);\n    thresholds = new Array(n - 1);\n    while (++i < n) thresholds[i - 1] = threshold(domain, i / n);\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : range[bisect(thresholds, x)];\n  }\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN] : [\n      i > 0 ? thresholds[i - 1] : domain[0],\n      i < thresholds.length ? thresholds[i] : domain[domain.length - 1]\n    ];\n  };\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(ascending);\n    return rescale();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.quantiles = function() {\n    return thresholds.slice();\n  };\n\n  scale.copy = function() {\n    return quantile()\n        .domain(domain)\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return initRange.apply(scale, arguments);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,EAAEC,MAAM,EAAEC,cAAc,IAAIC,SAAS,QAAO,UAAU;AACvE,SAAQC,SAAS,QAAO,WAAW;AAEnC,eAAe,SAASC,QAAQA,CAAA,EAAG;EACjC,IAAIC,MAAM,GAAG,EAAE;IACXC,KAAK,GAAG,EAAE;IACVC,UAAU,GAAG,EAAE;IACfC,OAAO;EAEX,SAASC,OAAOA,CAAA,EAAG;IACjB,IAAIC,CAAC,GAAG,CAAC;MAAEC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,KAAK,CAACQ,MAAM,CAAC;IACxCP,UAAU,GAAG,IAAIQ,KAAK,CAACJ,CAAC,GAAG,CAAC,CAAC;IAC7B,OAAO,EAAED,CAAC,GAAGC,CAAC,EAAEJ,UAAU,CAACG,CAAC,GAAG,CAAC,CAAC,GAAGR,SAAS,CAACG,MAAM,EAAEK,CAAC,GAAGC,CAAC,CAAC;IAC5D,OAAOK,KAAK;EACd;EAEA,SAASA,KAAKA,CAACC,CAAC,EAAE;IAChB,OAAOA,CAAC,IAAI,IAAI,IAAIC,KAAK,CAACD,CAAC,GAAG,CAACA,CAAC,CAAC,GAAGT,OAAO,GAAGF,KAAK,CAACN,MAAM,CAACO,UAAU,EAAEU,CAAC,CAAC,CAAC;EAC5E;EAEAD,KAAK,CAACG,YAAY,GAAG,UAASC,CAAC,EAAE;IAC/B,IAAIV,CAAC,GAAGJ,KAAK,CAACe,OAAO,CAACD,CAAC,CAAC;IACxB,OAAOV,CAAC,GAAG,CAAC,GAAG,CAACY,GAAG,EAAEA,GAAG,CAAC,GAAG,CAC1BZ,CAAC,GAAG,CAAC,GAAGH,UAAU,CAACG,CAAC,GAAG,CAAC,CAAC,GAAGL,MAAM,CAAC,CAAC,CAAC,EACrCK,CAAC,GAAGH,UAAU,CAACO,MAAM,GAAGP,UAAU,CAACG,CAAC,CAAC,GAAGL,MAAM,CAACA,MAAM,CAACS,MAAM,GAAG,CAAC,CAAC,CAClE;EACH,CAAC;EAEDE,KAAK,CAACX,MAAM,GAAG,UAASkB,CAAC,EAAE;IACzB,IAAI,CAACC,SAAS,CAACV,MAAM,EAAE,OAAOT,MAAM,CAACoB,KAAK,CAAC,CAAC;IAC5CpB,MAAM,GAAG,EAAE;IACX,KAAK,IAAIqB,CAAC,IAAIH,CAAC,EAAE,IAAIG,CAAC,IAAI,IAAI,IAAI,CAACR,KAAK,CAACQ,CAAC,GAAG,CAACA,CAAC,CAAC,EAAErB,MAAM,CAACsB,IAAI,CAACD,CAAC,CAAC;IAChErB,MAAM,CAACuB,IAAI,CAAC7B,SAAS,CAAC;IACtB,OAAOU,OAAO,CAAC,CAAC;EAClB,CAAC;EAEDO,KAAK,CAACV,KAAK,GAAG,UAASiB,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACV,MAAM,IAAIR,KAAK,GAAGS,KAAK,CAACc,IAAI,CAACN,CAAC,CAAC,EAAEd,OAAO,CAAC,CAAC,IAAIH,KAAK,CAACmB,KAAK,CAAC,CAAC;EAC9E,CAAC;EAEDT,KAAK,CAACR,OAAO,GAAG,UAASe,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACV,MAAM,IAAIN,OAAO,GAAGe,CAAC,EAAEP,KAAK,IAAIR,OAAO;EAC1D,CAAC;EAEDQ,KAAK,CAACc,SAAS,GAAG,YAAW;IAC3B,OAAOvB,UAAU,CAACkB,KAAK,CAAC,CAAC;EAC3B,CAAC;EAEDT,KAAK,CAACe,IAAI,GAAG,YAAW;IACtB,OAAO3B,QAAQ,CAAC,CAAC,CACZC,MAAM,CAACA,MAAM,CAAC,CACdC,KAAK,CAACA,KAAK,CAAC,CACZE,OAAO,CAACA,OAAO,CAAC;EACvB,CAAC;EAED,OAAOL,SAAS,CAAC6B,KAAK,CAAChB,KAAK,EAAEQ,SAAS,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}