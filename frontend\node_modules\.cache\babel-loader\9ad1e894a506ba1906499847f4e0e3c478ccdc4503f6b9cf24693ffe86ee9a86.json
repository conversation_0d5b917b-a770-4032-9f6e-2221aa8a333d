{"ast": null, "code": "import EventEmitter from 'eventemitter3';\nvar eventCenter = new EventEmitter();\nexport { eventCenter };\nexport var SYNC_EVENT = 'recharts.syncMouseEvents';", "map": {"version": 3, "names": ["EventEmitter", "eventCenter", "SYNC_EVENT"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/recharts/es6/util/Events.js"], "sourcesContent": ["import EventEmitter from 'eventemitter3';\nvar eventCenter = new EventEmitter();\nexport { eventCenter };\nexport var SYNC_EVENT = 'recharts.syncMouseEvents';"], "mappings": "AAAA,OAAOA,YAAY,MAAM,eAAe;AACxC,IAAIC,WAAW,GAAG,IAAID,YAAY,CAAC,CAAC;AACpC,SAASC,WAAW;AACpB,OAAO,IAAIC,UAAU,GAAG,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}