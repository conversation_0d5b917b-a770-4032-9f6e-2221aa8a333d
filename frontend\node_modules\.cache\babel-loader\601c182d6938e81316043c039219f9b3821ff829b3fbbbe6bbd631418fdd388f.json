{"ast": null, "code": "import { ticks } from \"d3-array\";\nimport { format, formatSpecifier } from \"d3-format\";\nimport nice from \"./nice.js\";\nimport { copy, transformer } from \"./continuous.js\";\nimport { initRange } from \"./init.js\";\nfunction transformLog(x) {\n  return Math.log(x);\n}\nfunction transformExp(x) {\n  return Math.exp(x);\n}\nfunction transformLogn(x) {\n  return -Math.log(-x);\n}\nfunction transformExpn(x) {\n  return -Math.exp(-x);\n}\nfunction pow10(x) {\n  return isFinite(x) ? +(\"1e\" + x) : x < 0 ? 0 : x;\n}\nfunction powp(base) {\n  return base === 10 ? pow10 : base === Math.E ? Math.exp : x => Math.pow(base, x);\n}\nfunction logp(base) {\n  return base === Math.E ? Math.log : base === 10 && Math.log10 || base === 2 && Math.log2 || (base = Math.log(base), x => Math.log(x) / base);\n}\nfunction reflect(f) {\n  return (x, k) => -f(-x, k);\n}\nexport function loggish(transform) {\n  const scale = transform(transformLog, transformExp);\n  const domain = scale.domain;\n  let base = 10;\n  let logs;\n  let pows;\n  function rescale() {\n    logs = logp(base), pows = powp(base);\n    if (domain()[0] < 0) {\n      logs = reflect(logs), pows = reflect(pows);\n      transform(transformLogn, transformExpn);\n    } else {\n      transform(transformLog, transformExp);\n    }\n    return scale;\n  }\n  scale.base = function (_) {\n    return arguments.length ? (base = +_, rescale()) : base;\n  };\n  scale.domain = function (_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n  scale.ticks = count => {\n    const d = domain();\n    let u = d[0];\n    let v = d[d.length - 1];\n    const r = v < u;\n    if (r) [u, v] = [v, u];\n    let i = logs(u);\n    let j = logs(v);\n    let k;\n    let t;\n    const n = count == null ? 10 : +count;\n    let z = [];\n    if (!(base % 1) && j - i < n) {\n      i = Math.floor(i), j = Math.ceil(j);\n      if (u > 0) for (; i <= j; ++i) {\n        for (k = 1; k < base; ++k) {\n          t = i < 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      } else for (; i <= j; ++i) {\n        for (k = base - 1; k >= 1; --k) {\n          t = i > 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      }\n      if (z.length * 2 < n) z = ticks(u, v, n);\n    } else {\n      z = ticks(i, j, Math.min(j - i, n)).map(pows);\n    }\n    return r ? z.reverse() : z;\n  };\n  scale.tickFormat = (count, specifier) => {\n    if (count == null) count = 10;\n    if (specifier == null) specifier = base === 10 ? \"s\" : \",\";\n    if (typeof specifier !== \"function\") {\n      if (!(base % 1) && (specifier = formatSpecifier(specifier)).precision == null) specifier.trim = true;\n      specifier = format(specifier);\n    }\n    if (count === Infinity) return specifier;\n    const k = Math.max(1, base * count / scale.ticks().length); // TODO fast estimate?\n    return d => {\n      let i = d / pows(Math.round(logs(d)));\n      if (i * base < base - 0.5) i *= base;\n      return i <= k ? specifier(d) : \"\";\n    };\n  };\n  scale.nice = () => {\n    return domain(nice(domain(), {\n      floor: x => pows(Math.floor(logs(x))),\n      ceil: x => pows(Math.ceil(logs(x)))\n    }));\n  };\n  return scale;\n}\nexport default function log() {\n  const scale = loggish(transformer()).domain([1, 10]);\n  scale.copy = () => copy(scale, log()).base(scale.base());\n  initRange.apply(scale, arguments);\n  return scale;\n}", "map": {"version": 3, "names": ["ticks", "format", "formatSpecifier", "nice", "copy", "transformer", "initRange", "transformLog", "x", "Math", "log", "transformExp", "exp", "transformLogn", "transformExpn", "pow10", "isFinite", "powp", "base", "E", "pow", "logp", "log10", "log2", "reflect", "f", "k", "loggish", "transform", "scale", "domain", "logs", "pows", "rescale", "_", "arguments", "length", "count", "d", "u", "v", "r", "i", "j", "t", "n", "z", "floor", "ceil", "push", "min", "map", "reverse", "tickFormat", "specifier", "precision", "trim", "Infinity", "max", "round", "apply"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/d3-scale/src/log.js"], "sourcesContent": ["import {ticks} from \"d3-array\";\nimport {format, formatSpecifier} from \"d3-format\";\nimport nice from \"./nice.js\";\nimport {copy, transformer} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\n\nfunction transformLog(x) {\n  return Math.log(x);\n}\n\nfunction transformExp(x) {\n  return Math.exp(x);\n}\n\nfunction transformLogn(x) {\n  return -Math.log(-x);\n}\n\nfunction transformExpn(x) {\n  return -Math.exp(-x);\n}\n\nfunction pow10(x) {\n  return isFinite(x) ? +(\"1e\" + x) : x < 0 ? 0 : x;\n}\n\nfunction powp(base) {\n  return base === 10 ? pow10\n      : base === Math.E ? Math.exp\n      : x => Math.pow(base, x);\n}\n\nfunction logp(base) {\n  return base === Math.E ? Math.log\n      : base === 10 && Math.log10\n      || base === 2 && Math.log2\n      || (base = Math.log(base), x => Math.log(x) / base);\n}\n\nfunction reflect(f) {\n  return (x, k) => -f(-x, k);\n}\n\nexport function loggish(transform) {\n  const scale = transform(transformLog, transformExp);\n  const domain = scale.domain;\n  let base = 10;\n  let logs;\n  let pows;\n\n  function rescale() {\n    logs = logp(base), pows = powp(base);\n    if (domain()[0] < 0) {\n      logs = reflect(logs), pows = reflect(pows);\n      transform(transformLogn, transformExpn);\n    } else {\n      transform(transformLog, transformExp);\n    }\n    return scale;\n  }\n\n  scale.base = function(_) {\n    return arguments.length ? (base = +_, rescale()) : base;\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.ticks = count => {\n    const d = domain();\n    let u = d[0];\n    let v = d[d.length - 1];\n    const r = v < u;\n\n    if (r) ([u, v] = [v, u]);\n\n    let i = logs(u);\n    let j = logs(v);\n    let k;\n    let t;\n    const n = count == null ? 10 : +count;\n    let z = [];\n\n    if (!(base % 1) && j - i < n) {\n      i = Math.floor(i), j = Math.ceil(j);\n      if (u > 0) for (; i <= j; ++i) {\n        for (k = 1; k < base; ++k) {\n          t = i < 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      } else for (; i <= j; ++i) {\n        for (k = base - 1; k >= 1; --k) {\n          t = i > 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      }\n      if (z.length * 2 < n) z = ticks(u, v, n);\n    } else {\n      z = ticks(i, j, Math.min(j - i, n)).map(pows);\n    }\n    return r ? z.reverse() : z;\n  };\n\n  scale.tickFormat = (count, specifier) => {\n    if (count == null) count = 10;\n    if (specifier == null) specifier = base === 10 ? \"s\" : \",\";\n    if (typeof specifier !== \"function\") {\n      if (!(base % 1) && (specifier = formatSpecifier(specifier)).precision == null) specifier.trim = true;\n      specifier = format(specifier);\n    }\n    if (count === Infinity) return specifier;\n    const k = Math.max(1, base * count / scale.ticks().length); // TODO fast estimate?\n    return d => {\n      let i = d / pows(Math.round(logs(d)));\n      if (i * base < base - 0.5) i *= base;\n      return i <= k ? specifier(d) : \"\";\n    };\n  };\n\n  scale.nice = () => {\n    return domain(nice(domain(), {\n      floor: x => pows(Math.floor(logs(x))),\n      ceil: x => pows(Math.ceil(logs(x)))\n    }));\n  };\n\n  return scale;\n}\n\nexport default function log() {\n  const scale = loggish(transformer()).domain([1, 10]);\n  scale.copy = () => copy(scale, log()).base(scale.base());\n  initRange.apply(scale, arguments);\n  return scale;\n}\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,UAAU;AAC9B,SAAQC,MAAM,EAAEC,eAAe,QAAO,WAAW;AACjD,OAAOC,IAAI,MAAM,WAAW;AAC5B,SAAQC,IAAI,EAAEC,WAAW,QAAO,iBAAiB;AACjD,SAAQC,SAAS,QAAO,WAAW;AAEnC,SAASC,YAAYA,CAACC,CAAC,EAAE;EACvB,OAAOC,IAAI,CAACC,GAAG,CAACF,CAAC,CAAC;AACpB;AAEA,SAASG,YAAYA,CAACH,CAAC,EAAE;EACvB,OAAOC,IAAI,CAACG,GAAG,CAACJ,CAAC,CAAC;AACpB;AAEA,SAASK,aAAaA,CAACL,CAAC,EAAE;EACxB,OAAO,CAACC,IAAI,CAACC,GAAG,CAAC,CAACF,CAAC,CAAC;AACtB;AAEA,SAASM,aAAaA,CAACN,CAAC,EAAE;EACxB,OAAO,CAACC,IAAI,CAACG,GAAG,CAAC,CAACJ,CAAC,CAAC;AACtB;AAEA,SAASO,KAAKA,CAACP,CAAC,EAAE;EAChB,OAAOQ,QAAQ,CAACR,CAAC,CAAC,GAAG,EAAE,IAAI,GAAGA,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC;AAClD;AAEA,SAASS,IAAIA,CAACC,IAAI,EAAE;EAClB,OAAOA,IAAI,KAAK,EAAE,GAAGH,KAAK,GACpBG,IAAI,KAAKT,IAAI,CAACU,CAAC,GAAGV,IAAI,CAACG,GAAG,GAC1BJ,CAAC,IAAIC,IAAI,CAACW,GAAG,CAACF,IAAI,EAAEV,CAAC,CAAC;AAC9B;AAEA,SAASa,IAAIA,CAACH,IAAI,EAAE;EAClB,OAAOA,IAAI,KAAKT,IAAI,CAACU,CAAC,GAAGV,IAAI,CAACC,GAAG,GAC3BQ,IAAI,KAAK,EAAE,IAAIT,IAAI,CAACa,KAAK,IACxBJ,IAAI,KAAK,CAAC,IAAIT,IAAI,CAACc,IAAI,KACtBL,IAAI,GAAGT,IAAI,CAACC,GAAG,CAACQ,IAAI,CAAC,EAAEV,CAAC,IAAIC,IAAI,CAACC,GAAG,CAACF,CAAC,CAAC,GAAGU,IAAI,CAAC;AACzD;AAEA,SAASM,OAAOA,CAACC,CAAC,EAAE;EAClB,OAAO,CAACjB,CAAC,EAAEkB,CAAC,KAAK,CAACD,CAAC,CAAC,CAACjB,CAAC,EAAEkB,CAAC,CAAC;AAC5B;AAEA,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjC,MAAMC,KAAK,GAAGD,SAAS,CAACrB,YAAY,EAAEI,YAAY,CAAC;EACnD,MAAMmB,MAAM,GAAGD,KAAK,CAACC,MAAM;EAC3B,IAAIZ,IAAI,GAAG,EAAE;EACb,IAAIa,IAAI;EACR,IAAIC,IAAI;EAER,SAASC,OAAOA,CAAA,EAAG;IACjBF,IAAI,GAAGV,IAAI,CAACH,IAAI,CAAC,EAAEc,IAAI,GAAGf,IAAI,CAACC,IAAI,CAAC;IACpC,IAAIY,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACnBC,IAAI,GAAGP,OAAO,CAACO,IAAI,CAAC,EAAEC,IAAI,GAAGR,OAAO,CAACQ,IAAI,CAAC;MAC1CJ,SAAS,CAACf,aAAa,EAAEC,aAAa,CAAC;IACzC,CAAC,MAAM;MACLc,SAAS,CAACrB,YAAY,EAAEI,YAAY,CAAC;IACvC;IACA,OAAOkB,KAAK;EACd;EAEAA,KAAK,CAACX,IAAI,GAAG,UAASgB,CAAC,EAAE;IACvB,OAAOC,SAAS,CAACC,MAAM,IAAIlB,IAAI,GAAG,CAACgB,CAAC,EAAED,OAAO,CAAC,CAAC,IAAIf,IAAI;EACzD,CAAC;EAEDW,KAAK,CAACC,MAAM,GAAG,UAASI,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACC,MAAM,IAAIN,MAAM,CAACI,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,IAAIH,MAAM,CAAC,CAAC;EAC7D,CAAC;EAEDD,KAAK,CAAC7B,KAAK,GAAGqC,KAAK,IAAI;IACrB,MAAMC,CAAC,GAAGR,MAAM,CAAC,CAAC;IAClB,IAAIS,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIE,CAAC,GAAGF,CAAC,CAACA,CAAC,CAACF,MAAM,GAAG,CAAC,CAAC;IACvB,MAAMK,CAAC,GAAGD,CAAC,GAAGD,CAAC;IAEf,IAAIE,CAAC,EAAG,CAACF,CAAC,EAAEC,CAAC,CAAC,GAAG,CAACA,CAAC,EAAED,CAAC,CAAC;IAEvB,IAAIG,CAAC,GAAGX,IAAI,CAACQ,CAAC,CAAC;IACf,IAAII,CAAC,GAAGZ,IAAI,CAACS,CAAC,CAAC;IACf,IAAId,CAAC;IACL,IAAIkB,CAAC;IACL,MAAMC,CAAC,GAAGR,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,CAACA,KAAK;IACrC,IAAIS,CAAC,GAAG,EAAE;IAEV,IAAI,EAAE5B,IAAI,GAAG,CAAC,CAAC,IAAIyB,CAAC,GAAGD,CAAC,GAAGG,CAAC,EAAE;MAC5BH,CAAC,GAAGjC,IAAI,CAACsC,KAAK,CAACL,CAAC,CAAC,EAAEC,CAAC,GAAGlC,IAAI,CAACuC,IAAI,CAACL,CAAC,CAAC;MACnC,IAAIJ,CAAC,GAAG,CAAC,EAAE,OAAOG,CAAC,IAAIC,CAAC,EAAE,EAAED,CAAC,EAAE;QAC7B,KAAKhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,EAAE,EAAEQ,CAAC,EAAE;UACzBkB,CAAC,GAAGF,CAAC,GAAG,CAAC,GAAGhB,CAAC,GAAGM,IAAI,CAAC,CAACU,CAAC,CAAC,GAAGhB,CAAC,GAAGM,IAAI,CAACU,CAAC,CAAC;UACtC,IAAIE,CAAC,GAAGL,CAAC,EAAE;UACX,IAAIK,CAAC,GAAGJ,CAAC,EAAE;UACXM,CAAC,CAACG,IAAI,CAACL,CAAC,CAAC;QACX;MACF,CAAC,MAAM,OAAOF,CAAC,IAAIC,CAAC,EAAE,EAAED,CAAC,EAAE;QACzB,KAAKhB,CAAC,GAAGR,IAAI,GAAG,CAAC,EAAEQ,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;UAC9BkB,CAAC,GAAGF,CAAC,GAAG,CAAC,GAAGhB,CAAC,GAAGM,IAAI,CAAC,CAACU,CAAC,CAAC,GAAGhB,CAAC,GAAGM,IAAI,CAACU,CAAC,CAAC;UACtC,IAAIE,CAAC,GAAGL,CAAC,EAAE;UACX,IAAIK,CAAC,GAAGJ,CAAC,EAAE;UACXM,CAAC,CAACG,IAAI,CAACL,CAAC,CAAC;QACX;MACF;MACA,IAAIE,CAAC,CAACV,MAAM,GAAG,CAAC,GAAGS,CAAC,EAAEC,CAAC,GAAG9C,KAAK,CAACuC,CAAC,EAAEC,CAAC,EAAEK,CAAC,CAAC;IAC1C,CAAC,MAAM;MACLC,CAAC,GAAG9C,KAAK,CAAC0C,CAAC,EAAEC,CAAC,EAAElC,IAAI,CAACyC,GAAG,CAACP,CAAC,GAAGD,CAAC,EAAEG,CAAC,CAAC,CAAC,CAACM,GAAG,CAACnB,IAAI,CAAC;IAC/C;IACA,OAAOS,CAAC,GAAGK,CAAC,CAACM,OAAO,CAAC,CAAC,GAAGN,CAAC;EAC5B,CAAC;EAEDjB,KAAK,CAACwB,UAAU,GAAG,CAAChB,KAAK,EAAEiB,SAAS,KAAK;IACvC,IAAIjB,KAAK,IAAI,IAAI,EAAEA,KAAK,GAAG,EAAE;IAC7B,IAAIiB,SAAS,IAAI,IAAI,EAAEA,SAAS,GAAGpC,IAAI,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG;IAC1D,IAAI,OAAOoC,SAAS,KAAK,UAAU,EAAE;MACnC,IAAI,EAAEpC,IAAI,GAAG,CAAC,CAAC,IAAI,CAACoC,SAAS,GAAGpD,eAAe,CAACoD,SAAS,CAAC,EAAEC,SAAS,IAAI,IAAI,EAAED,SAAS,CAACE,IAAI,GAAG,IAAI;MACpGF,SAAS,GAAGrD,MAAM,CAACqD,SAAS,CAAC;IAC/B;IACA,IAAIjB,KAAK,KAAKoB,QAAQ,EAAE,OAAOH,SAAS;IACxC,MAAM5B,CAAC,GAAGjB,IAAI,CAACiD,GAAG,CAAC,CAAC,EAAExC,IAAI,GAAGmB,KAAK,GAAGR,KAAK,CAAC7B,KAAK,CAAC,CAAC,CAACoC,MAAM,CAAC,CAAC,CAAC;IAC5D,OAAOE,CAAC,IAAI;MACV,IAAII,CAAC,GAAGJ,CAAC,GAAGN,IAAI,CAACvB,IAAI,CAACkD,KAAK,CAAC5B,IAAI,CAACO,CAAC,CAAC,CAAC,CAAC;MACrC,IAAII,CAAC,GAAGxB,IAAI,GAAGA,IAAI,GAAG,GAAG,EAAEwB,CAAC,IAAIxB,IAAI;MACpC,OAAOwB,CAAC,IAAIhB,CAAC,GAAG4B,SAAS,CAAChB,CAAC,CAAC,GAAG,EAAE;IACnC,CAAC;EACH,CAAC;EAEDT,KAAK,CAAC1B,IAAI,GAAG,MAAM;IACjB,OAAO2B,MAAM,CAAC3B,IAAI,CAAC2B,MAAM,CAAC,CAAC,EAAE;MAC3BiB,KAAK,EAAEvC,CAAC,IAAIwB,IAAI,CAACvB,IAAI,CAACsC,KAAK,CAAChB,IAAI,CAACvB,CAAC,CAAC,CAAC,CAAC;MACrCwC,IAAI,EAAExC,CAAC,IAAIwB,IAAI,CAACvB,IAAI,CAACuC,IAAI,CAACjB,IAAI,CAACvB,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,OAAOqB,KAAK;AACd;AAEA,eAAe,SAASnB,GAAGA,CAAA,EAAG;EAC5B,MAAMmB,KAAK,GAAGF,OAAO,CAACtB,WAAW,CAAC,CAAC,CAAC,CAACyB,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EACpDD,KAAK,CAACzB,IAAI,GAAG,MAAMA,IAAI,CAACyB,KAAK,EAAEnB,GAAG,CAAC,CAAC,CAAC,CAACQ,IAAI,CAACW,KAAK,CAACX,IAAI,CAAC,CAAC,CAAC;EACxDZ,SAAS,CAACsD,KAAK,CAAC/B,KAAK,EAAEM,SAAS,CAAC;EACjC,OAAON,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}