{"ast": null, "code": "export default function reduce(values, reducer, value) {\n  if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n  const iterator = values[Symbol.iterator]();\n  let done,\n    next,\n    index = -1;\n  if (arguments.length < 3) {\n    ({\n      done,\n      value\n    } = iterator.next());\n    if (done) return;\n    ++index;\n  }\n  while ({\n    done,\n    value: next\n  } = iterator.next(), !done) {\n    value = reducer(value, next, ++index, values);\n  }\n  return value;\n}", "map": {"version": 3, "names": ["reduce", "values", "reducer", "value", "TypeError", "iterator", "Symbol", "done", "next", "index", "arguments", "length"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/d3-array/src/reduce.js"], "sourcesContent": ["export default function reduce(values, reducer, value) {\n  if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n  const iterator = values[Symbol.iterator]();\n  let done, next, index = -1;\n  if (arguments.length < 3) {\n    ({done, value} = iterator.next());\n    if (done) return;\n    ++index;\n  }\n  while (({done, value: next} = iterator.next()), !done) {\n    value = reducer(value, next, ++index, values);\n  }\n  return value;\n}\n"], "mappings": "AAAA,eAAe,SAASA,MAAMA,CAACC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACrD,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE,MAAM,IAAIE,SAAS,CAAC,2BAA2B,CAAC;EACnF,MAAMC,QAAQ,GAAGJ,MAAM,CAACK,MAAM,CAACD,QAAQ,CAAC,CAAC,CAAC;EAC1C,IAAIE,IAAI;IAAEC,IAAI;IAAEC,KAAK,GAAG,CAAC,CAAC;EAC1B,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,CAAC;MAACJ,IAAI;MAAEJ;IAAK,CAAC,GAAGE,QAAQ,CAACG,IAAI,CAAC,CAAC;IAChC,IAAID,IAAI,EAAE;IACV,EAAEE,KAAK;EACT;EACA,OAAQ;IAACF,IAAI;IAAEJ,KAAK,EAAEK;EAAI,CAAC,GAAGH,QAAQ,CAACG,IAAI,CAAC,CAAC,EAAG,CAACD,IAAI,EAAE;IACrDJ,KAAK,GAAGD,OAAO,CAACC,KAAK,EAAEK,IAAI,EAAE,EAAEC,KAAK,EAAER,MAAM,CAAC;EAC/C;EACA,OAAOE,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}