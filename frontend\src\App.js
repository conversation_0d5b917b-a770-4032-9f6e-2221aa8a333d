import React, { useEffect, useState } from "react";
import axios from "axios";
import Chart from "./components/Chart";

function App() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchData = async () => {
    try {
      if (!loading) setIsRefreshing(true); // Only show refreshing indicator after initial load
      setError(null);
      const response = await axios.get("http://localhost:5000/api/data");
      setData(response.data.reverse());
      setLastUpdated(new Date());
      setLoading(false);
      setIsRefreshing(false);
    } catch (err) {
      console.error("Error fetching data:", err);
      setError("Failed to fetch sensor data");
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    // Fetch data immediately when component mounts
    fetchData();

    // Set up interval to fetch data every 5 seconds
    const interval = setInterval(() => {
      fetchData();
    }, 5000);

    // Cleanup interval on component unmount
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="dashboard-container">
        <h2>IoT Sensor Dashboard</h2>
        <div className="chart-container">
          <p>Loading sensor data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dashboard-container">
        <h2>IoT Sensor Dashboard</h2>
        <div className="chart-container">
          <p style={{ color: "red" }}>{error}</p>
          <p>Data will automatically refresh every 5 seconds.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <h2>IoT Sensor Dashboard</h2>
      <div className="chart-container">
        <div style={{ marginBottom: "10px", fontSize: "14px", color: "#666" }}>
          <span>
            {isRefreshing
              ? "🔄 Refreshing..."
              : "🔄 Auto-refreshing every 5 seconds"}
          </span>
          <span style={{ marginLeft: "20px" }}>
            Last updated:{" "}
            {lastUpdated ? lastUpdated.toLocaleTimeString() : "Loading..."}
          </span>
        </div>
        <Chart data={data} />
      </div>
    </div>
  );
}

export default App;
