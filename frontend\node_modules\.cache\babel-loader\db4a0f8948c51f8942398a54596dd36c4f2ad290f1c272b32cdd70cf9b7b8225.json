{"ast": null, "code": "var arrayMap = require('./_arrayMap'),\n  baseGet = require('./_baseGet'),\n  baseIteratee = require('./_baseIteratee'),\n  baseMap = require('./_baseMap'),\n  baseSortBy = require('./_baseSortBy'),\n  baseUnary = require('./_baseUnary'),\n  compareMultiple = require('./_compareMultiple'),\n  identity = require('./identity'),\n  isArray = require('./isArray');\n\n/**\n * The base implementation of `_.orderBy` without param guards.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function[]|Object[]|string[]} iteratees The iteratees to sort by.\n * @param {string[]} orders The sort orders of `iteratees`.\n * @returns {Array} Returns the new sorted array.\n */\nfunction baseOrderBy(collection, iteratees, orders) {\n  if (iteratees.length) {\n    iteratees = arrayMap(iteratees, function (iteratee) {\n      if (isArray(iteratee)) {\n        return function (value) {\n          return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);\n        };\n      }\n      return iteratee;\n    });\n  } else {\n    iteratees = [identity];\n  }\n  var index = -1;\n  iteratees = arrayMap(iteratees, baseUnary(baseIteratee));\n  var result = baseMap(collection, function (value, key, collection) {\n    var criteria = arrayMap(iteratees, function (iteratee) {\n      return iteratee(value);\n    });\n    return {\n      'criteria': criteria,\n      'index': ++index,\n      'value': value\n    };\n  });\n  return baseSortBy(result, function (object, other) {\n    return compareMultiple(object, other, orders);\n  });\n}\nmodule.exports = baseOrderBy;", "map": {"version": 3, "names": ["arrayMap", "require", "baseGet", "baseIteratee", "baseMap", "baseSortBy", "baseUnary", "compareMultiple", "identity", "isArray", "baseOrderBy", "collection", "iteratees", "orders", "length", "iteratee", "value", "index", "result", "key", "criteria", "object", "other", "module", "exports"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/lodash/_baseOrderBy.js"], "sourcesContent": ["var arrayMap = require('./_arrayMap'),\n    baseGet = require('./_baseGet'),\n    baseIteratee = require('./_baseIteratee'),\n    baseMap = require('./_baseMap'),\n    baseSortBy = require('./_baseSortBy'),\n    baseUnary = require('./_baseUnary'),\n    compareMultiple = require('./_compareMultiple'),\n    identity = require('./identity'),\n    isArray = require('./isArray');\n\n/**\n * The base implementation of `_.orderBy` without param guards.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function[]|Object[]|string[]} iteratees The iteratees to sort by.\n * @param {string[]} orders The sort orders of `iteratees`.\n * @returns {Array} Returns the new sorted array.\n */\nfunction baseOrderBy(collection, iteratees, orders) {\n  if (iteratees.length) {\n    iteratees = arrayMap(iteratees, function(iteratee) {\n      if (isArray(iteratee)) {\n        return function(value) {\n          return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);\n        }\n      }\n      return iteratee;\n    });\n  } else {\n    iteratees = [identity];\n  }\n\n  var index = -1;\n  iteratees = arrayMap(iteratees, baseUnary(baseIteratee));\n\n  var result = baseMap(collection, function(value, key, collection) {\n    var criteria = arrayMap(iteratees, function(iteratee) {\n      return iteratee(value);\n    });\n    return { 'criteria': criteria, 'index': ++index, 'value': value };\n  });\n\n  return baseSortBy(result, function(object, other) {\n    return compareMultiple(object, other, orders);\n  });\n}\n\nmodule.exports = baseOrderBy;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACjCC,OAAO,GAAGD,OAAO,CAAC,YAAY,CAAC;EAC/BE,YAAY,GAAGF,OAAO,CAAC,iBAAiB,CAAC;EACzCG,OAAO,GAAGH,OAAO,CAAC,YAAY,CAAC;EAC/BI,UAAU,GAAGJ,OAAO,CAAC,eAAe,CAAC;EACrCK,SAAS,GAAGL,OAAO,CAAC,cAAc,CAAC;EACnCM,eAAe,GAAGN,OAAO,CAAC,oBAAoB,CAAC;EAC/CO,QAAQ,GAAGP,OAAO,CAAC,YAAY,CAAC;EAChCQ,OAAO,GAAGR,OAAO,CAAC,WAAW,CAAC;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,WAAWA,CAACC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAE;EAClD,IAAID,SAAS,CAACE,MAAM,EAAE;IACpBF,SAAS,GAAGZ,QAAQ,CAACY,SAAS,EAAE,UAASG,QAAQ,EAAE;MACjD,IAAIN,OAAO,CAACM,QAAQ,CAAC,EAAE;QACrB,OAAO,UAASC,KAAK,EAAE;UACrB,OAAOd,OAAO,CAACc,KAAK,EAAED,QAAQ,CAACD,MAAM,KAAK,CAAC,GAAGC,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC;QACvE,CAAC;MACH;MACA,OAAOA,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC,MAAM;IACLH,SAAS,GAAG,CAACJ,QAAQ,CAAC;EACxB;EAEA,IAAIS,KAAK,GAAG,CAAC,CAAC;EACdL,SAAS,GAAGZ,QAAQ,CAACY,SAAS,EAAEN,SAAS,CAACH,YAAY,CAAC,CAAC;EAExD,IAAIe,MAAM,GAAGd,OAAO,CAACO,UAAU,EAAE,UAASK,KAAK,EAAEG,GAAG,EAAER,UAAU,EAAE;IAChE,IAAIS,QAAQ,GAAGpB,QAAQ,CAACY,SAAS,EAAE,UAASG,QAAQ,EAAE;MACpD,OAAOA,QAAQ,CAACC,KAAK,CAAC;IACxB,CAAC,CAAC;IACF,OAAO;MAAE,UAAU,EAAEI,QAAQ;MAAE,OAAO,EAAE,EAAEH,KAAK;MAAE,OAAO,EAAED;IAAM,CAAC;EACnE,CAAC,CAAC;EAEF,OAAOX,UAAU,CAACa,MAAM,EAAE,UAASG,MAAM,EAAEC,KAAK,EAAE;IAChD,OAAOf,eAAe,CAACc,MAAM,EAAEC,KAAK,EAAET,MAAM,CAAC;EAC/C,CAAC,CAAC;AACJ;AAEAU,MAAM,CAACC,OAAO,GAAGd,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}