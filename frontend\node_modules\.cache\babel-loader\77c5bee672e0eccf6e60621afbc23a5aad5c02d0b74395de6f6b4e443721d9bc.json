{"ast": null, "code": "import { CardinalClosed } from \"./cardinalClosed.js\";\nimport noop from \"../noop.js\";\nimport { point } from \"./catmullRom.js\";\nfunction CatmullRomClosed(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\nCatmullRomClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function () {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;\n  },\n  lineEnd: function () {\n    switch (this._point) {\n      case 1:\n        {\n          this._context.moveTo(this._x3, this._y3);\n          this._context.closePath();\n          break;\n        }\n      case 2:\n        {\n          this._context.lineTo(this._x3, this._y3);\n          this._context.closePath();\n          break;\n        }\n      case 3:\n        {\n          this.point(this._x3, this._y3);\n          this.point(this._x4, this._y4);\n          this.point(this._x5, this._y5);\n          break;\n        }\n    }\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    if (this._point) {\n      var x23 = this._x2 - x,\n        y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._x3 = x, this._y3 = y;\n        break;\n      case 1:\n        this._point = 2;\n        this._context.moveTo(this._x4 = x, this._y4 = y);\n        break;\n      case 2:\n        this._point = 3;\n        this._x5 = x, this._y5 = y;\n        break;\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\nexport default (function custom(alpha) {\n  function catmullRom(context) {\n    return alpha ? new CatmullRomClosed(context, alpha) : new CardinalClosed(context, 0);\n  }\n  catmullRom.alpha = function (alpha) {\n    return custom(+alpha);\n  };\n  return catmullRom;\n})(0.5);", "map": {"version": 3, "names": ["CardinalClosed", "noop", "point", "CatmullRomClosed", "context", "alpha", "_context", "_alpha", "prototype", "areaStart", "areaEnd", "lineStart", "_x0", "_x1", "_x2", "_x3", "_x4", "_x5", "_y0", "_y1", "_y2", "_y3", "_y4", "_y5", "NaN", "_l01_a", "_l12_a", "_l23_a", "_l01_2a", "_l12_2a", "_l23_2a", "_point", "lineEnd", "moveTo", "closePath", "lineTo", "x", "y", "x23", "y23", "Math", "sqrt", "pow", "custom", "catmullRom"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/d3-shape/src/curve/catmullRomClosed.js"], "sourcesContent": ["import {CardinalClosed} from \"./cardinalClosed.js\";\nimport noop from \"../noop.js\";\nimport {point} from \"./catmullRom.js\";\n\nfunction CatmullRomClosed(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRomClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.lineTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        this.point(this._x5, this._y5);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; this._x3 = x, this._y3 = y; break;\n      case 1: this._point = 2; this._context.moveTo(this._x4 = x, this._y4 = y); break;\n      case 2: this._point = 3; this._x5 = x, this._y5 = y; break;\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRomClosed(context, alpha) : new CardinalClosed(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n"], "mappings": "AAAA,SAAQA,cAAc,QAAO,qBAAqB;AAClD,OAAOC,IAAI,MAAM,YAAY;AAC7B,SAAQC,KAAK,QAAO,iBAAiB;AAErC,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,KAAK,EAAE;EACxC,IAAI,CAACC,QAAQ,GAAGF,OAAO;EACvB,IAAI,CAACG,MAAM,GAAGF,KAAK;AACrB;AAEAF,gBAAgB,CAACK,SAAS,GAAG;EAC3BC,SAAS,EAAER,IAAI;EACfS,OAAO,EAAET,IAAI;EACbU,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAC/D,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAGC,GAAG;IACrE,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,MAAM,GACvC,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,OAAO,GAC1C,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,QAAQ,IAAI,CAACD,MAAM;MACjB,KAAK,CAAC;QAAE;UACN,IAAI,CAACzB,QAAQ,CAAC2B,MAAM,CAAC,IAAI,CAAClB,GAAG,EAAE,IAAI,CAACM,GAAG,CAAC;UACxC,IAAI,CAACf,QAAQ,CAAC4B,SAAS,CAAC,CAAC;UACzB;QACF;MACA,KAAK,CAAC;QAAE;UACN,IAAI,CAAC5B,QAAQ,CAAC6B,MAAM,CAAC,IAAI,CAACpB,GAAG,EAAE,IAAI,CAACM,GAAG,CAAC;UACxC,IAAI,CAACf,QAAQ,CAAC4B,SAAS,CAAC,CAAC;UACzB;QACF;MACA,KAAK,CAAC;QAAE;UACN,IAAI,CAAChC,KAAK,CAAC,IAAI,CAACa,GAAG,EAAE,IAAI,CAACM,GAAG,CAAC;UAC9B,IAAI,CAACnB,KAAK,CAAC,IAAI,CAACc,GAAG,EAAE,IAAI,CAACM,GAAG,CAAC;UAC9B,IAAI,CAACpB,KAAK,CAAC,IAAI,CAACe,GAAG,EAAE,IAAI,CAACM,GAAG,CAAC;UAC9B;QACF;IACF;EACF,CAAC;EACDrB,KAAK,EAAE,SAAAA,CAASkC,CAAC,EAAEC,CAAC,EAAE;IACpBD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC;IAEd,IAAI,IAAI,CAACN,MAAM,EAAE;MACf,IAAIO,GAAG,GAAG,IAAI,CAACxB,GAAG,GAAGsB,CAAC;QAClBG,GAAG,GAAG,IAAI,CAACnB,GAAG,GAAGiB,CAAC;MACtB,IAAI,CAACV,MAAM,GAAGa,IAAI,CAACC,IAAI,CAAC,IAAI,CAACX,OAAO,GAAGU,IAAI,CAACE,GAAG,CAACJ,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,EAAE,IAAI,CAAChC,MAAM,CAAC,CAAC;IACtF;IAEA,QAAQ,IAAI,CAACwB,MAAM;MACjB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE,IAAI,CAAChB,GAAG,GAAGqB,CAAC,EAAE,IAAI,CAACf,GAAG,GAAGgB,CAAC;QAAE;MACrD,KAAK,CAAC;QAAE,IAAI,CAACN,MAAM,GAAG,CAAC;QAAE,IAAI,CAACzB,QAAQ,CAAC2B,MAAM,CAAC,IAAI,CAACjB,GAAG,GAAGoB,CAAC,EAAE,IAAI,CAACd,GAAG,GAAGe,CAAC,CAAC;QAAE;MAC3E,KAAK,CAAC;QAAE,IAAI,CAACN,MAAM,GAAG,CAAC;QAAE,IAAI,CAACd,GAAG,GAAGmB,CAAC,EAAE,IAAI,CAACb,GAAG,GAAGc,CAAC;QAAE;MACrD;QAASnC,KAAK,CAAC,IAAI,EAAEkC,CAAC,EAAEC,CAAC,CAAC;QAAE;IAC9B;IAEA,IAAI,CAACZ,MAAM,GAAG,IAAI,CAACC,MAAM,EAAE,IAAI,CAACA,MAAM,GAAG,IAAI,CAACC,MAAM;IACpD,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,OAAO,EAAE,IAAI,CAACA,OAAO,GAAG,IAAI,CAACC,OAAO;IACxD,IAAI,CAAClB,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGsB,CAAC;IACtD,IAAI,CAAClB,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGiB,CAAC;EACxD;AACF,CAAC;AAED,eAAe,CAAC,SAASM,MAAMA,CAACtC,KAAK,EAAE;EAErC,SAASuC,UAAUA,CAACxC,OAAO,EAAE;IAC3B,OAAOC,KAAK,GAAG,IAAIF,gBAAgB,CAACC,OAAO,EAAEC,KAAK,CAAC,GAAG,IAAIL,cAAc,CAACI,OAAO,EAAE,CAAC,CAAC;EACtF;EAEAwC,UAAU,CAACvC,KAAK,GAAG,UAASA,KAAK,EAAE;IACjC,OAAOsC,MAAM,CAAC,CAACtC,KAAK,CAAC;EACvB,CAAC;EAED,OAAOuC,UAAU;AACnB,CAAC,EAAE,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}