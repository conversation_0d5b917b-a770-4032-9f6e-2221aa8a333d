const mqtt = require("mqtt");
const pool = require("./db");

const client = mqtt.connect("mqtt://broker.hivemq.com");

client.on("connect", () => {
  console.log("MQTT connected");
  client.subscribe("iot/sensors");
});

client.on("message", async (topic, message) => {
  const payload = JSON.parse(message.toString());
  const { device_id, temperature, humidity } = payload;

  try {
    await pool.query(
      "INSERT INTO sensor_data (device_id, temperature, humidity) VALUES ($1, $2, $3)",
      [device_id, temperature, humidity]
    );
  } catch (err) {
    console.error("DB insert error", err.message);
  }
});
