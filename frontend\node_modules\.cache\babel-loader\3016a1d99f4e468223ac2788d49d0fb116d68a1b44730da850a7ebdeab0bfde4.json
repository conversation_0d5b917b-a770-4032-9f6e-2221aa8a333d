{"ast": null, "code": "import { InternSet } from \"internmap\";\nexport default function intersection(values, ...others) {\n  values = new InternSet(values);\n  others = others.map(set);\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n  return values;\n}\nfunction set(values) {\n  return values instanceof InternSet ? values : new InternSet(values);\n}", "map": {"version": 3, "names": ["InternSet", "intersection", "values", "others", "map", "set", "out", "value", "other", "has", "delete"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/d3-array/src/intersection.js"], "sourcesContent": ["import {InternSet} from \"internmap\";\n\nexport default function intersection(values, ...others) {\n  values = new InternSet(values);\n  others = others.map(set);\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n  return values;\n}\n\nfunction set(values) {\n  return values instanceof InternSet ? values : new InternSet(values);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,WAAW;AAEnC,eAAe,SAASC,YAAYA,CAACC,MAAM,EAAE,GAAGC,MAAM,EAAE;EACtDD,MAAM,GAAG,IAAIF,SAAS,CAACE,MAAM,CAAC;EAC9BC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAACC,GAAG,CAAC;EACxBC,GAAG,EAAE,KAAK,MAAMC,KAAK,IAAIL,MAAM,EAAE;IAC/B,KAAK,MAAMM,KAAK,IAAIL,MAAM,EAAE;MAC1B,IAAI,CAACK,KAAK,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;QACrBL,MAAM,CAACQ,MAAM,CAACH,KAAK,CAAC;QACpB,SAASD,GAAG;MACd;IACF;EACF;EACA,OAAOJ,MAAM;AACf;AAEA,SAASG,GAAGA,CAACH,MAAM,EAAE;EACnB,OAAOA,MAAM,YAAYF,SAAS,GAAGE,MAAM,GAAG,IAAIF,SAAS,CAACE,MAAM,CAAC;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}