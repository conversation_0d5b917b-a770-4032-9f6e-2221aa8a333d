{"ast": null, "code": "var baseIsEqual = require('./_baseIsEqual'),\n  get = require('./get'),\n  hasIn = require('./hasIn'),\n  isKey = require('./_isKey'),\n  isStrictComparable = require('./_isStrictComparable'),\n  matchesStrictComparable = require('./_matchesStrictComparable'),\n  toKey = require('./_toKey');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n  COMPARE_UNORDERED_FLAG = 2;\n\n/**\n * The base implementation of `_.matchesProperty` which doesn't clone `srcValue`.\n *\n * @private\n * @param {string} path The path of the property to get.\n * @param {*} srcValue The value to match.\n * @returns {Function} Returns the new spec function.\n */\nfunction baseMatchesProperty(path, srcValue) {\n  if (isKey(path) && isStrictComparable(srcValue)) {\n    return matchesStrictComparable(toKey(path), srcValue);\n  }\n  return function (object) {\n    var objValue = get(object, path);\n    return objValue === undefined && objValue === srcValue ? hasIn(object, path) : baseIsEqual(srcValue, objValue, COMPARE_PARTIAL_FLAG | COMPARE_UNORDERED_FLAG);\n  };\n}\nmodule.exports = baseMatchesProperty;", "map": {"version": 3, "names": ["baseIsEqual", "require", "get", "hasIn", "is<PERSON>ey", "isStrictComparable", "matchesStrictComparable", "to<PERSON><PERSON>", "COMPARE_PARTIAL_FLAG", "COMPARE_UNORDERED_FLAG", "baseMatchesProperty", "path", "srcValue", "object", "objValue", "undefined", "module", "exports"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/lodash/_baseMatchesProperty.js"], "sourcesContent": ["var baseIsEqual = require('./_baseIsEqual'),\n    get = require('./get'),\n    hasIn = require('./hasIn'),\n    isKey = require('./_isKey'),\n    isStrictComparable = require('./_isStrictComparable'),\n    matchesStrictComparable = require('./_matchesStrictComparable'),\n    toKey = require('./_toKey');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n\n/**\n * The base implementation of `_.matchesProperty` which doesn't clone `srcValue`.\n *\n * @private\n * @param {string} path The path of the property to get.\n * @param {*} srcValue The value to match.\n * @returns {Function} Returns the new spec function.\n */\nfunction baseMatchesProperty(path, srcValue) {\n  if (isKey(path) && isStrictComparable(srcValue)) {\n    return matchesStrictComparable(toKey(path), srcValue);\n  }\n  return function(object) {\n    var objValue = get(object, path);\n    return (objValue === undefined && objValue === srcValue)\n      ? hasIn(object, path)\n      : baseIsEqual(srcValue, objValue, COMPARE_PARTIAL_FLAG | COMPARE_UNORDERED_FLAG);\n  };\n}\n\nmodule.exports = baseMatchesProperty;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;EACvCC,GAAG,GAAGD,OAAO,CAAC,OAAO,CAAC;EACtBE,KAAK,GAAGF,OAAO,CAAC,SAAS,CAAC;EAC1BG,KAAK,GAAGH,OAAO,CAAC,UAAU,CAAC;EAC3BI,kBAAkB,GAAGJ,OAAO,CAAC,uBAAuB,CAAC;EACrDK,uBAAuB,GAAGL,OAAO,CAAC,4BAA4B,CAAC;EAC/DM,KAAK,GAAGN,OAAO,CAAC,UAAU,CAAC;;AAE/B;AACA,IAAIO,oBAAoB,GAAG,CAAC;EACxBC,sBAAsB,GAAG,CAAC;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAC3C,IAAIR,KAAK,CAACO,IAAI,CAAC,IAAIN,kBAAkB,CAACO,QAAQ,CAAC,EAAE;IAC/C,OAAON,uBAAuB,CAACC,KAAK,CAACI,IAAI,CAAC,EAAEC,QAAQ,CAAC;EACvD;EACA,OAAO,UAASC,MAAM,EAAE;IACtB,IAAIC,QAAQ,GAAGZ,GAAG,CAACW,MAAM,EAAEF,IAAI,CAAC;IAChC,OAAQG,QAAQ,KAAKC,SAAS,IAAID,QAAQ,KAAKF,QAAQ,GACnDT,KAAK,CAACU,MAAM,EAAEF,IAAI,CAAC,GACnBX,WAAW,CAACY,QAAQ,EAAEE,QAAQ,EAAEN,oBAAoB,GAAGC,sBAAsB,CAAC;EACpF,CAAC;AACH;AAEAO,MAAM,CAACC,OAAO,GAAGP,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}