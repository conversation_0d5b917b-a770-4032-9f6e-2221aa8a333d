{"ast": null, "code": "/**\n * Given an array and a number N, return a new array which contains every nTh\n * element of the input array. For n below 1, an empty array is returned.\n * If isValid is provided, all candidates must suffice the condition, else undefined is returned.\n * @param {T[]} array An input array.\n * @param {integer} n A number\n * @param {Function} isValid A function to evaluate a candidate form the array\n * @returns {T[]} The result array of the same type as the input array.\n */\nexport function getEveryNthWithCondition(array, n, isValid) {\n  if (n < 1) {\n    return [];\n  }\n  if (n === 1 && isValid === undefined) {\n    return array;\n  }\n  var result = [];\n  for (var i = 0; i < array.length; i += n) {\n    if (isValid === undefined || isValid(array[i]) === true) {\n      result.push(array[i]);\n    } else {\n      return undefined;\n    }\n  }\n  return result;\n}", "map": {"version": 3, "names": ["getEveryNthWithCondition", "array", "n", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "result", "i", "length", "push"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/recharts/es6/util/getEveryNthWithCondition.js"], "sourcesContent": ["/**\n * Given an array and a number N, return a new array which contains every nTh\n * element of the input array. For n below 1, an empty array is returned.\n * If isValid is provided, all candidates must suffice the condition, else undefined is returned.\n * @param {T[]} array An input array.\n * @param {integer} n A number\n * @param {Function} isValid A function to evaluate a candidate form the array\n * @returns {T[]} The result array of the same type as the input array.\n */\nexport function getEveryNthWithCondition(array, n, isValid) {\n  if (n < 1) {\n    return [];\n  }\n  if (n === 1 && isValid === undefined) {\n    return array;\n  }\n  var result = [];\n  for (var i = 0; i < array.length; i += n) {\n    if (isValid === undefined || isValid(array[i]) === true) {\n      result.push(array[i]);\n    } else {\n      return undefined;\n    }\n  }\n  return result;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,wBAAwBA,CAACC,KAAK,EAAEC,CAAC,EAAEC,OAAO,EAAE;EAC1D,IAAID,CAAC,GAAG,CAAC,EAAE;IACT,OAAO,EAAE;EACX;EACA,IAAIA,CAAC,KAAK,CAAC,IAAIC,OAAO,KAAKC,SAAS,EAAE;IACpC,OAAOH,KAAK;EACd;EACA,IAAII,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACM,MAAM,EAAED,CAAC,IAAIJ,CAAC,EAAE;IACxC,IAAIC,OAAO,KAAKC,SAAS,IAAID,OAAO,CAACF,KAAK,CAACK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;MACvDD,MAAM,CAACG,IAAI,CAACP,KAAK,CAACK,CAAC,CAAC,CAAC;IACvB,CAAC,MAAM;MACL,OAAOF,SAAS;IAClB;EACF;EACA,OAAOC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}