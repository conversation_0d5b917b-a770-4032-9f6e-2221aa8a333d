{"ast": null, "code": "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "ReactIs", "require", "throwOnDirectAccess", "module", "exports", "isElement"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/prop-types/index.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,IAAIC,OAAO,GAAGC,OAAO,CAAC,UAAU,CAAC;;EAEjC;EACA;EACA,IAAIC,mBAAmB,GAAG,IAAI;EAC9BC,MAAM,CAACC,OAAO,GAAGH,OAAO,CAAC,2BAA2B,CAAC,CAACD,OAAO,CAACK,SAAS,EAAEH,mBAAmB,CAAC;AAC/F,CAAC,MAAM;EACL;EACA;EACAC,MAAM,CAACC,OAAO,GAAGH,OAAO,CAAC,4BAA4B,CAAC,CAAC,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}