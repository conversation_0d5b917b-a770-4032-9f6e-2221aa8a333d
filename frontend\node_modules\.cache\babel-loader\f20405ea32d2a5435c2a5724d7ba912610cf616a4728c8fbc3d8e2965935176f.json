{"ast": null, "code": "var _jsxFileName = \"D:\\\\MobioProjects\\\\iot-poc\\\\frontend\\\\src\\\\components\\\\Chart.js\";\nimport React from \"react\";\nimport { Line<PERSON><PERSON>, Line, XAxis, YAxis, Tooltip, CartesianGrid } from \"recharts\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Chart({\n  data\n}) {\n  return /*#__PURE__*/_jsxDEV(LineChart, {\n    width: 800,\n    height: 400,\n    data: data,\n    children: [/*#__PURE__*/_jsxDEV(XAxis, {\n      dataKey: \"timestamp\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CartesianGrid, {\n      stroke: \"#ccc\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Line, {\n      type: \"monotone\",\n      dataKey: \"temperature\",\n      stroke: \"#ff7300\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Line, {\n      type: \"monotone\",\n      dataKey: \"humidity\",\n      stroke: \"#387908\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_c = Chart;\nvar _c;\n$RefreshReg$(_c, \"Chart\");", "map": {"version": 3, "names": ["React", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "Cartesian<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Chart", "data", "width", "height", "children", "dataKey", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stroke", "type", "_c", "$RefreshReg$"], "sources": ["D:/MobioProjects/iot-poc/frontend/src/components/Chart.js"], "sourcesContent": ["import React from \"react\";\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  Line,\r\n  XAxis,\r\n  <PERSON><PERSON><PERSON>s,\r\n  <PERSON>ltip,\r\n  CartesianGrid,\r\n} from \"recharts\";\r\n\r\nexport default function Chart({ data }) {\r\n  return (\r\n    <LineChart width={800} height={400} data={data}>\r\n      <XAxis dataKey=\"timestamp\" />\r\n      <YAxis />\r\n      <Tooltip />\r\n      <CartesianGrid stroke=\"#ccc\" />\r\n      <Line type=\"monotone\" dataKey=\"temperature\" stroke=\"#ff7300\" />\r\n      <Line type=\"monotone\" dataKey=\"humidity\" stroke=\"#387908\" />\r\n    </LineChart>\r\n  );\r\n}\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,aAAa,QACR,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElB,eAAe,SAASC,KAAKA,CAAC;EAAEC;AAAK,CAAC,EAAE;EACtC,oBACEF,OAAA,CAACP,SAAS;IAACU,KAAK,EAAE,GAAI;IAACC,MAAM,EAAE,GAAI;IAACF,IAAI,EAAEA,IAAK;IAAAG,QAAA,gBAC7CL,OAAA,CAACL,KAAK;MAACW,OAAO,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7BV,OAAA,CAACJ,KAAK;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACTV,OAAA,CAACH,OAAO;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXV,OAAA,CAACF,aAAa;MAACa,MAAM,EAAC;IAAM;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC/BV,OAAA,CAACN,IAAI;MAACkB,IAAI,EAAC,UAAU;MAACN,OAAO,EAAC,aAAa;MAACK,MAAM,EAAC;IAAS;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC/DV,OAAA,CAACN,IAAI;MAACkB,IAAI,EAAC,UAAU;MAACN,OAAO,EAAC,UAAU;MAACK,MAAM,EAAC;IAAS;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnD,CAAC;AAEhB;AAACG,EAAA,GAXuBZ,KAAK;AAAA,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}