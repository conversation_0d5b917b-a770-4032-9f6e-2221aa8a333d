{"ast": null, "code": "import { InternMap } from \"d3-array\";\nimport { initRange } from \"./init.js\";\nexport const implicit = Symbol(\"implicit\");\nexport default function ordinal() {\n  var index = new InternMap(),\n    domain = [],\n    range = [],\n    unknown = implicit;\n  function scale(d) {\n    let i = index.get(d);\n    if (i === undefined) {\n      if (unknown !== implicit) return unknown;\n      index.set(d, i = domain.push(d) - 1);\n    }\n    return range[i % range.length];\n  }\n  scale.domain = function (_) {\n    if (!arguments.length) return domain.slice();\n    domain = [], index = new InternMap();\n    for (const value of _) {\n      if (index.has(value)) continue;\n      index.set(value, domain.push(value) - 1);\n    }\n    return scale;\n  };\n  scale.range = function (_) {\n    return arguments.length ? (range = Array.from(_), scale) : range.slice();\n  };\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  scale.copy = function () {\n    return ordinal(domain, range).unknown(unknown);\n  };\n  initRange.apply(scale, arguments);\n  return scale;\n}", "map": {"version": 3, "names": ["InternMap", "initRange", "implicit", "Symbol", "ordinal", "index", "domain", "range", "unknown", "scale", "d", "i", "get", "undefined", "set", "push", "length", "_", "arguments", "slice", "value", "has", "Array", "from", "copy", "apply"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/d3-scale/src/ordinal.js"], "sourcesContent": ["import {InternMap} from \"d3-array\";\nimport {initRange} from \"./init.js\";\n\nexport const implicit = Symbol(\"implicit\");\n\nexport default function ordinal() {\n  var index = new InternMap(),\n      domain = [],\n      range = [],\n      unknown = implicit;\n\n  function scale(d) {\n    let i = index.get(d);\n    if (i === undefined) {\n      if (unknown !== implicit) return unknown;\n      index.set(d, i = domain.push(d) - 1);\n    }\n    return range[i % range.length];\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [], index = new InternMap();\n    for (const value of _) {\n      if (index.has(value)) continue;\n      index.set(value, domain.push(value) - 1);\n    }\n    return scale;\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), scale) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return ordinal(domain, range).unknown(unknown);\n  };\n\n  initRange.apply(scale, arguments);\n\n  return scale;\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,UAAU;AAClC,SAAQC,SAAS,QAAO,WAAW;AAEnC,OAAO,MAAMC,QAAQ,GAAGC,MAAM,CAAC,UAAU,CAAC;AAE1C,eAAe,SAASC,OAAOA,CAAA,EAAG;EAChC,IAAIC,KAAK,GAAG,IAAIL,SAAS,CAAC,CAAC;IACvBM,MAAM,GAAG,EAAE;IACXC,KAAK,GAAG,EAAE;IACVC,OAAO,GAAGN,QAAQ;EAEtB,SAASO,KAAKA,CAACC,CAAC,EAAE;IAChB,IAAIC,CAAC,GAAGN,KAAK,CAACO,GAAG,CAACF,CAAC,CAAC;IACpB,IAAIC,CAAC,KAAKE,SAAS,EAAE;MACnB,IAAIL,OAAO,KAAKN,QAAQ,EAAE,OAAOM,OAAO;MACxCH,KAAK,CAACS,GAAG,CAACJ,CAAC,EAAEC,CAAC,GAAGL,MAAM,CAACS,IAAI,CAACL,CAAC,CAAC,GAAG,CAAC,CAAC;IACtC;IACA,OAAOH,KAAK,CAACI,CAAC,GAAGJ,KAAK,CAACS,MAAM,CAAC;EAChC;EAEAP,KAAK,CAACH,MAAM,GAAG,UAASW,CAAC,EAAE;IACzB,IAAI,CAACC,SAAS,CAACF,MAAM,EAAE,OAAOV,MAAM,CAACa,KAAK,CAAC,CAAC;IAC5Cb,MAAM,GAAG,EAAE,EAAED,KAAK,GAAG,IAAIL,SAAS,CAAC,CAAC;IACpC,KAAK,MAAMoB,KAAK,IAAIH,CAAC,EAAE;MACrB,IAAIZ,KAAK,CAACgB,GAAG,CAACD,KAAK,CAAC,EAAE;MACtBf,KAAK,CAACS,GAAG,CAACM,KAAK,EAAEd,MAAM,CAACS,IAAI,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC;IAC1C;IACA,OAAOX,KAAK;EACd,CAAC;EAEDA,KAAK,CAACF,KAAK,GAAG,UAASU,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACF,MAAM,IAAIT,KAAK,GAAGe,KAAK,CAACC,IAAI,CAACN,CAAC,CAAC,EAAER,KAAK,IAAIF,KAAK,CAACY,KAAK,CAAC,CAAC;EAC1E,CAAC;EAEDV,KAAK,CAACD,OAAO,GAAG,UAASS,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACF,MAAM,IAAIR,OAAO,GAAGS,CAAC,EAAER,KAAK,IAAID,OAAO;EAC1D,CAAC;EAEDC,KAAK,CAACe,IAAI,GAAG,YAAW;IACtB,OAAOpB,OAAO,CAACE,MAAM,EAAEC,KAAK,CAAC,CAACC,OAAO,CAACA,OAAO,CAAC;EAChD,CAAC;EAEDP,SAAS,CAACwB,KAAK,CAAChB,KAAK,EAAES,SAAS,CAAC;EAEjC,OAAOT,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}