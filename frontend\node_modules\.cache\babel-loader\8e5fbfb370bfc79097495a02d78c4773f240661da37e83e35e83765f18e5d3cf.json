{"ast": null, "code": "var Symbol = require('./_Symbol'),\n  isArguments = require('./isArguments'),\n  isArray = require('./isArray');\n\n/** Built-in value references. */\nvar spreadableSymbol = Symbol ? Symbol.isConcatSpreadable : undefined;\n\n/**\n * Checks if `value` is a flattenable `arguments` object or array.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is flattenable, else `false`.\n */\nfunction isFlattenable(value) {\n  return isArray(value) || isArguments(value) || !!(spreadableSymbol && value && value[spreadableSymbol]);\n}\nmodule.exports = isFlattenable;", "map": {"version": 3, "names": ["Symbol", "require", "isArguments", "isArray", "spreadableSymbol", "isConcatSpreadable", "undefined", "isFlattenable", "value", "module", "exports"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/lodash/_isFlattenable.js"], "sourcesContent": ["var Symbol = require('./_Symbol'),\n    isArguments = require('./isArguments'),\n    isArray = require('./isArray');\n\n/** Built-in value references. */\nvar spreadableSymbol = Symbol ? Symbol.isConcatSpreadable : undefined;\n\n/**\n * Checks if `value` is a flattenable `arguments` object or array.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is flattenable, else `false`.\n */\nfunction isFlattenable(value) {\n  return isArray(value) || isArguments(value) ||\n    !!(spreadableSymbol && value && value[spreadableSymbol]);\n}\n\nmodule.exports = isFlattenable;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,WAAW,CAAC;EAC7BC,WAAW,GAAGD,OAAO,CAAC,eAAe,CAAC;EACtCE,OAAO,GAAGF,OAAO,CAAC,WAAW,CAAC;;AAElC;AACA,IAAIG,gBAAgB,GAAGJ,MAAM,GAAGA,MAAM,CAACK,kBAAkB,GAAGC,SAAS;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOL,OAAO,CAACK,KAAK,CAAC,IAAIN,WAAW,CAACM,KAAK,CAAC,IACzC,CAAC,EAAEJ,gBAAgB,IAAII,KAAK,IAAIA,KAAK,CAACJ,gBAAgB,CAAC,CAAC;AAC5D;AAEAK,MAAM,CAACC,OAAO,GAAGH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}