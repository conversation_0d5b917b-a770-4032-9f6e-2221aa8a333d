{"ast": null, "code": "var _excluded = [\"item\"],\n  _excluded2 = [\"children\", \"className\", \"width\", \"height\", \"style\", \"compact\", \"title\", \"desc\"];\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport React, { Component, cloneElement, isValidElement, forwardRef } from 'react';\nimport isNil from 'lodash/isNil';\nimport isFunction from 'lodash/isFunction';\nimport range from 'lodash/range';\nimport get from 'lodash/get';\nimport sortBy from 'lodash/sortBy';\nimport throttle from 'lodash/throttle';\nimport clsx from 'clsx';\n// eslint-disable-next-line no-restricted-imports\n\nimport invariant from 'tiny-invariant';\nimport { Surface } from '../container/Surface';\nimport { Layer } from '../container/Layer';\nimport { Tooltip } from '../component/Tooltip';\nimport { Legend } from '../component/Legend';\nimport { Dot } from '../shape/Dot';\nimport { isInRectangle } from '../shape/Rectangle';\nimport { filterProps, findAllByType, findChildByType, getDisplayName, getReactEventByType, isChildrenEqual, parseChildIndex, renderByOrder, validateWidthHeight } from '../util/ReactUtils';\nimport { Brush } from '../cartesian/Brush';\nimport { getOffset } from '../util/DOMUtils';\nimport { findEntryInArray, getAnyElementOfObject, hasDuplicate, isNumber, uniqueId } from '../util/DataUtils';\nimport { appendOffsetOfLegend, calculateActiveTickIndex, combineEventHandlers, getBandSizeOfAxis, getBarPosition, getBarSizeList, getDomainOfDataByKey, getDomainOfItemsWithSameAxis, getDomainOfStackGroups, getLegendProps, getMainColorOfGraphicItem, getStackedDataOfItem, getStackGroupsByAxisId, getTicksOfAxis, getTooltipItem, isCategoricalAxis, parseDomainOfCategoryAxis, parseErrorBarsOfAxis, parseSpecifiedDomain } from '../util/ChartUtils';\nimport { detectReferenceElementsDomain } from '../util/DetectReferenceElementsDomain';\nimport { inRangeOfSector, polarToCartesian } from '../util/PolarUtils';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { eventCenter, SYNC_EVENT } from '../util/Events';\nimport { adaptEventHandlers } from '../util/types';\nimport { AccessibilityManager } from './AccessibilityManager';\nimport { isDomainSpecifiedByUser } from '../util/isDomainSpecifiedByUser';\nimport { getActiveShapeIndexForTooltip, isFunnel, isPie, isScatter } from '../util/ActiveShapeUtils';\nimport { Cursor } from '../component/Cursor';\nimport { ChartLayoutContextProvider } from '../context/chartLayoutContext';\nvar ORIENT_MAP = {\n  xAxis: ['bottom', 'top'],\n  yAxis: ['left', 'right']\n};\nvar FULL_WIDTH_AND_HEIGHT = {\n  width: '100%',\n  height: '100%'\n};\nvar originCoordinate = {\n  x: 0,\n  y: 0\n};\n\n/**\n * This function exists as a temporary workaround.\n *\n * Why? generateCategoricalChart does not render `{children}` directly;\n * instead it passes them through `renderByOrder` function which reads their handlers.\n *\n * So, this is a handler that does nothing.\n * Once we get rid of `renderByOrder` and switch to JSX only, we can get rid of this handler too.\n *\n * @param {JSX} element as is in JSX\n * @returns {JSX} the same element\n */\nfunction renderAsIs(element) {\n  return element;\n}\nvar calculateTooltipPos = function calculateTooltipPos(rangeObj, layout) {\n  if (layout === 'horizontal') {\n    return rangeObj.x;\n  }\n  if (layout === 'vertical') {\n    return rangeObj.y;\n  }\n  if (layout === 'centric') {\n    return rangeObj.angle;\n  }\n  return rangeObj.radius;\n};\nvar getActiveCoordinate = function getActiveCoordinate(layout, tooltipTicks, activeIndex, rangeObj) {\n  var entry = tooltipTicks.find(function (tick) {\n    return tick && tick.index === activeIndex;\n  });\n  if (entry) {\n    if (layout === 'horizontal') {\n      return {\n        x: entry.coordinate,\n        y: rangeObj.y\n      };\n    }\n    if (layout === 'vertical') {\n      return {\n        x: rangeObj.x,\n        y: entry.coordinate\n      };\n    }\n    if (layout === 'centric') {\n      var _angle = entry.coordinate;\n      var _radius = rangeObj.radius;\n      return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), polarToCartesian(rangeObj.cx, rangeObj.cy, _radius, _angle)), {}, {\n        angle: _angle,\n        radius: _radius\n      });\n    }\n    var radius = entry.coordinate;\n    var angle = rangeObj.angle;\n    return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), polarToCartesian(rangeObj.cx, rangeObj.cy, radius, angle)), {}, {\n      angle: angle,\n      radius: radius\n    });\n  }\n  return originCoordinate;\n};\nvar getDisplayedData = function getDisplayedData(data, _ref) {\n  var graphicalItems = _ref.graphicalItems,\n    dataStartIndex = _ref.dataStartIndex,\n    dataEndIndex = _ref.dataEndIndex;\n  var itemsData = (graphicalItems !== null && graphicalItems !== void 0 ? graphicalItems : []).reduce(function (result, child) {\n    var itemData = child.props.data;\n    if (itemData && itemData.length) {\n      return [].concat(_toConsumableArray(result), _toConsumableArray(itemData));\n    }\n    return result;\n  }, []);\n  if (itemsData.length > 0) {\n    return itemsData;\n  }\n  if (data && data.length && isNumber(dataStartIndex) && isNumber(dataEndIndex)) {\n    return data.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  return [];\n};\nfunction getDefaultDomainByAxisType(axisType) {\n  return axisType === 'number' ? [0, 'auto'] : undefined;\n}\n\n/**\n * Get the content to be displayed in the tooltip\n * @param  {Object} state          Current state\n * @param  {Array}  chartData      The data defined in chart\n * @param  {Number} activeIndex    Active index of data\n * @param  {String} activeLabel    Active label of data\n * @return {Array}                 The content of tooltip\n */\nvar getTooltipContent = function getTooltipContent(state, chartData, activeIndex, activeLabel) {\n  var graphicalItems = state.graphicalItems,\n    tooltipAxis = state.tooltipAxis;\n  var displayedData = getDisplayedData(chartData, state);\n  if (activeIndex < 0 || !graphicalItems || !graphicalItems.length || activeIndex >= displayedData.length) {\n    return null;\n  }\n  // get data by activeIndex when the axis don't allow duplicated category\n  return graphicalItems.reduce(function (result, child) {\n    var _child$props$data;\n    /**\n     * Fixes: https://github.com/recharts/recharts/issues/3669\n     * Defaulting to chartData below to fix an edge case where the tooltip does not include data from all charts\n     * when a separate dataset is passed to chart prop data and specified on Line/Area/etc prop data\n     */\n    var data = (_child$props$data = child.props.data) !== null && _child$props$data !== void 0 ? _child$props$data : chartData;\n    if (data && state.dataStartIndex + state.dataEndIndex !== 0 &&\n    // https://github.com/recharts/recharts/issues/4717\n    // The data is sliced only when the active index is within the start/end index range.\n    state.dataEndIndex - state.dataStartIndex >= activeIndex) {\n      data = data.slice(state.dataStartIndex, state.dataEndIndex + 1);\n    }\n    var payload;\n    if (tooltipAxis.dataKey && !tooltipAxis.allowDuplicatedCategory) {\n      // graphic child has data props\n      var entries = data === undefined ? displayedData : data;\n      payload = findEntryInArray(entries, tooltipAxis.dataKey, activeLabel);\n    } else {\n      payload = data && data[activeIndex] || displayedData[activeIndex];\n    }\n    if (!payload) {\n      return result;\n    }\n    return [].concat(_toConsumableArray(result), [getTooltipItem(child, payload)]);\n  }, []);\n};\n\n/**\n * Returns tooltip data based on a mouse position (as a parameter or in state)\n * @param  {Object} state     current state\n * @param  {Array}  chartData the data defined in chart\n * @param  {String} layout     The layout type of chart\n * @param  {Object} rangeObj  { x, y } coordinates\n * @return {Object}           Tooltip data data\n */\nvar getTooltipData = function getTooltipData(state, chartData, layout, rangeObj) {\n  var rangeData = rangeObj || {\n    x: state.chartX,\n    y: state.chartY\n  };\n  var pos = calculateTooltipPos(rangeData, layout);\n  var ticks = state.orderedTooltipTicks,\n    axis = state.tooltipAxis,\n    tooltipTicks = state.tooltipTicks;\n  var activeIndex = calculateActiveTickIndex(pos, ticks, tooltipTicks, axis);\n  if (activeIndex >= 0 && tooltipTicks) {\n    var activeLabel = tooltipTicks[activeIndex] && tooltipTicks[activeIndex].value;\n    var activePayload = getTooltipContent(state, chartData, activeIndex, activeLabel);\n    var activeCoordinate = getActiveCoordinate(layout, ticks, activeIndex, rangeData);\n    return {\n      activeTooltipIndex: activeIndex,\n      activeLabel: activeLabel,\n      activePayload: activePayload,\n      activeCoordinate: activeCoordinate\n    };\n  }\n  return null;\n};\n\n/**\n * Get the configuration of axis by the options of axis instance\n * @param  {Object} props         Latest props\n * @param {Array}  axes           The instance of axes\n * @param  {Array} graphicalItems The instances of item\n * @param  {String} axisType      The type of axis, xAxis - x-axis, yAxis - y-axis\n * @param  {String} axisIdKey     The unique id of an axis\n * @param  {Object} stackGroups   The items grouped by axisId and stackId\n * @param {Number} dataStartIndex The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex   The end index of the data series when a brush is applied\n * @return {Object}      Configuration\n */\nexport var getAxisMapByAxes = function getAxisMapByAxes(props, _ref2) {\n  var axes = _ref2.axes,\n    graphicalItems = _ref2.graphicalItems,\n    axisType = _ref2.axisType,\n    axisIdKey = _ref2.axisIdKey,\n    stackGroups = _ref2.stackGroups,\n    dataStartIndex = _ref2.dataStartIndex,\n    dataEndIndex = _ref2.dataEndIndex;\n  var layout = props.layout,\n    children = props.children,\n    stackOffset = props.stackOffset;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n\n  // Eliminate duplicated axes\n  return axes.reduce(function (result, child) {\n    var _childProps$domain2;\n    var childProps = child.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, child.type.defaultProps), child.props) : child.props;\n    var type = childProps.type,\n      dataKey = childProps.dataKey,\n      allowDataOverflow = childProps.allowDataOverflow,\n      allowDuplicatedCategory = childProps.allowDuplicatedCategory,\n      scale = childProps.scale,\n      ticks = childProps.ticks,\n      includeHidden = childProps.includeHidden;\n    var axisId = childProps[axisIdKey];\n    if (result[axisId]) {\n      return result;\n    }\n    var displayedData = getDisplayedData(props.data, {\n      graphicalItems: graphicalItems.filter(function (item) {\n        var _defaultProps;\n        var itemAxisId = axisIdKey in item.props ? item.props[axisIdKey] : (_defaultProps = item.type.defaultProps) === null || _defaultProps === void 0 ? void 0 : _defaultProps[axisIdKey];\n        return itemAxisId === axisId;\n      }),\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex\n    });\n    var len = displayedData.length;\n    var domain, duplicateDomain, categoricalDomain;\n\n    /*\n     * This is a hack to short-circuit the domain creation here to enhance performance.\n     * Usually, the data is used to determine the domain, but when the user specifies\n     * a domain upfront (via props), there is no need to calculate the domain start and end,\n     * which is very expensive for a larger amount of data.\n     * The only thing that would prohibit short-circuiting is when the user doesn't allow data overflow,\n     * because the axis is supposed to ignore the specified domain that way.\n     */\n    if (isDomainSpecifiedByUser(childProps.domain, allowDataOverflow, type)) {\n      domain = parseSpecifiedDomain(childProps.domain, null, allowDataOverflow);\n      /* The chart can be categorical and have the domain specified in numbers\n       * we still need to calculate the categorical domain\n       * TODO: refactor this more\n       */\n      if (isCategorical && (type === 'number' || scale !== 'auto')) {\n        categoricalDomain = getDomainOfDataByKey(displayedData, dataKey, 'category');\n      }\n    }\n\n    // if the domain is defaulted we need this for `originalDomain` as well\n    var defaultDomain = getDefaultDomainByAxisType(type);\n\n    // we didn't create the domain from user's props above, so we need to calculate it\n    if (!domain || domain.length === 0) {\n      var _childProps$domain;\n      var childDomain = (_childProps$domain = childProps.domain) !== null && _childProps$domain !== void 0 ? _childProps$domain : defaultDomain;\n      if (dataKey) {\n        // has dataKey in <Axis />\n        domain = getDomainOfDataByKey(displayedData, dataKey, type);\n        if (type === 'category' && isCategorical) {\n          // the field type is category data and this axis is categorical axis\n          var duplicate = hasDuplicate(domain);\n          if (allowDuplicatedCategory && duplicate) {\n            duplicateDomain = domain;\n            // When category axis has duplicated text, serial numbers are used to generate scale\n            domain = range(0, len);\n          } else if (!allowDuplicatedCategory) {\n            // remove duplicated category\n            domain = parseDomainOfCategoryAxis(childDomain, domain, child).reduce(function (finalDomain, entry) {\n              return finalDomain.indexOf(entry) >= 0 ? finalDomain : [].concat(_toConsumableArray(finalDomain), [entry]);\n            }, []);\n          }\n        } else if (type === 'category') {\n          // the field type is category data and this axis is numerical axis\n          if (!allowDuplicatedCategory) {\n            domain = parseDomainOfCategoryAxis(childDomain, domain, child).reduce(function (finalDomain, entry) {\n              return finalDomain.indexOf(entry) >= 0 || entry === '' || isNil(entry) ? finalDomain : [].concat(_toConsumableArray(finalDomain), [entry]);\n            }, []);\n          } else {\n            // eliminate undefined or null or empty string\n            domain = domain.filter(function (entry) {\n              return entry !== '' && !isNil(entry);\n            });\n          }\n        } else if (type === 'number') {\n          // the field type is numerical\n          var errorBarsDomain = parseErrorBarsOfAxis(displayedData, graphicalItems.filter(function (item) {\n            var _defaultProps2, _defaultProps3;\n            var itemAxisId = axisIdKey in item.props ? item.props[axisIdKey] : (_defaultProps2 = item.type.defaultProps) === null || _defaultProps2 === void 0 ? void 0 : _defaultProps2[axisIdKey];\n            var itemHide = 'hide' in item.props ? item.props.hide : (_defaultProps3 = item.type.defaultProps) === null || _defaultProps3 === void 0 ? void 0 : _defaultProps3.hide;\n            return itemAxisId === axisId && (includeHidden || !itemHide);\n          }), dataKey, axisType, layout);\n          if (errorBarsDomain) {\n            domain = errorBarsDomain;\n          }\n        }\n        if (isCategorical && (type === 'number' || scale !== 'auto')) {\n          categoricalDomain = getDomainOfDataByKey(displayedData, dataKey, 'category');\n        }\n      } else if (isCategorical) {\n        // the axis is a categorical axis\n        domain = range(0, len);\n      } else if (stackGroups && stackGroups[axisId] && stackGroups[axisId].hasStack && type === 'number') {\n        // when stackOffset is 'expand', the domain may be calculated as [0, 1.000000000002]\n        domain = stackOffset === 'expand' ? [0, 1] : getDomainOfStackGroups(stackGroups[axisId].stackGroups, dataStartIndex, dataEndIndex);\n      } else {\n        domain = getDomainOfItemsWithSameAxis(displayedData, graphicalItems.filter(function (item) {\n          var itemAxisId = axisIdKey in item.props ? item.props[axisIdKey] : item.type.defaultProps[axisIdKey];\n          var itemHide = 'hide' in item.props ? item.props.hide : item.type.defaultProps.hide;\n          return itemAxisId === axisId && (includeHidden || !itemHide);\n        }), type, layout, true);\n      }\n      if (type === 'number') {\n        // To detect wether there is any reference lines whose props alwaysShow is true\n        domain = detectReferenceElementsDomain(children, domain, axisId, axisType, ticks);\n        if (childDomain) {\n          domain = parseSpecifiedDomain(childDomain, domain, allowDataOverflow);\n        }\n      } else if (type === 'category' && childDomain) {\n        var axisDomain = childDomain;\n        var isDomainValid = domain.every(function (entry) {\n          return axisDomain.indexOf(entry) >= 0;\n        });\n        if (isDomainValid) {\n          domain = axisDomain;\n        }\n      }\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, _objectSpread(_objectSpread({}, childProps), {}, {\n      axisType: axisType,\n      domain: domain,\n      categoricalDomain: categoricalDomain,\n      duplicateDomain: duplicateDomain,\n      originalDomain: (_childProps$domain2 = childProps.domain) !== null && _childProps$domain2 !== void 0 ? _childProps$domain2 : defaultDomain,\n      isCategorical: isCategorical,\n      layout: layout\n    })));\n  }, {});\n};\n\n/**\n * Get the configuration of axis by the options of item,\n * this kind of axis does not display in chart\n * @param  {Object} props         Latest props\n * @param  {Array} graphicalItems The instances of item\n * @param  {ReactElement} Axis    Axis Component\n * @param  {String} axisType      The type of axis, xAxis - x-axis, yAxis - y-axis\n * @param  {String} axisIdKey     The unique id of an axis\n * @param  {Object} stackGroups   The items grouped by axisId and stackId\n * @param {Number} dataStartIndex The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex   The end index of the data series when a brush is applied\n * @return {Object}               Configuration\n */\nvar getAxisMapByItems = function getAxisMapByItems(props, _ref3) {\n  var graphicalItems = _ref3.graphicalItems,\n    Axis = _ref3.Axis,\n    axisType = _ref3.axisType,\n    axisIdKey = _ref3.axisIdKey,\n    stackGroups = _ref3.stackGroups,\n    dataStartIndex = _ref3.dataStartIndex,\n    dataEndIndex = _ref3.dataEndIndex;\n  var layout = props.layout,\n    children = props.children;\n  var displayedData = getDisplayedData(props.data, {\n    graphicalItems: graphicalItems,\n    dataStartIndex: dataStartIndex,\n    dataEndIndex: dataEndIndex\n  });\n  var len = displayedData.length;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var index = -1;\n\n  // The default type of x-axis is category axis,\n  // The default contents of x-axis is the serial numbers of data\n  // The default type of y-axis is number axis\n  // The default contents of y-axis is the domain of data\n  return graphicalItems.reduce(function (result, child) {\n    var childProps = child.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, child.type.defaultProps), child.props) : child.props;\n    var axisId = childProps[axisIdKey];\n    var originalDomain = getDefaultDomainByAxisType('number');\n    if (!result[axisId]) {\n      index++;\n      var domain;\n      if (isCategorical) {\n        domain = range(0, len);\n      } else if (stackGroups && stackGroups[axisId] && stackGroups[axisId].hasStack) {\n        domain = getDomainOfStackGroups(stackGroups[axisId].stackGroups, dataStartIndex, dataEndIndex);\n        domain = detectReferenceElementsDomain(children, domain, axisId, axisType);\n      } else {\n        domain = parseSpecifiedDomain(originalDomain, getDomainOfItemsWithSameAxis(displayedData, graphicalItems.filter(function (item) {\n          var _defaultProps4, _defaultProps5;\n          var itemAxisId = axisIdKey in item.props ? item.props[axisIdKey] : (_defaultProps4 = item.type.defaultProps) === null || _defaultProps4 === void 0 ? void 0 : _defaultProps4[axisIdKey];\n          var itemHide = 'hide' in item.props ? item.props.hide : (_defaultProps5 = item.type.defaultProps) === null || _defaultProps5 === void 0 ? void 0 : _defaultProps5.hide;\n          return itemAxisId === axisId && !itemHide;\n        }), 'number', layout), Axis.defaultProps.allowDataOverflow);\n        domain = detectReferenceElementsDomain(children, domain, axisId, axisType);\n      }\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, _objectSpread(_objectSpread({\n        axisType: axisType\n      }, Axis.defaultProps), {}, {\n        hide: true,\n        orientation: get(ORIENT_MAP, \"\".concat(axisType, \".\").concat(index % 2), null),\n        domain: domain,\n        originalDomain: originalDomain,\n        isCategorical: isCategorical,\n        layout: layout\n        // specify scale when no Axis\n        // scale: isCategorical ? 'band' : 'linear',\n      })));\n    }\n    return result;\n  }, {});\n};\n\n/**\n * Get the configuration of all x-axis or y-axis\n * @param  {Object} props          Latest props\n * @param  {String} axisType       The type of axis\n * @param  {React.ComponentType}  [AxisComp]      Axis Component\n * @param  {Array}  graphicalItems The instances of item\n * @param  {Object} stackGroups    The items grouped by axisId and stackId\n * @param {Number} dataStartIndex  The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex    The end index of the data series when a brush is applied\n * @return {Object}          Configuration\n */\nvar getAxisMap = function getAxisMap(props, _ref4) {\n  var _ref4$axisType = _ref4.axisType,\n    axisType = _ref4$axisType === void 0 ? 'xAxis' : _ref4$axisType,\n    AxisComp = _ref4.AxisComp,\n    graphicalItems = _ref4.graphicalItems,\n    stackGroups = _ref4.stackGroups,\n    dataStartIndex = _ref4.dataStartIndex,\n    dataEndIndex = _ref4.dataEndIndex;\n  var children = props.children;\n  var axisIdKey = \"\".concat(axisType, \"Id\");\n  // Get all the instance of Axis\n  var axes = findAllByType(children, AxisComp);\n  var axisMap = {};\n  if (axes && axes.length) {\n    axisMap = getAxisMapByAxes(props, {\n      axes: axes,\n      graphicalItems: graphicalItems,\n      axisType: axisType,\n      axisIdKey: axisIdKey,\n      stackGroups: stackGroups,\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex\n    });\n  } else if (graphicalItems && graphicalItems.length) {\n    axisMap = getAxisMapByItems(props, {\n      Axis: AxisComp,\n      graphicalItems: graphicalItems,\n      axisType: axisType,\n      axisIdKey: axisIdKey,\n      stackGroups: stackGroups,\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex\n    });\n  }\n  return axisMap;\n};\nvar tooltipTicksGenerator = function tooltipTicksGenerator(axisMap) {\n  var axis = getAnyElementOfObject(axisMap);\n  var tooltipTicks = getTicksOfAxis(axis, false, true);\n  return {\n    tooltipTicks: tooltipTicks,\n    orderedTooltipTicks: sortBy(tooltipTicks, function (o) {\n      return o.coordinate;\n    }),\n    tooltipAxis: axis,\n    tooltipAxisBandSize: getBandSizeOfAxis(axis, tooltipTicks)\n  };\n};\n\n/**\n * Returns default, reset state for the categorical chart.\n * @param {Object} props Props object to use when creating the default state\n * @return {Object} Whole new state\n */\nexport var createDefaultState = function createDefaultState(props) {\n  var children = props.children,\n    defaultShowTooltip = props.defaultShowTooltip;\n  var brushItem = findChildByType(children, Brush);\n  var startIndex = 0;\n  var endIndex = 0;\n  if (props.data && props.data.length !== 0) {\n    endIndex = props.data.length - 1;\n  }\n  if (brushItem && brushItem.props) {\n    if (brushItem.props.startIndex >= 0) {\n      startIndex = brushItem.props.startIndex;\n    }\n    if (brushItem.props.endIndex >= 0) {\n      endIndex = brushItem.props.endIndex;\n    }\n  }\n  return {\n    chartX: 0,\n    chartY: 0,\n    dataStartIndex: startIndex,\n    dataEndIndex: endIndex,\n    activeTooltipIndex: -1,\n    isTooltipActive: Boolean(defaultShowTooltip)\n  };\n};\nvar hasGraphicalBarItem = function hasGraphicalBarItem(graphicalItems) {\n  if (!graphicalItems || !graphicalItems.length) {\n    return false;\n  }\n  return graphicalItems.some(function (item) {\n    var name = getDisplayName(item && item.type);\n    return name && name.indexOf('Bar') >= 0;\n  });\n};\nvar getAxisNameByLayout = function getAxisNameByLayout(layout) {\n  if (layout === 'horizontal') {\n    return {\n      numericAxisName: 'yAxis',\n      cateAxisName: 'xAxis'\n    };\n  }\n  if (layout === 'vertical') {\n    return {\n      numericAxisName: 'xAxis',\n      cateAxisName: 'yAxis'\n    };\n  }\n  if (layout === 'centric') {\n    return {\n      numericAxisName: 'radiusAxis',\n      cateAxisName: 'angleAxis'\n    };\n  }\n  return {\n    numericAxisName: 'angleAxis',\n    cateAxisName: 'radiusAxis'\n  };\n};\n\n/**\n * Calculate the offset of main part in the svg element\n * @param  {Object} params.props          Latest props\n * @param  {Array}  params.graphicalItems The instances of item\n * @param  {Object} params.xAxisMap       The configuration of x-axis\n * @param  {Object} params.yAxisMap       The configuration of y-axis\n * @param  {Object} prevLegendBBox        The boundary box of legend\n * @return {Object} The offset of main part in the svg element\n */\nvar calculateOffset = function calculateOffset(_ref5, prevLegendBBox) {\n  var props = _ref5.props,\n    graphicalItems = _ref5.graphicalItems,\n    _ref5$xAxisMap = _ref5.xAxisMap,\n    xAxisMap = _ref5$xAxisMap === void 0 ? {} : _ref5$xAxisMap,\n    _ref5$yAxisMap = _ref5.yAxisMap,\n    yAxisMap = _ref5$yAxisMap === void 0 ? {} : _ref5$yAxisMap;\n  var width = props.width,\n    height = props.height,\n    children = props.children;\n  var margin = props.margin || {};\n  var brushItem = findChildByType(children, Brush);\n  var legendItem = findChildByType(children, Legend);\n  var offsetH = Object.keys(yAxisMap).reduce(function (result, id) {\n    var entry = yAxisMap[id];\n    var orientation = entry.orientation;\n    if (!entry.mirror && !entry.hide) {\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, orientation, result[orientation] + entry.width));\n    }\n    return result;\n  }, {\n    left: margin.left || 0,\n    right: margin.right || 0\n  });\n  var offsetV = Object.keys(xAxisMap).reduce(function (result, id) {\n    var entry = xAxisMap[id];\n    var orientation = entry.orientation;\n    if (!entry.mirror && !entry.hide) {\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, orientation, get(result, \"\".concat(orientation)) + entry.height));\n    }\n    return result;\n  }, {\n    top: margin.top || 0,\n    bottom: margin.bottom || 0\n  });\n  var offset = _objectSpread(_objectSpread({}, offsetV), offsetH);\n  var brushBottom = offset.bottom;\n  if (brushItem) {\n    offset.bottom += brushItem.props.height || Brush.defaultProps.height;\n  }\n  if (legendItem && prevLegendBBox) {\n    // @ts-expect-error margin is optional in props but required in appendOffsetOfLegend\n    offset = appendOffsetOfLegend(offset, graphicalItems, props, prevLegendBBox);\n  }\n  var offsetWidth = width - offset.left - offset.right;\n  var offsetHeight = height - offset.top - offset.bottom;\n  return _objectSpread(_objectSpread({\n    brushBottom: brushBottom\n  }, offset), {}, {\n    // never return negative values for height and width\n    width: Math.max(offsetWidth, 0),\n    height: Math.max(offsetHeight, 0)\n  });\n};\n// Determine the size of the axis, used for calculation of relative bar sizes\nvar getCartesianAxisSize = function getCartesianAxisSize(axisObj, axisName) {\n  if (axisName === 'xAxis') {\n    return axisObj[axisName].width;\n  }\n  if (axisName === 'yAxis') {\n    return axisObj[axisName].height;\n  }\n  // This is only supported for Bar charts (i.e. charts with cartesian axes), so we should never get here\n  return undefined;\n};\nexport var generateCategoricalChart = function generateCategoricalChart(_ref6) {\n  var chartName = _ref6.chartName,\n    GraphicalChild = _ref6.GraphicalChild,\n    _ref6$defaultTooltipE = _ref6.defaultTooltipEventType,\n    defaultTooltipEventType = _ref6$defaultTooltipE === void 0 ? 'axis' : _ref6$defaultTooltipE,\n    _ref6$validateTooltip = _ref6.validateTooltipEventTypes,\n    validateTooltipEventTypes = _ref6$validateTooltip === void 0 ? ['axis'] : _ref6$validateTooltip,\n    axisComponents = _ref6.axisComponents,\n    legendContent = _ref6.legendContent,\n    formatAxisMap = _ref6.formatAxisMap,\n    defaultProps = _ref6.defaultProps;\n  var getFormatItems = function getFormatItems(props, currentState) {\n    var graphicalItems = currentState.graphicalItems,\n      stackGroups = currentState.stackGroups,\n      offset = currentState.offset,\n      updateId = currentState.updateId,\n      dataStartIndex = currentState.dataStartIndex,\n      dataEndIndex = currentState.dataEndIndex;\n    var barSize = props.barSize,\n      layout = props.layout,\n      barGap = props.barGap,\n      barCategoryGap = props.barCategoryGap,\n      globalMaxBarSize = props.maxBarSize;\n    var _getAxisNameByLayout = getAxisNameByLayout(layout),\n      numericAxisName = _getAxisNameByLayout.numericAxisName,\n      cateAxisName = _getAxisNameByLayout.cateAxisName;\n    var hasBar = hasGraphicalBarItem(graphicalItems);\n    var formattedItems = [];\n    graphicalItems.forEach(function (item, index) {\n      var displayedData = getDisplayedData(props.data, {\n        graphicalItems: [item],\n        dataStartIndex: dataStartIndex,\n        dataEndIndex: dataEndIndex\n      });\n      var itemProps = item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n      var dataKey = itemProps.dataKey,\n        childMaxBarSize = itemProps.maxBarSize;\n      // axisId of the numerical axis\n      var numericAxisId = itemProps[\"\".concat(numericAxisName, \"Id\")];\n      // axisId of the categorical axis\n      var cateAxisId = itemProps[\"\".concat(cateAxisName, \"Id\")];\n      var axisObjInitialValue = {};\n      var axisObj = axisComponents.reduce(function (result, entry) {\n        var _item$type$displayNam, _item$type;\n        // map of axisId to axis for a specific axis type\n        var axisMap = currentState[\"\".concat(entry.axisType, \"Map\")];\n        // axisId of axis we are currently computing\n        var id = itemProps[\"\".concat(entry.axisType, \"Id\")];\n\n        /**\n         * tell the user in dev mode that their configuration is incorrect if we cannot find a match between\n         * axisId on the chart and axisId on the axis. zAxis does not get passed in the map for ComposedChart,\n         * leave it out of the check for now.\n         */\n        !(axisMap && axisMap[id] || entry.axisType === 'zAxis') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Specifying a(n) \".concat(entry.axisType, \"Id requires a corresponding \").concat(entry.axisType\n        // @ts-expect-error we should stop reading data from ReactElements\n        , \"Id on the targeted graphical component \").concat((_item$type$displayNam = item === null || item === void 0 || (_item$type = item.type) === null || _item$type === void 0 ? void 0 : _item$type.displayName) !== null && _item$type$displayNam !== void 0 ? _item$type$displayNam : '')) : invariant(false) : void 0;\n\n        // the axis we are currently formatting\n        var axis = axisMap[id];\n        return _objectSpread(_objectSpread({}, result), {}, _defineProperty(_defineProperty({}, entry.axisType, axis), \"\".concat(entry.axisType, \"Ticks\"), getTicksOfAxis(axis)));\n      }, axisObjInitialValue);\n      var cateAxis = axisObj[cateAxisName];\n      var cateTicks = axisObj[\"\".concat(cateAxisName, \"Ticks\")];\n      var stackedData = stackGroups && stackGroups[numericAxisId] && stackGroups[numericAxisId].hasStack && getStackedDataOfItem(item, stackGroups[numericAxisId].stackGroups);\n      var itemIsBar = getDisplayName(item.type).indexOf('Bar') >= 0;\n      var bandSize = getBandSizeOfAxis(cateAxis, cateTicks);\n      var barPosition = [];\n      var sizeList = hasBar && getBarSizeList({\n        barSize: barSize,\n        stackGroups: stackGroups,\n        totalSize: getCartesianAxisSize(axisObj, cateAxisName)\n      });\n      if (itemIsBar) {\n        var _ref7, _getBandSizeOfAxis;\n        // If it is bar, calculate the position of bar\n        var maxBarSize = isNil(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n        var barBandSize = (_ref7 = (_getBandSizeOfAxis = getBandSizeOfAxis(cateAxis, cateTicks, true)) !== null && _getBandSizeOfAxis !== void 0 ? _getBandSizeOfAxis : maxBarSize) !== null && _ref7 !== void 0 ? _ref7 : 0;\n        barPosition = getBarPosition({\n          barGap: barGap,\n          barCategoryGap: barCategoryGap,\n          bandSize: barBandSize !== bandSize ? barBandSize : bandSize,\n          sizeList: sizeList[cateAxisId],\n          maxBarSize: maxBarSize\n        });\n        if (barBandSize !== bandSize) {\n          barPosition = barPosition.map(function (pos) {\n            return _objectSpread(_objectSpread({}, pos), {}, {\n              position: _objectSpread(_objectSpread({}, pos.position), {}, {\n                offset: pos.position.offset - barBandSize / 2\n              })\n            });\n          });\n        }\n      }\n      // @ts-expect-error we should stop reading data from ReactElements\n      var composedFn = item && item.type && item.type.getComposedData;\n      if (composedFn) {\n        formattedItems.push({\n          props: _objectSpread(_objectSpread({}, composedFn(_objectSpread(_objectSpread({}, axisObj), {}, {\n            displayedData: displayedData,\n            props: props,\n            dataKey: dataKey,\n            item: item,\n            bandSize: bandSize,\n            barPosition: barPosition,\n            offset: offset,\n            stackedData: stackedData,\n            layout: layout,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex\n          }))), {}, _defineProperty(_defineProperty(_defineProperty({\n            key: item.key || \"item-\".concat(index)\n          }, numericAxisName, axisObj[numericAxisName]), cateAxisName, axisObj[cateAxisName]), \"animationId\", updateId)),\n          childIndex: parseChildIndex(item, props.children),\n          item: item\n        });\n      }\n    });\n    return formattedItems;\n  };\n\n  /**\n   * The AxisMaps are expensive to render on large data sets\n   * so provide the ability to store them in state and only update them when necessary\n   * they are dependent upon the start and end index of\n   * the brush so it's important that this method is called _after_\n   * the state is updated with any new start/end indices\n   *\n   * @param {Object} props          The props object to be used for updating the axismaps\n   * dataStartIndex: The start index of the data series when a brush is applied\n   * dataEndIndex: The end index of the data series when a brush is applied\n   * updateId: The update id\n   * @param {Object} prevState      Prev state\n   * @return {Object} state New state to set\n   */\n  var updateStateOfAxisMapsOffsetAndStackGroups = function updateStateOfAxisMapsOffsetAndStackGroups(_ref8, prevState) {\n    var props = _ref8.props,\n      dataStartIndex = _ref8.dataStartIndex,\n      dataEndIndex = _ref8.dataEndIndex,\n      updateId = _ref8.updateId;\n    if (!validateWidthHeight({\n      props: props\n    })) {\n      return null;\n    }\n    var children = props.children,\n      layout = props.layout,\n      stackOffset = props.stackOffset,\n      data = props.data,\n      reverseStackOrder = props.reverseStackOrder;\n    var _getAxisNameByLayout2 = getAxisNameByLayout(layout),\n      numericAxisName = _getAxisNameByLayout2.numericAxisName,\n      cateAxisName = _getAxisNameByLayout2.cateAxisName;\n    var graphicalItems = findAllByType(children, GraphicalChild);\n    var stackGroups = getStackGroupsByAxisId(data, graphicalItems, \"\".concat(numericAxisName, \"Id\"), \"\".concat(cateAxisName, \"Id\"), stackOffset, reverseStackOrder);\n    var axisObj = axisComponents.reduce(function (result, entry) {\n      var name = \"\".concat(entry.axisType, \"Map\");\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, name, getAxisMap(props, _objectSpread(_objectSpread({}, entry), {}, {\n        graphicalItems: graphicalItems,\n        stackGroups: entry.axisType === numericAxisName && stackGroups,\n        dataStartIndex: dataStartIndex,\n        dataEndIndex: dataEndIndex\n      }))));\n    }, {});\n    var offset = calculateOffset(_objectSpread(_objectSpread({}, axisObj), {}, {\n      props: props,\n      graphicalItems: graphicalItems\n    }), prevState === null || prevState === void 0 ? void 0 : prevState.legendBBox);\n    Object.keys(axisObj).forEach(function (key) {\n      axisObj[key] = formatAxisMap(props, axisObj[key], offset, key.replace('Map', ''), chartName);\n    });\n    var cateAxisMap = axisObj[\"\".concat(cateAxisName, \"Map\")];\n    var ticksObj = tooltipTicksGenerator(cateAxisMap);\n    var formattedGraphicalItems = getFormatItems(props, _objectSpread(_objectSpread({}, axisObj), {}, {\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex,\n      updateId: updateId,\n      graphicalItems: graphicalItems,\n      stackGroups: stackGroups,\n      offset: offset\n    }));\n    return _objectSpread(_objectSpread({\n      formattedGraphicalItems: formattedGraphicalItems,\n      graphicalItems: graphicalItems,\n      offset: offset,\n      stackGroups: stackGroups\n    }, ticksObj), axisObj);\n  };\n  var CategoricalChartWrapper = /*#__PURE__*/function (_Component) {\n    function CategoricalChartWrapper(_props) {\n      var _props$id, _props$throttleDelay;\n      var _this;\n      _classCallCheck(this, CategoricalChartWrapper);\n      _this = _callSuper(this, CategoricalChartWrapper, [_props]);\n      _defineProperty(_this, \"eventEmitterSymbol\", Symbol('rechartsEventEmitter'));\n      _defineProperty(_this, \"accessibilityManager\", new AccessibilityManager());\n      _defineProperty(_this, \"handleLegendBBoxUpdate\", function (box) {\n        if (box) {\n          var _this$state = _this.state,\n            dataStartIndex = _this$state.dataStartIndex,\n            dataEndIndex = _this$state.dataEndIndex,\n            updateId = _this$state.updateId;\n          _this.setState(_objectSpread({\n            legendBBox: box\n          }, updateStateOfAxisMapsOffsetAndStackGroups({\n            props: _this.props,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex,\n            updateId: updateId\n          }, _objectSpread(_objectSpread({}, _this.state), {}, {\n            legendBBox: box\n          }))));\n        }\n      });\n      _defineProperty(_this, \"handleReceiveSyncEvent\", function (cId, data, emitter) {\n        if (_this.props.syncId === cId) {\n          if (emitter === _this.eventEmitterSymbol && typeof _this.props.syncMethod !== 'function') {\n            return;\n          }\n          _this.applySyncEvent(data);\n        }\n      });\n      _defineProperty(_this, \"handleBrushChange\", function (_ref9) {\n        var startIndex = _ref9.startIndex,\n          endIndex = _ref9.endIndex;\n        // Only trigger changes if the extents of the brush have actually changed\n        if (startIndex !== _this.state.dataStartIndex || endIndex !== _this.state.dataEndIndex) {\n          var updateId = _this.state.updateId;\n          _this.setState(function () {\n            return _objectSpread({\n              dataStartIndex: startIndex,\n              dataEndIndex: endIndex\n            }, updateStateOfAxisMapsOffsetAndStackGroups({\n              props: _this.props,\n              dataStartIndex: startIndex,\n              dataEndIndex: endIndex,\n              updateId: updateId\n            }, _this.state));\n          });\n          _this.triggerSyncEvent({\n            dataStartIndex: startIndex,\n            dataEndIndex: endIndex\n          });\n        }\n      });\n      /**\n       * The handler of mouse entering chart\n       * @param  {Object} e              Event object\n       * @return {Null}                  null\n       */\n      _defineProperty(_this, \"handleMouseEnter\", function (e) {\n        var mouse = _this.getMouseInfo(e);\n        if (mouse) {\n          var _nextState = _objectSpread(_objectSpread({}, mouse), {}, {\n            isTooltipActive: true\n          });\n          _this.setState(_nextState);\n          _this.triggerSyncEvent(_nextState);\n          var onMouseEnter = _this.props.onMouseEnter;\n          if (isFunction(onMouseEnter)) {\n            onMouseEnter(_nextState, e);\n          }\n        }\n      });\n      _defineProperty(_this, \"triggeredAfterMouseMove\", function (e) {\n        var mouse = _this.getMouseInfo(e);\n        var nextState = mouse ? _objectSpread(_objectSpread({}, mouse), {}, {\n          isTooltipActive: true\n        }) : {\n          isTooltipActive: false\n        };\n        _this.setState(nextState);\n        _this.triggerSyncEvent(nextState);\n        var onMouseMove = _this.props.onMouseMove;\n        if (isFunction(onMouseMove)) {\n          onMouseMove(nextState, e);\n        }\n      });\n      /**\n       * The handler of mouse entering a scatter\n       * @param {Object} el The active scatter\n       * @return {Object} no return\n       */\n      _defineProperty(_this, \"handleItemMouseEnter\", function (el) {\n        _this.setState(function () {\n          return {\n            isTooltipActive: true,\n            activeItem: el,\n            activePayload: el.tooltipPayload,\n            activeCoordinate: el.tooltipPosition || {\n              x: el.cx,\n              y: el.cy\n            }\n          };\n        });\n      });\n      /**\n       * The handler of mouse leaving a scatter\n       * @return {Object} no return\n       */\n      _defineProperty(_this, \"handleItemMouseLeave\", function () {\n        _this.setState(function () {\n          return {\n            isTooltipActive: false\n          };\n        });\n      });\n      /**\n       * The handler of mouse moving in chart\n       * @param  {React.MouseEvent} e        Event object\n       * @return {void} no return\n       */\n      _defineProperty(_this, \"handleMouseMove\", function (e) {\n        e.persist();\n        _this.throttleTriggeredAfterMouseMove(e);\n      });\n      /**\n       * The handler if mouse leaving chart\n       * @param {Object} e Event object\n       * @return {Null} no return\n       */\n      _defineProperty(_this, \"handleMouseLeave\", function (e) {\n        _this.throttleTriggeredAfterMouseMove.cancel();\n        var nextState = {\n          isTooltipActive: false\n        };\n        _this.setState(nextState);\n        _this.triggerSyncEvent(nextState);\n        var onMouseLeave = _this.props.onMouseLeave;\n        if (isFunction(onMouseLeave)) {\n          onMouseLeave(nextState, e);\n        }\n      });\n      _defineProperty(_this, \"handleOuterEvent\", function (e) {\n        var eventName = getReactEventByType(e);\n        var event = get(_this.props, \"\".concat(eventName));\n        if (eventName && isFunction(event)) {\n          var _mouse;\n          var mouse;\n          if (/.*touch.*/i.test(eventName)) {\n            mouse = _this.getMouseInfo(e.changedTouches[0]);\n          } else {\n            mouse = _this.getMouseInfo(e);\n          }\n          event((_mouse = mouse) !== null && _mouse !== void 0 ? _mouse : {}, e);\n        }\n      });\n      _defineProperty(_this, \"handleClick\", function (e) {\n        var mouse = _this.getMouseInfo(e);\n        if (mouse) {\n          var _nextState2 = _objectSpread(_objectSpread({}, mouse), {}, {\n            isTooltipActive: true\n          });\n          _this.setState(_nextState2);\n          _this.triggerSyncEvent(_nextState2);\n          var onClick = _this.props.onClick;\n          if (isFunction(onClick)) {\n            onClick(_nextState2, e);\n          }\n        }\n      });\n      _defineProperty(_this, \"handleMouseDown\", function (e) {\n        var onMouseDown = _this.props.onMouseDown;\n        if (isFunction(onMouseDown)) {\n          var _nextState3 = _this.getMouseInfo(e);\n          onMouseDown(_nextState3, e);\n        }\n      });\n      _defineProperty(_this, \"handleMouseUp\", function (e) {\n        var onMouseUp = _this.props.onMouseUp;\n        if (isFunction(onMouseUp)) {\n          var _nextState4 = _this.getMouseInfo(e);\n          onMouseUp(_nextState4, e);\n        }\n      });\n      _defineProperty(_this, \"handleTouchMove\", function (e) {\n        if (e.changedTouches != null && e.changedTouches.length > 0) {\n          _this.throttleTriggeredAfterMouseMove(e.changedTouches[0]);\n        }\n      });\n      _defineProperty(_this, \"handleTouchStart\", function (e) {\n        if (e.changedTouches != null && e.changedTouches.length > 0) {\n          _this.handleMouseDown(e.changedTouches[0]);\n        }\n      });\n      _defineProperty(_this, \"handleTouchEnd\", function (e) {\n        if (e.changedTouches != null && e.changedTouches.length > 0) {\n          _this.handleMouseUp(e.changedTouches[0]);\n        }\n      });\n      _defineProperty(_this, \"handleDoubleClick\", function (e) {\n        var onDoubleClick = _this.props.onDoubleClick;\n        if (isFunction(onDoubleClick)) {\n          var _nextState5 = _this.getMouseInfo(e);\n          onDoubleClick(_nextState5, e);\n        }\n      });\n      _defineProperty(_this, \"handleContextMenu\", function (e) {\n        var onContextMenu = _this.props.onContextMenu;\n        if (isFunction(onContextMenu)) {\n          var _nextState6 = _this.getMouseInfo(e);\n          onContextMenu(_nextState6, e);\n        }\n      });\n      _defineProperty(_this, \"triggerSyncEvent\", function (data) {\n        if (_this.props.syncId !== undefined) {\n          eventCenter.emit(SYNC_EVENT, _this.props.syncId, data, _this.eventEmitterSymbol);\n        }\n      });\n      _defineProperty(_this, \"applySyncEvent\", function (data) {\n        var _this$props = _this.props,\n          layout = _this$props.layout,\n          syncMethod = _this$props.syncMethod;\n        var updateId = _this.state.updateId;\n        var dataStartIndex = data.dataStartIndex,\n          dataEndIndex = data.dataEndIndex;\n        if (data.dataStartIndex !== undefined || data.dataEndIndex !== undefined) {\n          _this.setState(_objectSpread({\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex\n          }, updateStateOfAxisMapsOffsetAndStackGroups({\n            props: _this.props,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex,\n            updateId: updateId\n          }, _this.state)));\n        } else if (data.activeTooltipIndex !== undefined) {\n          var chartX = data.chartX,\n            chartY = data.chartY;\n          var activeTooltipIndex = data.activeTooltipIndex;\n          var _this$state2 = _this.state,\n            offset = _this$state2.offset,\n            tooltipTicks = _this$state2.tooltipTicks;\n          if (!offset) {\n            return;\n          }\n          if (typeof syncMethod === 'function') {\n            // Call a callback function. If there is an application specific algorithm\n            activeTooltipIndex = syncMethod(tooltipTicks, data);\n          } else if (syncMethod === 'value') {\n            // Set activeTooltipIndex to the index with the same value as data.activeLabel\n            // For loop instead of findIndex because the latter is very slow in some browsers\n            activeTooltipIndex = -1; // in case we cannot find the element\n            for (var i = 0; i < tooltipTicks.length; i++) {\n              if (tooltipTicks[i].value === data.activeLabel) {\n                activeTooltipIndex = i;\n                break;\n              }\n            }\n          }\n          var viewBox = _objectSpread(_objectSpread({}, offset), {}, {\n            x: offset.left,\n            y: offset.top\n          });\n          // When a categorical chart is combined with another chart, the value of chartX\n          // and chartY may beyond the boundaries.\n          var validateChartX = Math.min(chartX, viewBox.x + viewBox.width);\n          var validateChartY = Math.min(chartY, viewBox.y + viewBox.height);\n          var activeLabel = tooltipTicks[activeTooltipIndex] && tooltipTicks[activeTooltipIndex].value;\n          var activePayload = getTooltipContent(_this.state, _this.props.data, activeTooltipIndex);\n          var activeCoordinate = tooltipTicks[activeTooltipIndex] ? {\n            x: layout === 'horizontal' ? tooltipTicks[activeTooltipIndex].coordinate : validateChartX,\n            y: layout === 'horizontal' ? validateChartY : tooltipTicks[activeTooltipIndex].coordinate\n          } : originCoordinate;\n          _this.setState(_objectSpread(_objectSpread({}, data), {}, {\n            activeLabel: activeLabel,\n            activeCoordinate: activeCoordinate,\n            activePayload: activePayload,\n            activeTooltipIndex: activeTooltipIndex\n          }));\n        } else {\n          _this.setState(data);\n        }\n      });\n      _defineProperty(_this, \"renderCursor\", function (element) {\n        var _element$props$active;\n        var _this$state3 = _this.state,\n          isTooltipActive = _this$state3.isTooltipActive,\n          activeCoordinate = _this$state3.activeCoordinate,\n          activePayload = _this$state3.activePayload,\n          offset = _this$state3.offset,\n          activeTooltipIndex = _this$state3.activeTooltipIndex,\n          tooltipAxisBandSize = _this$state3.tooltipAxisBandSize;\n        var tooltipEventType = _this.getTooltipEventType();\n        // The cursor is a part of the Tooltip, and it should be shown (by default) when the Tooltip is active.\n        var isActive = (_element$props$active = element.props.active) !== null && _element$props$active !== void 0 ? _element$props$active : isTooltipActive;\n        var layout = _this.props.layout;\n        var key = element.key || '_recharts-cursor';\n        return /*#__PURE__*/React.createElement(Cursor, {\n          key: key,\n          activeCoordinate: activeCoordinate,\n          activePayload: activePayload,\n          activeTooltipIndex: activeTooltipIndex,\n          chartName: chartName,\n          element: element,\n          isActive: isActive,\n          layout: layout,\n          offset: offset,\n          tooltipAxisBandSize: tooltipAxisBandSize,\n          tooltipEventType: tooltipEventType\n        });\n      });\n      _defineProperty(_this, \"renderPolarAxis\", function (element, displayName, index) {\n        var axisType = get(element, 'type.axisType');\n        var axisMap = get(_this.state, \"\".concat(axisType, \"Map\"));\n        var elementDefaultProps = element.type.defaultProps;\n        var elementProps = elementDefaultProps !== undefined ? _objectSpread(_objectSpread({}, elementDefaultProps), element.props) : element.props;\n        var axisOption = axisMap && axisMap[elementProps[\"\".concat(axisType, \"Id\")]];\n        return /*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread({}, axisOption), {}, {\n          className: clsx(axisType, axisOption.className),\n          key: element.key || \"\".concat(displayName, \"-\").concat(index),\n          ticks: getTicksOfAxis(axisOption, true)\n        }));\n      });\n      _defineProperty(_this, \"renderPolarGrid\", function (element) {\n        var _element$props = element.props,\n          radialLines = _element$props.radialLines,\n          polarAngles = _element$props.polarAngles,\n          polarRadius = _element$props.polarRadius;\n        var _this$state4 = _this.state,\n          radiusAxisMap = _this$state4.radiusAxisMap,\n          angleAxisMap = _this$state4.angleAxisMap;\n        var radiusAxis = getAnyElementOfObject(radiusAxisMap);\n        var angleAxis = getAnyElementOfObject(angleAxisMap);\n        var cx = angleAxis.cx,\n          cy = angleAxis.cy,\n          innerRadius = angleAxis.innerRadius,\n          outerRadius = angleAxis.outerRadius;\n        return /*#__PURE__*/cloneElement(element, {\n          polarAngles: Array.isArray(polarAngles) ? polarAngles : getTicksOfAxis(angleAxis, true).map(function (entry) {\n            return entry.coordinate;\n          }),\n          polarRadius: Array.isArray(polarRadius) ? polarRadius : getTicksOfAxis(radiusAxis, true).map(function (entry) {\n            return entry.coordinate;\n          }),\n          cx: cx,\n          cy: cy,\n          innerRadius: innerRadius,\n          outerRadius: outerRadius,\n          key: element.key || 'polar-grid',\n          radialLines: radialLines\n        });\n      });\n      /**\n       * Draw legend\n       * @return {ReactElement}            The instance of Legend\n       */\n      _defineProperty(_this, \"renderLegend\", function () {\n        var formattedGraphicalItems = _this.state.formattedGraphicalItems;\n        var _this$props2 = _this.props,\n          children = _this$props2.children,\n          width = _this$props2.width,\n          height = _this$props2.height;\n        var margin = _this.props.margin || {};\n        var legendWidth = width - (margin.left || 0) - (margin.right || 0);\n        var props = getLegendProps({\n          children: children,\n          formattedGraphicalItems: formattedGraphicalItems,\n          legendWidth: legendWidth,\n          legendContent: legendContent\n        });\n        if (!props) {\n          return null;\n        }\n        var item = props.item,\n          otherProps = _objectWithoutProperties(props, _excluded);\n        return /*#__PURE__*/cloneElement(item, _objectSpread(_objectSpread({}, otherProps), {}, {\n          chartWidth: width,\n          chartHeight: height,\n          margin: margin,\n          onBBoxUpdate: _this.handleLegendBBoxUpdate\n        }));\n      });\n      /**\n       * Draw Tooltip\n       * @return {ReactElement}  The instance of Tooltip\n       */\n      _defineProperty(_this, \"renderTooltip\", function () {\n        var _tooltipItem$props$ac;\n        var _this$props3 = _this.props,\n          children = _this$props3.children,\n          accessibilityLayer = _this$props3.accessibilityLayer;\n        var tooltipItem = findChildByType(children, Tooltip);\n        if (!tooltipItem) {\n          return null;\n        }\n        var _this$state5 = _this.state,\n          isTooltipActive = _this$state5.isTooltipActive,\n          activeCoordinate = _this$state5.activeCoordinate,\n          activePayload = _this$state5.activePayload,\n          activeLabel = _this$state5.activeLabel,\n          offset = _this$state5.offset;\n\n        // The user can set isActive on the Tooltip,\n        // and we respect the user to enable customisation.\n        // The Tooltip is active if the user has set isActive, or if the tooltip is active due to a mouse event.\n        var isActive = (_tooltipItem$props$ac = tooltipItem.props.active) !== null && _tooltipItem$props$ac !== void 0 ? _tooltipItem$props$ac : isTooltipActive;\n        return /*#__PURE__*/cloneElement(tooltipItem, {\n          viewBox: _objectSpread(_objectSpread({}, offset), {}, {\n            x: offset.left,\n            y: offset.top\n          }),\n          active: isActive,\n          label: activeLabel,\n          payload: isActive ? activePayload : [],\n          coordinate: activeCoordinate,\n          accessibilityLayer: accessibilityLayer\n        });\n      });\n      _defineProperty(_this, \"renderBrush\", function (element) {\n        var _this$props4 = _this.props,\n          margin = _this$props4.margin,\n          data = _this$props4.data;\n        var _this$state6 = _this.state,\n          offset = _this$state6.offset,\n          dataStartIndex = _this$state6.dataStartIndex,\n          dataEndIndex = _this$state6.dataEndIndex,\n          updateId = _this$state6.updateId;\n\n        // TODO: update brush when children update\n        return /*#__PURE__*/cloneElement(element, {\n          key: element.key || '_recharts-brush',\n          onChange: combineEventHandlers(_this.handleBrushChange, element.props.onChange),\n          data: data,\n          x: isNumber(element.props.x) ? element.props.x : offset.left,\n          y: isNumber(element.props.y) ? element.props.y : offset.top + offset.height + offset.brushBottom - (margin.bottom || 0),\n          width: isNumber(element.props.width) ? element.props.width : offset.width,\n          startIndex: dataStartIndex,\n          endIndex: dataEndIndex,\n          updateId: \"brush-\".concat(updateId)\n        });\n      });\n      _defineProperty(_this, \"renderReferenceElement\", function (element, displayName, index) {\n        if (!element) {\n          return null;\n        }\n        var _this2 = _this,\n          clipPathId = _this2.clipPathId;\n        var _this$state7 = _this.state,\n          xAxisMap = _this$state7.xAxisMap,\n          yAxisMap = _this$state7.yAxisMap,\n          offset = _this$state7.offset;\n        var elementDefaultProps = element.type.defaultProps || {};\n        var _element$props2 = element.props,\n          _element$props2$xAxis = _element$props2.xAxisId,\n          xAxisId = _element$props2$xAxis === void 0 ? elementDefaultProps.xAxisId : _element$props2$xAxis,\n          _element$props2$yAxis = _element$props2.yAxisId,\n          yAxisId = _element$props2$yAxis === void 0 ? elementDefaultProps.yAxisId : _element$props2$yAxis;\n        return /*#__PURE__*/cloneElement(element, {\n          key: element.key || \"\".concat(displayName, \"-\").concat(index),\n          xAxis: xAxisMap[xAxisId],\n          yAxis: yAxisMap[yAxisId],\n          viewBox: {\n            x: offset.left,\n            y: offset.top,\n            width: offset.width,\n            height: offset.height\n          },\n          clipPathId: clipPathId\n        });\n      });\n      _defineProperty(_this, \"renderActivePoints\", function (_ref10) {\n        var item = _ref10.item,\n          activePoint = _ref10.activePoint,\n          basePoint = _ref10.basePoint,\n          childIndex = _ref10.childIndex,\n          isRange = _ref10.isRange;\n        var result = [];\n        // item is not a React Element so we don't need to resolve defaultProps.\n        var key = item.props.key;\n        var itemItemProps = item.item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.item.type.defaultProps), item.item.props) : item.item.props;\n        var activeDot = itemItemProps.activeDot,\n          dataKey = itemItemProps.dataKey;\n        var dotProps = _objectSpread(_objectSpread({\n          index: childIndex,\n          dataKey: dataKey,\n          cx: activePoint.x,\n          cy: activePoint.y,\n          r: 4,\n          fill: getMainColorOfGraphicItem(item.item),\n          strokeWidth: 2,\n          stroke: '#fff',\n          payload: activePoint.payload,\n          value: activePoint.value\n        }, filterProps(activeDot, false)), adaptEventHandlers(activeDot));\n        result.push(CategoricalChartWrapper.renderActiveDot(activeDot, dotProps, \"\".concat(key, \"-activePoint-\").concat(childIndex)));\n        if (basePoint) {\n          result.push(CategoricalChartWrapper.renderActiveDot(activeDot, _objectSpread(_objectSpread({}, dotProps), {}, {\n            cx: basePoint.x,\n            cy: basePoint.y\n          }), \"\".concat(key, \"-basePoint-\").concat(childIndex)));\n        } else if (isRange) {\n          result.push(null);\n        }\n        return result;\n      });\n      _defineProperty(_this, \"renderGraphicChild\", function (element, displayName, index) {\n        var item = _this.filterFormatItem(element, displayName, index);\n        if (!item) {\n          return null;\n        }\n        var tooltipEventType = _this.getTooltipEventType();\n        var _this$state8 = _this.state,\n          isTooltipActive = _this$state8.isTooltipActive,\n          tooltipAxis = _this$state8.tooltipAxis,\n          activeTooltipIndex = _this$state8.activeTooltipIndex,\n          activeLabel = _this$state8.activeLabel;\n        var children = _this.props.children;\n        var tooltipItem = findChildByType(children, Tooltip);\n        // item is not a React Element so we don't need to resolve defaultProps\n        var _item$props = item.props,\n          points = _item$props.points,\n          isRange = _item$props.isRange,\n          baseLine = _item$props.baseLine;\n        var itemItemProps = item.item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.item.type.defaultProps), item.item.props) : item.item.props;\n        var activeDot = itemItemProps.activeDot,\n          hide = itemItemProps.hide,\n          activeBar = itemItemProps.activeBar,\n          activeShape = itemItemProps.activeShape;\n        var hasActive = Boolean(!hide && isTooltipActive && tooltipItem && (activeDot || activeBar || activeShape));\n        var itemEvents = {};\n        if (tooltipEventType !== 'axis' && tooltipItem && tooltipItem.props.trigger === 'click') {\n          itemEvents = {\n            onClick: combineEventHandlers(_this.handleItemMouseEnter, element.props.onClick)\n          };\n        } else if (tooltipEventType !== 'axis') {\n          itemEvents = {\n            onMouseLeave: combineEventHandlers(_this.handleItemMouseLeave, element.props.onMouseLeave),\n            onMouseEnter: combineEventHandlers(_this.handleItemMouseEnter, element.props.onMouseEnter)\n          };\n        }\n        var graphicalItem = /*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread({}, item.props), itemEvents));\n        function findWithPayload(entry) {\n          // TODO needs to verify dataKey is Function\n          return typeof tooltipAxis.dataKey === 'function' ? tooltipAxis.dataKey(entry.payload) : null;\n        }\n        if (hasActive) {\n          if (activeTooltipIndex >= 0) {\n            var activePoint, basePoint;\n            if (tooltipAxis.dataKey && !tooltipAxis.allowDuplicatedCategory) {\n              // number transform to string\n              var specifiedKey = typeof tooltipAxis.dataKey === 'function' ? findWithPayload : 'payload.'.concat(tooltipAxis.dataKey.toString());\n              activePoint = findEntryInArray(points, specifiedKey, activeLabel);\n              basePoint = isRange && baseLine && findEntryInArray(baseLine, specifiedKey, activeLabel);\n            } else {\n              activePoint = points === null || points === void 0 ? void 0 : points[activeTooltipIndex];\n              basePoint = isRange && baseLine && baseLine[activeTooltipIndex];\n            }\n            if (activeShape || activeBar) {\n              var activeIndex = element.props.activeIndex !== undefined ? element.props.activeIndex : activeTooltipIndex;\n              return [/*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread(_objectSpread({}, item.props), itemEvents), {}, {\n                activeIndex: activeIndex\n              })), null, null];\n            }\n            if (!isNil(activePoint)) {\n              return [graphicalItem].concat(_toConsumableArray(_this.renderActivePoints({\n                item: item,\n                activePoint: activePoint,\n                basePoint: basePoint,\n                childIndex: activeTooltipIndex,\n                isRange: isRange\n              })));\n            }\n          } else {\n            var _this$getItemByXY;\n            /**\n             * We hit this block if consumer uses a Tooltip without XAxis and/or YAxis.\n             * In which case, this.state.activeTooltipIndex never gets set\n             * because the mouse events that trigger that value getting set never get trigged without the axis components.\n             *\n             * An example usage case is a FunnelChart\n             */\n            var _ref11 = (_this$getItemByXY = _this.getItemByXY(_this.state.activeCoordinate)) !== null && _this$getItemByXY !== void 0 ? _this$getItemByXY : {\n                graphicalItem: graphicalItem\n              },\n              _ref11$graphicalItem = _ref11.graphicalItem,\n              _ref11$graphicalItem$ = _ref11$graphicalItem.item,\n              xyItem = _ref11$graphicalItem$ === void 0 ? element : _ref11$graphicalItem$,\n              childIndex = _ref11$graphicalItem.childIndex;\n            var elementProps = _objectSpread(_objectSpread(_objectSpread({}, item.props), itemEvents), {}, {\n              activeIndex: childIndex\n            });\n            return [/*#__PURE__*/cloneElement(xyItem, elementProps), null, null];\n          }\n        }\n        if (isRange) {\n          return [graphicalItem, null, null];\n        }\n        return [graphicalItem, null];\n      });\n      _defineProperty(_this, \"renderCustomized\", function (element, displayName, index) {\n        return /*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread({\n          key: \"recharts-customized-\".concat(index)\n        }, _this.props), _this.state));\n      });\n      _defineProperty(_this, \"renderMap\", {\n        CartesianGrid: {\n          handler: renderAsIs,\n          once: true\n        },\n        ReferenceArea: {\n          handler: _this.renderReferenceElement\n        },\n        ReferenceLine: {\n          handler: renderAsIs\n        },\n        ReferenceDot: {\n          handler: _this.renderReferenceElement\n        },\n        XAxis: {\n          handler: renderAsIs\n        },\n        YAxis: {\n          handler: renderAsIs\n        },\n        Brush: {\n          handler: _this.renderBrush,\n          once: true\n        },\n        Bar: {\n          handler: _this.renderGraphicChild\n        },\n        Line: {\n          handler: _this.renderGraphicChild\n        },\n        Area: {\n          handler: _this.renderGraphicChild\n        },\n        Radar: {\n          handler: _this.renderGraphicChild\n        },\n        RadialBar: {\n          handler: _this.renderGraphicChild\n        },\n        Scatter: {\n          handler: _this.renderGraphicChild\n        },\n        Pie: {\n          handler: _this.renderGraphicChild\n        },\n        Funnel: {\n          handler: _this.renderGraphicChild\n        },\n        Tooltip: {\n          handler: _this.renderCursor,\n          once: true\n        },\n        PolarGrid: {\n          handler: _this.renderPolarGrid,\n          once: true\n        },\n        PolarAngleAxis: {\n          handler: _this.renderPolarAxis\n        },\n        PolarRadiusAxis: {\n          handler: _this.renderPolarAxis\n        },\n        Customized: {\n          handler: _this.renderCustomized\n        }\n      });\n      _this.clipPathId = \"\".concat((_props$id = _props.id) !== null && _props$id !== void 0 ? _props$id : uniqueId('recharts'), \"-clip\");\n\n      // trigger 60fps\n      _this.throttleTriggeredAfterMouseMove = throttle(_this.triggeredAfterMouseMove, (_props$throttleDelay = _props.throttleDelay) !== null && _props$throttleDelay !== void 0 ? _props$throttleDelay : 1000 / 60);\n      _this.state = {};\n      return _this;\n    }\n    _inherits(CategoricalChartWrapper, _Component);\n    return _createClass(CategoricalChartWrapper, [{\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        var _this$props$margin$le, _this$props$margin$to;\n        this.addListener();\n        this.accessibilityManager.setDetails({\n          container: this.container,\n          offset: {\n            left: (_this$props$margin$le = this.props.margin.left) !== null && _this$props$margin$le !== void 0 ? _this$props$margin$le : 0,\n            top: (_this$props$margin$to = this.props.margin.top) !== null && _this$props$margin$to !== void 0 ? _this$props$margin$to : 0\n          },\n          coordinateList: this.state.tooltipTicks,\n          mouseHandlerCallback: this.triggeredAfterMouseMove,\n          layout: this.props.layout\n        });\n        this.displayDefaultTooltip();\n      }\n    }, {\n      key: \"displayDefaultTooltip\",\n      value: function displayDefaultTooltip() {\n        var _this$props5 = this.props,\n          children = _this$props5.children,\n          data = _this$props5.data,\n          height = _this$props5.height,\n          layout = _this$props5.layout;\n        var tooltipElem = findChildByType(children, Tooltip);\n        // If the chart doesn't include a <Tooltip /> element, there's no tooltip to display\n        if (!tooltipElem) {\n          return;\n        }\n        var defaultIndex = tooltipElem.props.defaultIndex;\n\n        // Protect against runtime errors\n        if (typeof defaultIndex !== 'number' || defaultIndex < 0 || defaultIndex > this.state.tooltipTicks.length - 1) {\n          return;\n        }\n        var activeLabel = this.state.tooltipTicks[defaultIndex] && this.state.tooltipTicks[defaultIndex].value;\n        var activePayload = getTooltipContent(this.state, data, defaultIndex, activeLabel);\n        var independentAxisCoord = this.state.tooltipTicks[defaultIndex].coordinate;\n        var dependentAxisCoord = (this.state.offset.top + height) / 2;\n        var isHorizontal = layout === 'horizontal';\n        var activeCoordinate = isHorizontal ? {\n          x: independentAxisCoord,\n          y: dependentAxisCoord\n        } : {\n          y: independentAxisCoord,\n          x: dependentAxisCoord\n        };\n\n        // Unlike other chart types, scatter plot's tooltip positions rely on both X and Y coordinates. Only the scatter plot\n        // element knows its own Y coordinates.\n        // If there's a scatter plot, we'll want to grab that element for an interrogation.\n        var scatterPlotElement = this.state.formattedGraphicalItems.find(function (_ref12) {\n          var item = _ref12.item;\n          return item.type.name === 'Scatter';\n        });\n        if (scatterPlotElement) {\n          activeCoordinate = _objectSpread(_objectSpread({}, activeCoordinate), scatterPlotElement.props.points[defaultIndex].tooltipPosition);\n          activePayload = scatterPlotElement.props.points[defaultIndex].tooltipPayload;\n        }\n        var nextState = {\n          activeTooltipIndex: defaultIndex,\n          isTooltipActive: true,\n          activeLabel: activeLabel,\n          activePayload: activePayload,\n          activeCoordinate: activeCoordinate\n        };\n        this.setState(nextState);\n        this.renderCursor(tooltipElem);\n\n        // Make sure that anyone who keyboard-only users who tab to the chart will start their\n        // cursors at defaultIndex\n        this.accessibilityManager.setIndex(defaultIndex);\n      }\n    }, {\n      key: \"getSnapshotBeforeUpdate\",\n      value: function getSnapshotBeforeUpdate(prevProps, prevState) {\n        if (!this.props.accessibilityLayer) {\n          return null;\n        }\n        if (this.state.tooltipTicks !== prevState.tooltipTicks) {\n          this.accessibilityManager.setDetails({\n            coordinateList: this.state.tooltipTicks\n          });\n        }\n        if (this.props.layout !== prevProps.layout) {\n          this.accessibilityManager.setDetails({\n            layout: this.props.layout\n          });\n        }\n        if (this.props.margin !== prevProps.margin) {\n          var _this$props$margin$le2, _this$props$margin$to2;\n          this.accessibilityManager.setDetails({\n            offset: {\n              left: (_this$props$margin$le2 = this.props.margin.left) !== null && _this$props$margin$le2 !== void 0 ? _this$props$margin$le2 : 0,\n              top: (_this$props$margin$to2 = this.props.margin.top) !== null && _this$props$margin$to2 !== void 0 ? _this$props$margin$to2 : 0\n            }\n          });\n        }\n\n        // Something has to be returned for getSnapshotBeforeUpdate\n        return null;\n      }\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate(prevProps) {\n        // Check to see if the Tooltip updated. If so, re-check default tooltip position\n        if (!isChildrenEqual([findChildByType(prevProps.children, Tooltip)], [findChildByType(this.props.children, Tooltip)])) {\n          this.displayDefaultTooltip();\n        }\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        this.removeListener();\n        this.throttleTriggeredAfterMouseMove.cancel();\n      }\n    }, {\n      key: \"getTooltipEventType\",\n      value: function getTooltipEventType() {\n        var tooltipItem = findChildByType(this.props.children, Tooltip);\n        if (tooltipItem && typeof tooltipItem.props.shared === 'boolean') {\n          var eventType = tooltipItem.props.shared ? 'axis' : 'item';\n          return validateTooltipEventTypes.indexOf(eventType) >= 0 ? eventType : defaultTooltipEventType;\n        }\n        return defaultTooltipEventType;\n      }\n\n      /**\n       * Get the information of mouse in chart, return null when the mouse is not in the chart\n       * @param  {MousePointer} event    The event object\n       * @return {Object}          Mouse data\n       */\n    }, {\n      key: \"getMouseInfo\",\n      value: function getMouseInfo(event) {\n        if (!this.container) {\n          return null;\n        }\n        var element = this.container;\n        var boundingRect = element.getBoundingClientRect();\n        var containerOffset = getOffset(boundingRect);\n        var e = {\n          chartX: Math.round(event.pageX - containerOffset.left),\n          chartY: Math.round(event.pageY - containerOffset.top)\n        };\n        var scale = boundingRect.width / element.offsetWidth || 1;\n        var rangeObj = this.inRange(e.chartX, e.chartY, scale);\n        if (!rangeObj) {\n          return null;\n        }\n        var _this$state9 = this.state,\n          xAxisMap = _this$state9.xAxisMap,\n          yAxisMap = _this$state9.yAxisMap;\n        var tooltipEventType = this.getTooltipEventType();\n        var toolTipData = getTooltipData(this.state, this.props.data, this.props.layout, rangeObj);\n        if (tooltipEventType !== 'axis' && xAxisMap && yAxisMap) {\n          var xScale = getAnyElementOfObject(xAxisMap).scale;\n          var yScale = getAnyElementOfObject(yAxisMap).scale;\n          var xValue = xScale && xScale.invert ? xScale.invert(e.chartX) : null;\n          var yValue = yScale && yScale.invert ? yScale.invert(e.chartY) : null;\n          return _objectSpread(_objectSpread({}, e), {}, {\n            xValue: xValue,\n            yValue: yValue\n          }, toolTipData);\n        }\n        if (toolTipData) {\n          return _objectSpread(_objectSpread({}, e), toolTipData);\n        }\n        return null;\n      }\n    }, {\n      key: \"inRange\",\n      value: function inRange(x, y) {\n        var scale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n        var layout = this.props.layout;\n        var scaledX = x / scale,\n          scaledY = y / scale;\n        if (layout === 'horizontal' || layout === 'vertical') {\n          var offset = this.state.offset;\n          var isInRange = scaledX >= offset.left && scaledX <= offset.left + offset.width && scaledY >= offset.top && scaledY <= offset.top + offset.height;\n          return isInRange ? {\n            x: scaledX,\n            y: scaledY\n          } : null;\n        }\n        var _this$state10 = this.state,\n          angleAxisMap = _this$state10.angleAxisMap,\n          radiusAxisMap = _this$state10.radiusAxisMap;\n        if (angleAxisMap && radiusAxisMap) {\n          var angleAxis = getAnyElementOfObject(angleAxisMap);\n          return inRangeOfSector({\n            x: scaledX,\n            y: scaledY\n          }, angleAxis);\n        }\n        return null;\n      }\n    }, {\n      key: \"parseEventsOfWrapper\",\n      value: function parseEventsOfWrapper() {\n        var children = this.props.children;\n        var tooltipEventType = this.getTooltipEventType();\n        var tooltipItem = findChildByType(children, Tooltip);\n        var tooltipEvents = {};\n        if (tooltipItem && tooltipEventType === 'axis') {\n          if (tooltipItem.props.trigger === 'click') {\n            tooltipEvents = {\n              onClick: this.handleClick\n            };\n          } else {\n            tooltipEvents = {\n              onMouseEnter: this.handleMouseEnter,\n              onDoubleClick: this.handleDoubleClick,\n              onMouseMove: this.handleMouseMove,\n              onMouseLeave: this.handleMouseLeave,\n              onTouchMove: this.handleTouchMove,\n              onTouchStart: this.handleTouchStart,\n              onTouchEnd: this.handleTouchEnd,\n              onContextMenu: this.handleContextMenu\n            };\n          }\n        }\n\n        // @ts-expect-error adaptEventHandlers expects DOM Event but generateCategoricalChart works with React UIEvents\n        var outerEvents = adaptEventHandlers(this.props, this.handleOuterEvent);\n        return _objectSpread(_objectSpread({}, outerEvents), tooltipEvents);\n      }\n    }, {\n      key: \"addListener\",\n      value: function addListener() {\n        eventCenter.on(SYNC_EVENT, this.handleReceiveSyncEvent);\n      }\n    }, {\n      key: \"removeListener\",\n      value: function removeListener() {\n        eventCenter.removeListener(SYNC_EVENT, this.handleReceiveSyncEvent);\n      }\n    }, {\n      key: \"filterFormatItem\",\n      value: function filterFormatItem(item, displayName, childIndex) {\n        var formattedGraphicalItems = this.state.formattedGraphicalItems;\n        for (var i = 0, len = formattedGraphicalItems.length; i < len; i++) {\n          var entry = formattedGraphicalItems[i];\n          if (entry.item === item || entry.props.key === item.key || displayName === getDisplayName(entry.item.type) && childIndex === entry.childIndex) {\n            return entry;\n          }\n        }\n        return null;\n      }\n    }, {\n      key: \"renderClipPath\",\n      value: function renderClipPath() {\n        var clipPathId = this.clipPathId;\n        var _this$state$offset = this.state.offset,\n          left = _this$state$offset.left,\n          top = _this$state$offset.top,\n          height = _this$state$offset.height,\n          width = _this$state$offset.width;\n        return /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n          id: clipPathId\n        }, /*#__PURE__*/React.createElement(\"rect\", {\n          x: left,\n          y: top,\n          height: height,\n          width: width\n        })));\n      }\n    }, {\n      key: \"getXScales\",\n      value: function getXScales() {\n        var xAxisMap = this.state.xAxisMap;\n        return xAxisMap ? Object.entries(xAxisMap).reduce(function (res, _ref13) {\n          var _ref14 = _slicedToArray(_ref13, 2),\n            axisId = _ref14[0],\n            axisProps = _ref14[1];\n          return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, axisId, axisProps.scale));\n        }, {}) : null;\n      }\n    }, {\n      key: \"getYScales\",\n      value: function getYScales() {\n        var yAxisMap = this.state.yAxisMap;\n        return yAxisMap ? Object.entries(yAxisMap).reduce(function (res, _ref15) {\n          var _ref16 = _slicedToArray(_ref15, 2),\n            axisId = _ref16[0],\n            axisProps = _ref16[1];\n          return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, axisId, axisProps.scale));\n        }, {}) : null;\n      }\n    }, {\n      key: \"getXScaleByAxisId\",\n      value: function getXScaleByAxisId(axisId) {\n        var _this$state$xAxisMap;\n        return (_this$state$xAxisMap = this.state.xAxisMap) === null || _this$state$xAxisMap === void 0 || (_this$state$xAxisMap = _this$state$xAxisMap[axisId]) === null || _this$state$xAxisMap === void 0 ? void 0 : _this$state$xAxisMap.scale;\n      }\n    }, {\n      key: \"getYScaleByAxisId\",\n      value: function getYScaleByAxisId(axisId) {\n        var _this$state$yAxisMap;\n        return (_this$state$yAxisMap = this.state.yAxisMap) === null || _this$state$yAxisMap === void 0 || (_this$state$yAxisMap = _this$state$yAxisMap[axisId]) === null || _this$state$yAxisMap === void 0 ? void 0 : _this$state$yAxisMap.scale;\n      }\n    }, {\n      key: \"getItemByXY\",\n      value: function getItemByXY(chartXY) {\n        var _this$state11 = this.state,\n          formattedGraphicalItems = _this$state11.formattedGraphicalItems,\n          activeItem = _this$state11.activeItem;\n        if (formattedGraphicalItems && formattedGraphicalItems.length) {\n          for (var i = 0, len = formattedGraphicalItems.length; i < len; i++) {\n            var graphicalItem = formattedGraphicalItems[i];\n            // graphicalItem is not a React Element so we don't need to resolve defaultProps\n            var props = graphicalItem.props,\n              item = graphicalItem.item;\n            var itemProps = item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n            var itemDisplayName = getDisplayName(item.type);\n            if (itemDisplayName === 'Bar') {\n              var activeBarItem = (props.data || []).find(function (entry) {\n                return isInRectangle(chartXY, entry);\n              });\n              if (activeBarItem) {\n                return {\n                  graphicalItem: graphicalItem,\n                  payload: activeBarItem\n                };\n              }\n            } else if (itemDisplayName === 'RadialBar') {\n              var _activeBarItem = (props.data || []).find(function (entry) {\n                return inRangeOfSector(chartXY, entry);\n              });\n              if (_activeBarItem) {\n                return {\n                  graphicalItem: graphicalItem,\n                  payload: _activeBarItem\n                };\n              }\n            } else if (isFunnel(graphicalItem, activeItem) || isPie(graphicalItem, activeItem) || isScatter(graphicalItem, activeItem)) {\n              var activeIndex = getActiveShapeIndexForTooltip({\n                graphicalItem: graphicalItem,\n                activeTooltipItem: activeItem,\n                itemData: itemProps.data\n              });\n              var childIndex = itemProps.activeIndex === undefined ? activeIndex : itemProps.activeIndex;\n              return {\n                graphicalItem: _objectSpread(_objectSpread({}, graphicalItem), {}, {\n                  childIndex: childIndex\n                }),\n                payload: isScatter(graphicalItem, activeItem) ? itemProps.data[activeIndex] : graphicalItem.props.data[activeIndex]\n              };\n            }\n          }\n        }\n        return null;\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var _this3 = this;\n        if (!validateWidthHeight(this)) {\n          return null;\n        }\n        var _this$props6 = this.props,\n          children = _this$props6.children,\n          className = _this$props6.className,\n          width = _this$props6.width,\n          height = _this$props6.height,\n          style = _this$props6.style,\n          compact = _this$props6.compact,\n          title = _this$props6.title,\n          desc = _this$props6.desc,\n          others = _objectWithoutProperties(_this$props6, _excluded2);\n        var attrs = filterProps(others, false);\n\n        // The \"compact\" mode is mainly used as the panorama within Brush\n        if (compact) {\n          return /*#__PURE__*/React.createElement(ChartLayoutContextProvider, {\n            state: this.state,\n            width: this.props.width,\n            height: this.props.height,\n            clipPathId: this.clipPathId\n          }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n            width: width,\n            height: height,\n            title: title,\n            desc: desc\n          }), this.renderClipPath(), renderByOrder(children, this.renderMap)));\n        }\n        if (this.props.accessibilityLayer) {\n          var _this$props$tabIndex, _this$props$role;\n          // Set tabIndex to 0 by default (can be overwritten)\n          attrs.tabIndex = (_this$props$tabIndex = this.props.tabIndex) !== null && _this$props$tabIndex !== void 0 ? _this$props$tabIndex : 0;\n          // Set role to img by default (can be overwritten)\n          attrs.role = (_this$props$role = this.props.role) !== null && _this$props$role !== void 0 ? _this$props$role : 'application';\n          attrs.onKeyDown = function (e) {\n            _this3.accessibilityManager.keyboardEvent(e);\n            // 'onKeyDown' is not currently a supported prop that can be passed through\n            // if it's added, this should be added: this.props.onKeyDown(e);\n          };\n          attrs.onFocus = function () {\n            _this3.accessibilityManager.focus();\n            // 'onFocus' is not currently a supported prop that can be passed through\n            // if it's added, the focus event should be forwarded to the prop\n          };\n        }\n        var events = this.parseEventsOfWrapper();\n        return /*#__PURE__*/React.createElement(ChartLayoutContextProvider, {\n          state: this.state,\n          width: this.props.width,\n          height: this.props.height,\n          clipPathId: this.clipPathId\n        }, /*#__PURE__*/React.createElement(\"div\", _extends({\n          className: clsx('recharts-wrapper', className),\n          style: _objectSpread({\n            position: 'relative',\n            cursor: 'default',\n            width: width,\n            height: height\n          }, style)\n        }, events, {\n          ref: function ref(node) {\n            _this3.container = node;\n          }\n        }), /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n          width: width,\n          height: height,\n          title: title,\n          desc: desc,\n          style: FULL_WIDTH_AND_HEIGHT\n        }), this.renderClipPath(), renderByOrder(children, this.renderMap)), this.renderLegend(), this.renderTooltip()));\n      }\n    }]);\n  }(Component);\n  _defineProperty(CategoricalChartWrapper, \"displayName\", chartName);\n  // todo join specific chart propTypes\n  _defineProperty(CategoricalChartWrapper, \"defaultProps\", _objectSpread({\n    layout: 'horizontal',\n    stackOffset: 'none',\n    barCategoryGap: '10%',\n    barGap: 4,\n    margin: {\n      top: 5,\n      right: 5,\n      bottom: 5,\n      left: 5\n    },\n    reverseStackOrder: false,\n    syncMethod: 'index'\n  }, defaultProps));\n  _defineProperty(CategoricalChartWrapper, \"getDerivedStateFromProps\", function (nextProps, prevState) {\n    var dataKey = nextProps.dataKey,\n      data = nextProps.data,\n      children = nextProps.children,\n      width = nextProps.width,\n      height = nextProps.height,\n      layout = nextProps.layout,\n      stackOffset = nextProps.stackOffset,\n      margin = nextProps.margin;\n    var dataStartIndex = prevState.dataStartIndex,\n      dataEndIndex = prevState.dataEndIndex;\n    if (prevState.updateId === undefined) {\n      var defaultState = createDefaultState(nextProps);\n      return _objectSpread(_objectSpread(_objectSpread({}, defaultState), {}, {\n        updateId: 0\n      }, updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread(_objectSpread({\n        props: nextProps\n      }, defaultState), {}, {\n        updateId: 0\n      }), prevState)), {}, {\n        prevDataKey: dataKey,\n        prevData: data,\n        prevWidth: width,\n        prevHeight: height,\n        prevLayout: layout,\n        prevStackOffset: stackOffset,\n        prevMargin: margin,\n        prevChildren: children\n      });\n    }\n    if (dataKey !== prevState.prevDataKey || data !== prevState.prevData || width !== prevState.prevWidth || height !== prevState.prevHeight || layout !== prevState.prevLayout || stackOffset !== prevState.prevStackOffset || !shallowEqual(margin, prevState.prevMargin)) {\n      var _defaultState = createDefaultState(nextProps);\n\n      // Fixes https://github.com/recharts/recharts/issues/2143\n      var keepFromPrevState = {\n        // (chartX, chartY) are (0,0) in default state, but we want to keep the last mouse position to avoid\n        // any flickering\n        chartX: prevState.chartX,\n        chartY: prevState.chartY,\n        // The tooltip should stay active when it was active in the previous render. If this is not\n        // the case, the tooltip disappears and immediately re-appears, causing a flickering effect\n        isTooltipActive: prevState.isTooltipActive\n      };\n      var updatesToState = _objectSpread(_objectSpread({}, getTooltipData(prevState, data, layout)), {}, {\n        updateId: prevState.updateId + 1\n      });\n      var newState = _objectSpread(_objectSpread(_objectSpread({}, _defaultState), keepFromPrevState), updatesToState);\n      return _objectSpread(_objectSpread(_objectSpread({}, newState), updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread({\n        props: nextProps\n      }, newState), prevState)), {}, {\n        prevDataKey: dataKey,\n        prevData: data,\n        prevWidth: width,\n        prevHeight: height,\n        prevLayout: layout,\n        prevStackOffset: stackOffset,\n        prevMargin: margin,\n        prevChildren: children\n      });\n    }\n    if (!isChildrenEqual(children, prevState.prevChildren)) {\n      var _brush$props$startInd, _brush$props, _brush$props$endIndex, _brush$props2;\n      // specifically check for Brush - if it exists and the start and end indexes are different, re-render with the new ones\n      var brush = findChildByType(children, Brush);\n      var startIndex = brush ? (_brush$props$startInd = (_brush$props = brush.props) === null || _brush$props === void 0 ? void 0 : _brush$props.startIndex) !== null && _brush$props$startInd !== void 0 ? _brush$props$startInd : dataStartIndex : dataStartIndex;\n      var endIndex = brush ? (_brush$props$endIndex = (_brush$props2 = brush.props) === null || _brush$props2 === void 0 ? void 0 : _brush$props2.endIndex) !== null && _brush$props$endIndex !== void 0 ? _brush$props$endIndex : dataEndIndex : dataEndIndex;\n      var hasDifferentStartOrEndIndex = startIndex !== dataStartIndex || endIndex !== dataEndIndex;\n\n      // update configuration in children\n      var hasGlobalData = !isNil(data);\n      var newUpdateId = hasGlobalData && !hasDifferentStartOrEndIndex ? prevState.updateId : prevState.updateId + 1;\n      return _objectSpread(_objectSpread({\n        updateId: newUpdateId\n      }, updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread(_objectSpread({\n        props: nextProps\n      }, prevState), {}, {\n        updateId: newUpdateId,\n        dataStartIndex: startIndex,\n        dataEndIndex: endIndex\n      }), prevState)), {}, {\n        prevChildren: children,\n        dataStartIndex: startIndex,\n        dataEndIndex: endIndex\n      });\n    }\n    return null;\n  });\n  _defineProperty(CategoricalChartWrapper, \"renderActiveDot\", function (option, props, key) {\n    var dot;\n    if (/*#__PURE__*/isValidElement(option)) {\n      dot = /*#__PURE__*/cloneElement(option, props);\n    } else if (isFunction(option)) {\n      dot = option(props);\n    } else {\n      dot = /*#__PURE__*/React.createElement(Dot, props);\n    }\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: \"recharts-active-dot\",\n      key: key\n    }, dot);\n  });\n  var CategoricalChart = /*#__PURE__*/forwardRef(function CategoricalChart(props, ref) {\n    return /*#__PURE__*/React.createElement(CategoricalChartWrapper, _extends({}, props, {\n      ref: ref\n    }));\n  });\n  CategoricalChart.displayName = CategoricalChartWrapper.displayName;\n  return CategoricalChart;\n};", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "_slicedToArray", "arr", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "r", "l", "t", "e", "n", "u", "a", "f", "next", "done", "push", "value", "Array", "isArray", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_callSuper", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "self", "_assertThisInitialized", "ReferenceError", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "_setPrototypeOf", "p", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_nonIterableSpread", "minLen", "_arrayLikeToArray", "toString", "slice", "name", "from", "test", "iter", "len", "arr2", "ownKeys", "keys", "filter", "getOwnPropertyDescriptor", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "obj", "_toPrimitive", "toPrimitive", "String", "Number", "React", "Component", "cloneElement", "isValidElement", "forwardRef", "isNil", "isFunction", "range", "get", "sortBy", "throttle", "clsx", "invariant", "Surface", "Layer", "<PERSON><PERSON><PERSON>", "Legend", "Dot", "isInRectangle", "filterProps", "findAllByType", "findChildByType", "getDisplayName", "getReactEventByType", "isChildrenEqual", "parseChildIndex", "renderByOrder", "validateWidthHeight", "Brush", "getOffset", "findEntryInArray", "getAnyElementOfObject", "hasDuplicate", "isNumber", "uniqueId", "appendOffsetOfLegend", "calculateActiveTickIndex", "combineEventHandlers", "getBandSizeOfAxis", "getBarPosition", "getBarSizeList", "getDomainOfDataByKey", "getDomainOfItemsWithSameAxis", "getDomainOfStackGroups", "getLegendProps", "getMainColorOfGraphicItem", "getStackedDataOfItem", "getStackGroupsByAxisId", "getTicksOfAxis", "getTooltipItem", "isCategoricalAxis", "parseDomainOfCategoryAxis", "parseErrorBarsOfAxis", "parseSpecifiedDomain", "detectReferenceElementsDomain", "inRangeOfSector", "polarToCartesian", "shallowEqual", "eventCenter", "SYNC_EVENT", "adaptEventHandlers", "AccessibilityManager", "isDomainSpecifiedByUser", "getActiveShapeIndexForTooltip", "isFunnel", "is<PERSON><PERSON>", "isScatter", "<PERSON><PERSON><PERSON>", "ChartLayoutContextProvider", "ORIENT_MAP", "xAxis", "yAxis", "FULL_WIDTH_AND_HEIGHT", "width", "height", "originCoordinate", "x", "y", "renderAsIs", "element", "calculateTooltipPos", "rangeObj", "layout", "angle", "radius", "getActiveCoordinate", "tooltipTicks", "activeIndex", "entry", "find", "tick", "index", "coordinate", "_angle", "_radius", "cx", "cy", "getDisplayedData", "data", "_ref", "graphicalItems", "dataStartIndex", "dataEndIndex", "itemsData", "reduce", "result", "child", "itemData", "concat", "getDefaultDomainByAxisType", "axisType", "undefined", "getTooltipContent", "state", "chartData", "activeLabel", "tooltipAxis", "displayedData", "_child$props$data", "payload", "dataKey", "allowDuplicatedCategory", "entries", "getTooltipData", "rangeData", "chartX", "chartY", "pos", "ticks", "orderedTooltipTicks", "axis", "activePayload", "activeCoordinate", "activeTooltipIndex", "getAxisMapByAxes", "_ref2", "axes", "axisIdKey", "stackGroups", "children", "stackOffset", "isCategorical", "_childProps$domain2", "childProps", "type", "defaultProps", "allowDataOverflow", "scale", "includeHidden", "axisId", "item", "_defaultProps", "itemAxisId", "domain", "duplicateDomain", "categoricalDomain", "defaultDomain", "_childProps$domain", "childDomain", "duplicate", "finalDomain", "errorBarsDomain", "_defaultProps2", "_defaultProps3", "itemHide", "hide", "hasStack", "axisDomain", "isDomain<PERSON><PERSON><PERSON>", "every", "originalDomain", "getAxisMapByItems", "_ref3", "Axis", "_defaultProps4", "_defaultProps5", "orientation", "getAxisMap", "_ref4", "_ref4$axisType", "AxisComp", "axisMap", "tooltipTicksGenerator", "tooltipAxisBandSize", "createDefaultState", "defaultShowTooltip", "brushItem", "startIndex", "endIndex", "isTooltipActive", "hasGraphicalBarItem", "some", "getAxisNameByLayout", "numericAxisName", "cateAxisName", "calculateOffset", "_ref5", "prevLegendBBox", "_ref5$xAxisMap", "xAxisMap", "_ref5$yAxisMap", "yAxisMap", "margin", "legendItem", "offsetH", "id", "mirror", "left", "right", "offsetV", "top", "bottom", "offset", "brushBottom", "offsetWidth", "offsetHeight", "Math", "max", "getCartesianAxisSize", "axisObj", "axisName", "generateCategoricalChart", "_ref6", "chartName", "GraphicalChild", "_ref6$defaultTooltipE", "defaultTooltipEventType", "_ref6$validateTooltip", "validateTooltipEventTypes", "axisComponents", "<PERSON><PERSON><PERSON><PERSON>", "formatAxisMap", "getFormatItems", "currentState", "updateId", "barSize", "barGap", "barCategoryGap", "globalMaxBarSize", "maxBarSize", "_getAxisNameByLayout", "<PERSON><PERSON><PERSON>", "formattedItems", "itemProps", "childMaxBarSize", "numericAxisId", "cateAxisId", "axisObjInitialValue", "_item$type$displayNam", "_item$type", "process", "env", "NODE_ENV", "displayName", "cateAxis", "cateTicks", "stackedData", "itemIsBar", "bandSize", "barPosition", "sizeList", "totalSize", "_ref7", "_getBandSizeOfAxis", "barBandSize", "map", "position", "composedFn", "getComposedData", "childIndex", "updateStateOfAxisMapsOffsetAndStackGroups", "_ref8", "prevState", "reverseStackOrder", "_getAxisNameByLayout2", "legend<PERSON><PERSON>", "replace", "cateAxisMap", "ticksObj", "formattedGraphicalItems", "CategoricalChartWrapper", "_Component", "_props", "_props$id", "_props$throttleDelay", "_this", "box", "_this$state", "setState", "cId", "emitter", "syncId", "eventEmitterSymbol", "syncMethod", "applySyncEvent", "_ref9", "triggerSyncEvent", "mouse", "getMouseInfo", "_nextState", "onMouseEnter", "nextState", "onMouseMove", "el", "activeItem", "tooltipPayload", "tooltipPosition", "persist", "throttleTriggeredAfterMouseMove", "cancel", "onMouseLeave", "eventName", "event", "_mouse", "changedTouches", "_nextState2", "onClick", "onMouseDown", "_nextState3", "onMouseUp", "_nextState4", "handleMouseDown", "handleMouseUp", "onDoubleClick", "_nextState5", "onContextMenu", "_nextState6", "emit", "_this$props", "_this$state2", "viewBox", "validateChartX", "min", "validateChartY", "_element$props$active", "_this$state3", "tooltipEventType", "getTooltipEventType", "isActive", "active", "createElement", "elementDefaultProps", "elementProps", "axisOption", "className", "_element$props", "radialLines", "polarAngles", "polarRadius", "_this$state4", "radiusAxisMap", "angleAxisMap", "radiusAxis", "angleAxis", "innerRadius", "outerRadius", "_this$props2", "legend<PERSON><PERSON><PERSON>", "otherProps", "chartWidth", "chartHeight", "onBBoxUpdate", "handleLegendBBoxUpdate", "_tooltipItem$props$ac", "_this$props3", "accessibilityLayer", "tooltipItem", "_this$state5", "label", "_this$props4", "_this$state6", "onChange", "handleBrushChange", "_this2", "clipPathId", "_this$state7", "_element$props2", "_element$props2$xAxis", "xAxisId", "_element$props2$yAxis", "yAxisId", "_ref10", "activePoint", "basePoint", "isRange", "itemItemProps", "activeDot", "dotProps", "fill", "strokeWidth", "stroke", "renderActiveDot", "filterFormatItem", "_this$state8", "_item$props", "points", "baseLine", "activeBar", "activeShape", "hasActive", "itemEvents", "trigger", "handleItemMouseEnter", "handleItemMouseLeave", "graphicalItem", "findWithPayload", "<PERSON><PERSON><PERSON>", "renderActivePoints", "_this$getItemByXY", "_ref11", "getItemByXY", "_ref11$graphicalItem", "_ref11$graphicalItem$", "xyItem", "Cartesian<PERSON><PERSON>", "handler", "once", "ReferenceArea", "renderReferenceElement", "ReferenceLine", "ReferenceDot", "XAxis", "YA<PERSON>s", "renderBrush", "Bar", "renderGraphicChild", "Line", "Area", "Radar", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pie", "Funnel", "renderCursor", "PolarGrid", "renderPolarGrid", "PolarAngleAxis", "renderPolarAxis", "PolarRadiusAxis", "Customized", "renderCustomized", "triggeredAfterMouseMove", "throttle<PERSON><PERSON><PERSON>", "componentDidMount", "_this$props$margin$le", "_this$props$margin$to", "addListener", "accessibilityManager", "setDetails", "container", "coordinateList", "mouseHandlerCallback", "displayDefaultTooltip", "_this$props5", "tooltipElem", "defaultIndex", "independentAxisCoord", "dependentAxisCoord", "isHorizontal", "scatterPlotElement", "_ref12", "setIndex", "getSnapshotBeforeUpdate", "prevProps", "_this$props$margin$le2", "_this$props$margin$to2", "componentDidUpdate", "componentWillUnmount", "removeListener", "shared", "eventType", "boundingRect", "getBoundingClientRect", "containerOffset", "round", "pageX", "pageY", "inRange", "_this$state9", "toolTipData", "xScale", "yScale", "xValue", "invert", "yValue", "scaledX", "scaledY", "isInRange", "_this$state10", "parseEventsOfWrapper", "tooltipEvents", "handleClick", "handleMouseEnter", "handleDoubleClick", "handleMouseMove", "handleMouseLeave", "onTouchMove", "handleTouchMove", "onTouchStart", "handleTouchStart", "onTouchEnd", "handleTouchEnd", "handleContextMenu", "outerEvents", "handleOuterEvent", "on", "handleReceiveSyncEvent", "renderClipPath", "_this$state$offset", "getXScales", "res", "_ref13", "_ref14", "axisProps", "getYScales", "_ref15", "_ref16", "getXScaleByAxisId", "_this$state$xAxisMap", "getYScaleByAxisId", "_this$state$yAxisMap", "chartXY", "_this$state11", "itemDisplayName", "activeBarItem", "_activeBarItem", "activeTooltipItem", "render", "_this3", "_this$props6", "style", "compact", "title", "desc", "others", "attrs", "renderMap", "_this$props$tabIndex", "_this$props$role", "tabIndex", "role", "onKeyDown", "keyboardEvent", "onFocus", "focus", "events", "cursor", "ref", "node", "renderLegend", "renderTooltip", "nextProps", "defaultState", "prevDataKey", "prevData", "prevWidth", "prevHeight", "prevLayout", "prevStackOffset", "<PERSON>v<PERSON><PERSON><PERSON>", "prev<PERSON><PERSON><PERSON><PERSON>", "_defaultState", "keepFromPrevState", "updatesToState", "newState", "_brush$props$startInd", "_brush$props", "_brush$props$endIndex", "_brush$props2", "brush", "hasDifferentStartOrEndIndex", "hasGlobalData", "newUpdateId", "option", "dot", "CategoricalChart"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/recharts/es6/chart/generateCategoricalChart.js"], "sourcesContent": ["var _excluded = [\"item\"],\n  _excluded2 = [\"children\", \"className\", \"width\", \"height\", \"style\", \"compact\", \"title\", \"desc\"];\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport React, { Component, cloneElement, isValidElement, forwardRef } from 'react';\nimport isNil from 'lodash/isNil';\nimport isFunction from 'lodash/isFunction';\nimport range from 'lodash/range';\nimport get from 'lodash/get';\nimport sortBy from 'lodash/sortBy';\nimport throttle from 'lodash/throttle';\nimport clsx from 'clsx';\n// eslint-disable-next-line no-restricted-imports\n\nimport invariant from 'tiny-invariant';\nimport { Surface } from '../container/Surface';\nimport { Layer } from '../container/Layer';\nimport { Tooltip } from '../component/Tooltip';\nimport { Legend } from '../component/Legend';\nimport { Dot } from '../shape/Dot';\nimport { isInRectangle } from '../shape/Rectangle';\nimport { filterProps, findAllByType, findChildByType, getDisplayName, getReactEventByType, isChildrenEqual, parseChildIndex, renderByOrder, validateWidthHeight } from '../util/ReactUtils';\nimport { Brush } from '../cartesian/Brush';\nimport { getOffset } from '../util/DOMUtils';\nimport { findEntryInArray, getAnyElementOfObject, hasDuplicate, isNumber, uniqueId } from '../util/DataUtils';\nimport { appendOffsetOfLegend, calculateActiveTickIndex, combineEventHandlers, getBandSizeOfAxis, getBarPosition, getBarSizeList, getDomainOfDataByKey, getDomainOfItemsWithSameAxis, getDomainOfStackGroups, getLegendProps, getMainColorOfGraphicItem, getStackedDataOfItem, getStackGroupsByAxisId, getTicksOfAxis, getTooltipItem, isCategoricalAxis, parseDomainOfCategoryAxis, parseErrorBarsOfAxis, parseSpecifiedDomain } from '../util/ChartUtils';\nimport { detectReferenceElementsDomain } from '../util/DetectReferenceElementsDomain';\nimport { inRangeOfSector, polarToCartesian } from '../util/PolarUtils';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { eventCenter, SYNC_EVENT } from '../util/Events';\nimport { adaptEventHandlers } from '../util/types';\nimport { AccessibilityManager } from './AccessibilityManager';\nimport { isDomainSpecifiedByUser } from '../util/isDomainSpecifiedByUser';\nimport { getActiveShapeIndexForTooltip, isFunnel, isPie, isScatter } from '../util/ActiveShapeUtils';\nimport { Cursor } from '../component/Cursor';\nimport { ChartLayoutContextProvider } from '../context/chartLayoutContext';\nvar ORIENT_MAP = {\n  xAxis: ['bottom', 'top'],\n  yAxis: ['left', 'right']\n};\nvar FULL_WIDTH_AND_HEIGHT = {\n  width: '100%',\n  height: '100%'\n};\nvar originCoordinate = {\n  x: 0,\n  y: 0\n};\n\n/**\n * This function exists as a temporary workaround.\n *\n * Why? generateCategoricalChart does not render `{children}` directly;\n * instead it passes them through `renderByOrder` function which reads their handlers.\n *\n * So, this is a handler that does nothing.\n * Once we get rid of `renderByOrder` and switch to JSX only, we can get rid of this handler too.\n *\n * @param {JSX} element as is in JSX\n * @returns {JSX} the same element\n */\nfunction renderAsIs(element) {\n  return element;\n}\nvar calculateTooltipPos = function calculateTooltipPos(rangeObj, layout) {\n  if (layout === 'horizontal') {\n    return rangeObj.x;\n  }\n  if (layout === 'vertical') {\n    return rangeObj.y;\n  }\n  if (layout === 'centric') {\n    return rangeObj.angle;\n  }\n  return rangeObj.radius;\n};\nvar getActiveCoordinate = function getActiveCoordinate(layout, tooltipTicks, activeIndex, rangeObj) {\n  var entry = tooltipTicks.find(function (tick) {\n    return tick && tick.index === activeIndex;\n  });\n  if (entry) {\n    if (layout === 'horizontal') {\n      return {\n        x: entry.coordinate,\n        y: rangeObj.y\n      };\n    }\n    if (layout === 'vertical') {\n      return {\n        x: rangeObj.x,\n        y: entry.coordinate\n      };\n    }\n    if (layout === 'centric') {\n      var _angle = entry.coordinate;\n      var _radius = rangeObj.radius;\n      return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), polarToCartesian(rangeObj.cx, rangeObj.cy, _radius, _angle)), {}, {\n        angle: _angle,\n        radius: _radius\n      });\n    }\n    var radius = entry.coordinate;\n    var angle = rangeObj.angle;\n    return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), polarToCartesian(rangeObj.cx, rangeObj.cy, radius, angle)), {}, {\n      angle: angle,\n      radius: radius\n    });\n  }\n  return originCoordinate;\n};\nvar getDisplayedData = function getDisplayedData(data, _ref) {\n  var graphicalItems = _ref.graphicalItems,\n    dataStartIndex = _ref.dataStartIndex,\n    dataEndIndex = _ref.dataEndIndex;\n  var itemsData = (graphicalItems !== null && graphicalItems !== void 0 ? graphicalItems : []).reduce(function (result, child) {\n    var itemData = child.props.data;\n    if (itemData && itemData.length) {\n      return [].concat(_toConsumableArray(result), _toConsumableArray(itemData));\n    }\n    return result;\n  }, []);\n  if (itemsData.length > 0) {\n    return itemsData;\n  }\n  if (data && data.length && isNumber(dataStartIndex) && isNumber(dataEndIndex)) {\n    return data.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  return [];\n};\nfunction getDefaultDomainByAxisType(axisType) {\n  return axisType === 'number' ? [0, 'auto'] : undefined;\n}\n\n/**\n * Get the content to be displayed in the tooltip\n * @param  {Object} state          Current state\n * @param  {Array}  chartData      The data defined in chart\n * @param  {Number} activeIndex    Active index of data\n * @param  {String} activeLabel    Active label of data\n * @return {Array}                 The content of tooltip\n */\nvar getTooltipContent = function getTooltipContent(state, chartData, activeIndex, activeLabel) {\n  var graphicalItems = state.graphicalItems,\n    tooltipAxis = state.tooltipAxis;\n  var displayedData = getDisplayedData(chartData, state);\n  if (activeIndex < 0 || !graphicalItems || !graphicalItems.length || activeIndex >= displayedData.length) {\n    return null;\n  }\n  // get data by activeIndex when the axis don't allow duplicated category\n  return graphicalItems.reduce(function (result, child) {\n    var _child$props$data;\n    /**\n     * Fixes: https://github.com/recharts/recharts/issues/3669\n     * Defaulting to chartData below to fix an edge case where the tooltip does not include data from all charts\n     * when a separate dataset is passed to chart prop data and specified on Line/Area/etc prop data\n     */\n    var data = (_child$props$data = child.props.data) !== null && _child$props$data !== void 0 ? _child$props$data : chartData;\n    if (data && state.dataStartIndex + state.dataEndIndex !== 0 &&\n    // https://github.com/recharts/recharts/issues/4717\n    // The data is sliced only when the active index is within the start/end index range.\n    state.dataEndIndex - state.dataStartIndex >= activeIndex) {\n      data = data.slice(state.dataStartIndex, state.dataEndIndex + 1);\n    }\n    var payload;\n    if (tooltipAxis.dataKey && !tooltipAxis.allowDuplicatedCategory) {\n      // graphic child has data props\n      var entries = data === undefined ? displayedData : data;\n      payload = findEntryInArray(entries, tooltipAxis.dataKey, activeLabel);\n    } else {\n      payload = data && data[activeIndex] || displayedData[activeIndex];\n    }\n    if (!payload) {\n      return result;\n    }\n    return [].concat(_toConsumableArray(result), [getTooltipItem(child, payload)]);\n  }, []);\n};\n\n/**\n * Returns tooltip data based on a mouse position (as a parameter or in state)\n * @param  {Object} state     current state\n * @param  {Array}  chartData the data defined in chart\n * @param  {String} layout     The layout type of chart\n * @param  {Object} rangeObj  { x, y } coordinates\n * @return {Object}           Tooltip data data\n */\nvar getTooltipData = function getTooltipData(state, chartData, layout, rangeObj) {\n  var rangeData = rangeObj || {\n    x: state.chartX,\n    y: state.chartY\n  };\n  var pos = calculateTooltipPos(rangeData, layout);\n  var ticks = state.orderedTooltipTicks,\n    axis = state.tooltipAxis,\n    tooltipTicks = state.tooltipTicks;\n  var activeIndex = calculateActiveTickIndex(pos, ticks, tooltipTicks, axis);\n  if (activeIndex >= 0 && tooltipTicks) {\n    var activeLabel = tooltipTicks[activeIndex] && tooltipTicks[activeIndex].value;\n    var activePayload = getTooltipContent(state, chartData, activeIndex, activeLabel);\n    var activeCoordinate = getActiveCoordinate(layout, ticks, activeIndex, rangeData);\n    return {\n      activeTooltipIndex: activeIndex,\n      activeLabel: activeLabel,\n      activePayload: activePayload,\n      activeCoordinate: activeCoordinate\n    };\n  }\n  return null;\n};\n\n/**\n * Get the configuration of axis by the options of axis instance\n * @param  {Object} props         Latest props\n * @param {Array}  axes           The instance of axes\n * @param  {Array} graphicalItems The instances of item\n * @param  {String} axisType      The type of axis, xAxis - x-axis, yAxis - y-axis\n * @param  {String} axisIdKey     The unique id of an axis\n * @param  {Object} stackGroups   The items grouped by axisId and stackId\n * @param {Number} dataStartIndex The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex   The end index of the data series when a brush is applied\n * @return {Object}      Configuration\n */\nexport var getAxisMapByAxes = function getAxisMapByAxes(props, _ref2) {\n  var axes = _ref2.axes,\n    graphicalItems = _ref2.graphicalItems,\n    axisType = _ref2.axisType,\n    axisIdKey = _ref2.axisIdKey,\n    stackGroups = _ref2.stackGroups,\n    dataStartIndex = _ref2.dataStartIndex,\n    dataEndIndex = _ref2.dataEndIndex;\n  var layout = props.layout,\n    children = props.children,\n    stackOffset = props.stackOffset;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n\n  // Eliminate duplicated axes\n  return axes.reduce(function (result, child) {\n    var _childProps$domain2;\n    var childProps = child.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, child.type.defaultProps), child.props) : child.props;\n    var type = childProps.type,\n      dataKey = childProps.dataKey,\n      allowDataOverflow = childProps.allowDataOverflow,\n      allowDuplicatedCategory = childProps.allowDuplicatedCategory,\n      scale = childProps.scale,\n      ticks = childProps.ticks,\n      includeHidden = childProps.includeHidden;\n    var axisId = childProps[axisIdKey];\n    if (result[axisId]) {\n      return result;\n    }\n    var displayedData = getDisplayedData(props.data, {\n      graphicalItems: graphicalItems.filter(function (item) {\n        var _defaultProps;\n        var itemAxisId = axisIdKey in item.props ? item.props[axisIdKey] : (_defaultProps = item.type.defaultProps) === null || _defaultProps === void 0 ? void 0 : _defaultProps[axisIdKey];\n        return itemAxisId === axisId;\n      }),\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex\n    });\n    var len = displayedData.length;\n    var domain, duplicateDomain, categoricalDomain;\n\n    /*\n     * This is a hack to short-circuit the domain creation here to enhance performance.\n     * Usually, the data is used to determine the domain, but when the user specifies\n     * a domain upfront (via props), there is no need to calculate the domain start and end,\n     * which is very expensive for a larger amount of data.\n     * The only thing that would prohibit short-circuiting is when the user doesn't allow data overflow,\n     * because the axis is supposed to ignore the specified domain that way.\n     */\n    if (isDomainSpecifiedByUser(childProps.domain, allowDataOverflow, type)) {\n      domain = parseSpecifiedDomain(childProps.domain, null, allowDataOverflow);\n      /* The chart can be categorical and have the domain specified in numbers\n       * we still need to calculate the categorical domain\n       * TODO: refactor this more\n       */\n      if (isCategorical && (type === 'number' || scale !== 'auto')) {\n        categoricalDomain = getDomainOfDataByKey(displayedData, dataKey, 'category');\n      }\n    }\n\n    // if the domain is defaulted we need this for `originalDomain` as well\n    var defaultDomain = getDefaultDomainByAxisType(type);\n\n    // we didn't create the domain from user's props above, so we need to calculate it\n    if (!domain || domain.length === 0) {\n      var _childProps$domain;\n      var childDomain = (_childProps$domain = childProps.domain) !== null && _childProps$domain !== void 0 ? _childProps$domain : defaultDomain;\n      if (dataKey) {\n        // has dataKey in <Axis />\n        domain = getDomainOfDataByKey(displayedData, dataKey, type);\n        if (type === 'category' && isCategorical) {\n          // the field type is category data and this axis is categorical axis\n          var duplicate = hasDuplicate(domain);\n          if (allowDuplicatedCategory && duplicate) {\n            duplicateDomain = domain;\n            // When category axis has duplicated text, serial numbers are used to generate scale\n            domain = range(0, len);\n          } else if (!allowDuplicatedCategory) {\n            // remove duplicated category\n            domain = parseDomainOfCategoryAxis(childDomain, domain, child).reduce(function (finalDomain, entry) {\n              return finalDomain.indexOf(entry) >= 0 ? finalDomain : [].concat(_toConsumableArray(finalDomain), [entry]);\n            }, []);\n          }\n        } else if (type === 'category') {\n          // the field type is category data and this axis is numerical axis\n          if (!allowDuplicatedCategory) {\n            domain = parseDomainOfCategoryAxis(childDomain, domain, child).reduce(function (finalDomain, entry) {\n              return finalDomain.indexOf(entry) >= 0 || entry === '' || isNil(entry) ? finalDomain : [].concat(_toConsumableArray(finalDomain), [entry]);\n            }, []);\n          } else {\n            // eliminate undefined or null or empty string\n            domain = domain.filter(function (entry) {\n              return entry !== '' && !isNil(entry);\n            });\n          }\n        } else if (type === 'number') {\n          // the field type is numerical\n          var errorBarsDomain = parseErrorBarsOfAxis(displayedData, graphicalItems.filter(function (item) {\n            var _defaultProps2, _defaultProps3;\n            var itemAxisId = axisIdKey in item.props ? item.props[axisIdKey] : (_defaultProps2 = item.type.defaultProps) === null || _defaultProps2 === void 0 ? void 0 : _defaultProps2[axisIdKey];\n            var itemHide = 'hide' in item.props ? item.props.hide : (_defaultProps3 = item.type.defaultProps) === null || _defaultProps3 === void 0 ? void 0 : _defaultProps3.hide;\n            return itemAxisId === axisId && (includeHidden || !itemHide);\n          }), dataKey, axisType, layout);\n          if (errorBarsDomain) {\n            domain = errorBarsDomain;\n          }\n        }\n        if (isCategorical && (type === 'number' || scale !== 'auto')) {\n          categoricalDomain = getDomainOfDataByKey(displayedData, dataKey, 'category');\n        }\n      } else if (isCategorical) {\n        // the axis is a categorical axis\n        domain = range(0, len);\n      } else if (stackGroups && stackGroups[axisId] && stackGroups[axisId].hasStack && type === 'number') {\n        // when stackOffset is 'expand', the domain may be calculated as [0, 1.000000000002]\n        domain = stackOffset === 'expand' ? [0, 1] : getDomainOfStackGroups(stackGroups[axisId].stackGroups, dataStartIndex, dataEndIndex);\n      } else {\n        domain = getDomainOfItemsWithSameAxis(displayedData, graphicalItems.filter(function (item) {\n          var itemAxisId = axisIdKey in item.props ? item.props[axisIdKey] : item.type.defaultProps[axisIdKey];\n          var itemHide = 'hide' in item.props ? item.props.hide : item.type.defaultProps.hide;\n          return itemAxisId === axisId && (includeHidden || !itemHide);\n        }), type, layout, true);\n      }\n      if (type === 'number') {\n        // To detect wether there is any reference lines whose props alwaysShow is true\n        domain = detectReferenceElementsDomain(children, domain, axisId, axisType, ticks);\n        if (childDomain) {\n          domain = parseSpecifiedDomain(childDomain, domain, allowDataOverflow);\n        }\n      } else if (type === 'category' && childDomain) {\n        var axisDomain = childDomain;\n        var isDomainValid = domain.every(function (entry) {\n          return axisDomain.indexOf(entry) >= 0;\n        });\n        if (isDomainValid) {\n          domain = axisDomain;\n        }\n      }\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, _objectSpread(_objectSpread({}, childProps), {}, {\n      axisType: axisType,\n      domain: domain,\n      categoricalDomain: categoricalDomain,\n      duplicateDomain: duplicateDomain,\n      originalDomain: (_childProps$domain2 = childProps.domain) !== null && _childProps$domain2 !== void 0 ? _childProps$domain2 : defaultDomain,\n      isCategorical: isCategorical,\n      layout: layout\n    })));\n  }, {});\n};\n\n/**\n * Get the configuration of axis by the options of item,\n * this kind of axis does not display in chart\n * @param  {Object} props         Latest props\n * @param  {Array} graphicalItems The instances of item\n * @param  {ReactElement} Axis    Axis Component\n * @param  {String} axisType      The type of axis, xAxis - x-axis, yAxis - y-axis\n * @param  {String} axisIdKey     The unique id of an axis\n * @param  {Object} stackGroups   The items grouped by axisId and stackId\n * @param {Number} dataStartIndex The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex   The end index of the data series when a brush is applied\n * @return {Object}               Configuration\n */\nvar getAxisMapByItems = function getAxisMapByItems(props, _ref3) {\n  var graphicalItems = _ref3.graphicalItems,\n    Axis = _ref3.Axis,\n    axisType = _ref3.axisType,\n    axisIdKey = _ref3.axisIdKey,\n    stackGroups = _ref3.stackGroups,\n    dataStartIndex = _ref3.dataStartIndex,\n    dataEndIndex = _ref3.dataEndIndex;\n  var layout = props.layout,\n    children = props.children;\n  var displayedData = getDisplayedData(props.data, {\n    graphicalItems: graphicalItems,\n    dataStartIndex: dataStartIndex,\n    dataEndIndex: dataEndIndex\n  });\n  var len = displayedData.length;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var index = -1;\n\n  // The default type of x-axis is category axis,\n  // The default contents of x-axis is the serial numbers of data\n  // The default type of y-axis is number axis\n  // The default contents of y-axis is the domain of data\n  return graphicalItems.reduce(function (result, child) {\n    var childProps = child.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, child.type.defaultProps), child.props) : child.props;\n    var axisId = childProps[axisIdKey];\n    var originalDomain = getDefaultDomainByAxisType('number');\n    if (!result[axisId]) {\n      index++;\n      var domain;\n      if (isCategorical) {\n        domain = range(0, len);\n      } else if (stackGroups && stackGroups[axisId] && stackGroups[axisId].hasStack) {\n        domain = getDomainOfStackGroups(stackGroups[axisId].stackGroups, dataStartIndex, dataEndIndex);\n        domain = detectReferenceElementsDomain(children, domain, axisId, axisType);\n      } else {\n        domain = parseSpecifiedDomain(originalDomain, getDomainOfItemsWithSameAxis(displayedData, graphicalItems.filter(function (item) {\n          var _defaultProps4, _defaultProps5;\n          var itemAxisId = axisIdKey in item.props ? item.props[axisIdKey] : (_defaultProps4 = item.type.defaultProps) === null || _defaultProps4 === void 0 ? void 0 : _defaultProps4[axisIdKey];\n          var itemHide = 'hide' in item.props ? item.props.hide : (_defaultProps5 = item.type.defaultProps) === null || _defaultProps5 === void 0 ? void 0 : _defaultProps5.hide;\n          return itemAxisId === axisId && !itemHide;\n        }), 'number', layout), Axis.defaultProps.allowDataOverflow);\n        domain = detectReferenceElementsDomain(children, domain, axisId, axisType);\n      }\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, _objectSpread(_objectSpread({\n        axisType: axisType\n      }, Axis.defaultProps), {}, {\n        hide: true,\n        orientation: get(ORIENT_MAP, \"\".concat(axisType, \".\").concat(index % 2), null),\n        domain: domain,\n        originalDomain: originalDomain,\n        isCategorical: isCategorical,\n        layout: layout\n        // specify scale when no Axis\n        // scale: isCategorical ? 'band' : 'linear',\n      })));\n    }\n    return result;\n  }, {});\n};\n\n/**\n * Get the configuration of all x-axis or y-axis\n * @param  {Object} props          Latest props\n * @param  {String} axisType       The type of axis\n * @param  {React.ComponentType}  [AxisComp]      Axis Component\n * @param  {Array}  graphicalItems The instances of item\n * @param  {Object} stackGroups    The items grouped by axisId and stackId\n * @param {Number} dataStartIndex  The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex    The end index of the data series when a brush is applied\n * @return {Object}          Configuration\n */\nvar getAxisMap = function getAxisMap(props, _ref4) {\n  var _ref4$axisType = _ref4.axisType,\n    axisType = _ref4$axisType === void 0 ? 'xAxis' : _ref4$axisType,\n    AxisComp = _ref4.AxisComp,\n    graphicalItems = _ref4.graphicalItems,\n    stackGroups = _ref4.stackGroups,\n    dataStartIndex = _ref4.dataStartIndex,\n    dataEndIndex = _ref4.dataEndIndex;\n  var children = props.children;\n  var axisIdKey = \"\".concat(axisType, \"Id\");\n  // Get all the instance of Axis\n  var axes = findAllByType(children, AxisComp);\n  var axisMap = {};\n  if (axes && axes.length) {\n    axisMap = getAxisMapByAxes(props, {\n      axes: axes,\n      graphicalItems: graphicalItems,\n      axisType: axisType,\n      axisIdKey: axisIdKey,\n      stackGroups: stackGroups,\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex\n    });\n  } else if (graphicalItems && graphicalItems.length) {\n    axisMap = getAxisMapByItems(props, {\n      Axis: AxisComp,\n      graphicalItems: graphicalItems,\n      axisType: axisType,\n      axisIdKey: axisIdKey,\n      stackGroups: stackGroups,\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex\n    });\n  }\n  return axisMap;\n};\nvar tooltipTicksGenerator = function tooltipTicksGenerator(axisMap) {\n  var axis = getAnyElementOfObject(axisMap);\n  var tooltipTicks = getTicksOfAxis(axis, false, true);\n  return {\n    tooltipTicks: tooltipTicks,\n    orderedTooltipTicks: sortBy(tooltipTicks, function (o) {\n      return o.coordinate;\n    }),\n    tooltipAxis: axis,\n    tooltipAxisBandSize: getBandSizeOfAxis(axis, tooltipTicks)\n  };\n};\n\n/**\n * Returns default, reset state for the categorical chart.\n * @param {Object} props Props object to use when creating the default state\n * @return {Object} Whole new state\n */\nexport var createDefaultState = function createDefaultState(props) {\n  var children = props.children,\n    defaultShowTooltip = props.defaultShowTooltip;\n  var brushItem = findChildByType(children, Brush);\n  var startIndex = 0;\n  var endIndex = 0;\n  if (props.data && props.data.length !== 0) {\n    endIndex = props.data.length - 1;\n  }\n  if (brushItem && brushItem.props) {\n    if (brushItem.props.startIndex >= 0) {\n      startIndex = brushItem.props.startIndex;\n    }\n    if (brushItem.props.endIndex >= 0) {\n      endIndex = brushItem.props.endIndex;\n    }\n  }\n  return {\n    chartX: 0,\n    chartY: 0,\n    dataStartIndex: startIndex,\n    dataEndIndex: endIndex,\n    activeTooltipIndex: -1,\n    isTooltipActive: Boolean(defaultShowTooltip)\n  };\n};\nvar hasGraphicalBarItem = function hasGraphicalBarItem(graphicalItems) {\n  if (!graphicalItems || !graphicalItems.length) {\n    return false;\n  }\n  return graphicalItems.some(function (item) {\n    var name = getDisplayName(item && item.type);\n    return name && name.indexOf('Bar') >= 0;\n  });\n};\nvar getAxisNameByLayout = function getAxisNameByLayout(layout) {\n  if (layout === 'horizontal') {\n    return {\n      numericAxisName: 'yAxis',\n      cateAxisName: 'xAxis'\n    };\n  }\n  if (layout === 'vertical') {\n    return {\n      numericAxisName: 'xAxis',\n      cateAxisName: 'yAxis'\n    };\n  }\n  if (layout === 'centric') {\n    return {\n      numericAxisName: 'radiusAxis',\n      cateAxisName: 'angleAxis'\n    };\n  }\n  return {\n    numericAxisName: 'angleAxis',\n    cateAxisName: 'radiusAxis'\n  };\n};\n\n/**\n * Calculate the offset of main part in the svg element\n * @param  {Object} params.props          Latest props\n * @param  {Array}  params.graphicalItems The instances of item\n * @param  {Object} params.xAxisMap       The configuration of x-axis\n * @param  {Object} params.yAxisMap       The configuration of y-axis\n * @param  {Object} prevLegendBBox        The boundary box of legend\n * @return {Object} The offset of main part in the svg element\n */\nvar calculateOffset = function calculateOffset(_ref5, prevLegendBBox) {\n  var props = _ref5.props,\n    graphicalItems = _ref5.graphicalItems,\n    _ref5$xAxisMap = _ref5.xAxisMap,\n    xAxisMap = _ref5$xAxisMap === void 0 ? {} : _ref5$xAxisMap,\n    _ref5$yAxisMap = _ref5.yAxisMap,\n    yAxisMap = _ref5$yAxisMap === void 0 ? {} : _ref5$yAxisMap;\n  var width = props.width,\n    height = props.height,\n    children = props.children;\n  var margin = props.margin || {};\n  var brushItem = findChildByType(children, Brush);\n  var legendItem = findChildByType(children, Legend);\n  var offsetH = Object.keys(yAxisMap).reduce(function (result, id) {\n    var entry = yAxisMap[id];\n    var orientation = entry.orientation;\n    if (!entry.mirror && !entry.hide) {\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, orientation, result[orientation] + entry.width));\n    }\n    return result;\n  }, {\n    left: margin.left || 0,\n    right: margin.right || 0\n  });\n  var offsetV = Object.keys(xAxisMap).reduce(function (result, id) {\n    var entry = xAxisMap[id];\n    var orientation = entry.orientation;\n    if (!entry.mirror && !entry.hide) {\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, orientation, get(result, \"\".concat(orientation)) + entry.height));\n    }\n    return result;\n  }, {\n    top: margin.top || 0,\n    bottom: margin.bottom || 0\n  });\n  var offset = _objectSpread(_objectSpread({}, offsetV), offsetH);\n  var brushBottom = offset.bottom;\n  if (brushItem) {\n    offset.bottom += brushItem.props.height || Brush.defaultProps.height;\n  }\n  if (legendItem && prevLegendBBox) {\n    // @ts-expect-error margin is optional in props but required in appendOffsetOfLegend\n    offset = appendOffsetOfLegend(offset, graphicalItems, props, prevLegendBBox);\n  }\n  var offsetWidth = width - offset.left - offset.right;\n  var offsetHeight = height - offset.top - offset.bottom;\n  return _objectSpread(_objectSpread({\n    brushBottom: brushBottom\n  }, offset), {}, {\n    // never return negative values for height and width\n    width: Math.max(offsetWidth, 0),\n    height: Math.max(offsetHeight, 0)\n  });\n};\n// Determine the size of the axis, used for calculation of relative bar sizes\nvar getCartesianAxisSize = function getCartesianAxisSize(axisObj, axisName) {\n  if (axisName === 'xAxis') {\n    return axisObj[axisName].width;\n  }\n  if (axisName === 'yAxis') {\n    return axisObj[axisName].height;\n  }\n  // This is only supported for Bar charts (i.e. charts with cartesian axes), so we should never get here\n  return undefined;\n};\nexport var generateCategoricalChart = function generateCategoricalChart(_ref6) {\n  var chartName = _ref6.chartName,\n    GraphicalChild = _ref6.GraphicalChild,\n    _ref6$defaultTooltipE = _ref6.defaultTooltipEventType,\n    defaultTooltipEventType = _ref6$defaultTooltipE === void 0 ? 'axis' : _ref6$defaultTooltipE,\n    _ref6$validateTooltip = _ref6.validateTooltipEventTypes,\n    validateTooltipEventTypes = _ref6$validateTooltip === void 0 ? ['axis'] : _ref6$validateTooltip,\n    axisComponents = _ref6.axisComponents,\n    legendContent = _ref6.legendContent,\n    formatAxisMap = _ref6.formatAxisMap,\n    defaultProps = _ref6.defaultProps;\n  var getFormatItems = function getFormatItems(props, currentState) {\n    var graphicalItems = currentState.graphicalItems,\n      stackGroups = currentState.stackGroups,\n      offset = currentState.offset,\n      updateId = currentState.updateId,\n      dataStartIndex = currentState.dataStartIndex,\n      dataEndIndex = currentState.dataEndIndex;\n    var barSize = props.barSize,\n      layout = props.layout,\n      barGap = props.barGap,\n      barCategoryGap = props.barCategoryGap,\n      globalMaxBarSize = props.maxBarSize;\n    var _getAxisNameByLayout = getAxisNameByLayout(layout),\n      numericAxisName = _getAxisNameByLayout.numericAxisName,\n      cateAxisName = _getAxisNameByLayout.cateAxisName;\n    var hasBar = hasGraphicalBarItem(graphicalItems);\n    var formattedItems = [];\n    graphicalItems.forEach(function (item, index) {\n      var displayedData = getDisplayedData(props.data, {\n        graphicalItems: [item],\n        dataStartIndex: dataStartIndex,\n        dataEndIndex: dataEndIndex\n      });\n      var itemProps = item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n      var dataKey = itemProps.dataKey,\n        childMaxBarSize = itemProps.maxBarSize;\n      // axisId of the numerical axis\n      var numericAxisId = itemProps[\"\".concat(numericAxisName, \"Id\")];\n      // axisId of the categorical axis\n      var cateAxisId = itemProps[\"\".concat(cateAxisName, \"Id\")];\n      var axisObjInitialValue = {};\n      var axisObj = axisComponents.reduce(function (result, entry) {\n        var _item$type$displayNam, _item$type;\n        // map of axisId to axis for a specific axis type\n        var axisMap = currentState[\"\".concat(entry.axisType, \"Map\")];\n        // axisId of axis we are currently computing\n        var id = itemProps[\"\".concat(entry.axisType, \"Id\")];\n\n        /**\n         * tell the user in dev mode that their configuration is incorrect if we cannot find a match between\n         * axisId on the chart and axisId on the axis. zAxis does not get passed in the map for ComposedChart,\n         * leave it out of the check for now.\n         */\n        !(axisMap && axisMap[id] || entry.axisType === 'zAxis') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Specifying a(n) \".concat(entry.axisType, \"Id requires a corresponding \").concat(entry.axisType\n        // @ts-expect-error we should stop reading data from ReactElements\n        , \"Id on the targeted graphical component \").concat((_item$type$displayNam = item === null || item === void 0 || (_item$type = item.type) === null || _item$type === void 0 ? void 0 : _item$type.displayName) !== null && _item$type$displayNam !== void 0 ? _item$type$displayNam : '')) : invariant(false) : void 0;\n\n        // the axis we are currently formatting\n        var axis = axisMap[id];\n        return _objectSpread(_objectSpread({}, result), {}, _defineProperty(_defineProperty({}, entry.axisType, axis), \"\".concat(entry.axisType, \"Ticks\"), getTicksOfAxis(axis)));\n      }, axisObjInitialValue);\n      var cateAxis = axisObj[cateAxisName];\n      var cateTicks = axisObj[\"\".concat(cateAxisName, \"Ticks\")];\n      var stackedData = stackGroups && stackGroups[numericAxisId] && stackGroups[numericAxisId].hasStack && getStackedDataOfItem(item, stackGroups[numericAxisId].stackGroups);\n      var itemIsBar = getDisplayName(item.type).indexOf('Bar') >= 0;\n      var bandSize = getBandSizeOfAxis(cateAxis, cateTicks);\n      var barPosition = [];\n      var sizeList = hasBar && getBarSizeList({\n        barSize: barSize,\n        stackGroups: stackGroups,\n        totalSize: getCartesianAxisSize(axisObj, cateAxisName)\n      });\n      if (itemIsBar) {\n        var _ref7, _getBandSizeOfAxis;\n        // If it is bar, calculate the position of bar\n        var maxBarSize = isNil(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n        var barBandSize = (_ref7 = (_getBandSizeOfAxis = getBandSizeOfAxis(cateAxis, cateTicks, true)) !== null && _getBandSizeOfAxis !== void 0 ? _getBandSizeOfAxis : maxBarSize) !== null && _ref7 !== void 0 ? _ref7 : 0;\n        barPosition = getBarPosition({\n          barGap: barGap,\n          barCategoryGap: barCategoryGap,\n          bandSize: barBandSize !== bandSize ? barBandSize : bandSize,\n          sizeList: sizeList[cateAxisId],\n          maxBarSize: maxBarSize\n        });\n        if (barBandSize !== bandSize) {\n          barPosition = barPosition.map(function (pos) {\n            return _objectSpread(_objectSpread({}, pos), {}, {\n              position: _objectSpread(_objectSpread({}, pos.position), {}, {\n                offset: pos.position.offset - barBandSize / 2\n              })\n            });\n          });\n        }\n      }\n      // @ts-expect-error we should stop reading data from ReactElements\n      var composedFn = item && item.type && item.type.getComposedData;\n      if (composedFn) {\n        formattedItems.push({\n          props: _objectSpread(_objectSpread({}, composedFn(_objectSpread(_objectSpread({}, axisObj), {}, {\n            displayedData: displayedData,\n            props: props,\n            dataKey: dataKey,\n            item: item,\n            bandSize: bandSize,\n            barPosition: barPosition,\n            offset: offset,\n            stackedData: stackedData,\n            layout: layout,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex\n          }))), {}, _defineProperty(_defineProperty(_defineProperty({\n            key: item.key || \"item-\".concat(index)\n          }, numericAxisName, axisObj[numericAxisName]), cateAxisName, axisObj[cateAxisName]), \"animationId\", updateId)),\n          childIndex: parseChildIndex(item, props.children),\n          item: item\n        });\n      }\n    });\n    return formattedItems;\n  };\n\n  /**\n   * The AxisMaps are expensive to render on large data sets\n   * so provide the ability to store them in state and only update them when necessary\n   * they are dependent upon the start and end index of\n   * the brush so it's important that this method is called _after_\n   * the state is updated with any new start/end indices\n   *\n   * @param {Object} props          The props object to be used for updating the axismaps\n   * dataStartIndex: The start index of the data series when a brush is applied\n   * dataEndIndex: The end index of the data series when a brush is applied\n   * updateId: The update id\n   * @param {Object} prevState      Prev state\n   * @return {Object} state New state to set\n   */\n  var updateStateOfAxisMapsOffsetAndStackGroups = function updateStateOfAxisMapsOffsetAndStackGroups(_ref8, prevState) {\n    var props = _ref8.props,\n      dataStartIndex = _ref8.dataStartIndex,\n      dataEndIndex = _ref8.dataEndIndex,\n      updateId = _ref8.updateId;\n    if (!validateWidthHeight({\n      props: props\n    })) {\n      return null;\n    }\n    var children = props.children,\n      layout = props.layout,\n      stackOffset = props.stackOffset,\n      data = props.data,\n      reverseStackOrder = props.reverseStackOrder;\n    var _getAxisNameByLayout2 = getAxisNameByLayout(layout),\n      numericAxisName = _getAxisNameByLayout2.numericAxisName,\n      cateAxisName = _getAxisNameByLayout2.cateAxisName;\n    var graphicalItems = findAllByType(children, GraphicalChild);\n    var stackGroups = getStackGroupsByAxisId(data, graphicalItems, \"\".concat(numericAxisName, \"Id\"), \"\".concat(cateAxisName, \"Id\"), stackOffset, reverseStackOrder);\n    var axisObj = axisComponents.reduce(function (result, entry) {\n      var name = \"\".concat(entry.axisType, \"Map\");\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, name, getAxisMap(props, _objectSpread(_objectSpread({}, entry), {}, {\n        graphicalItems: graphicalItems,\n        stackGroups: entry.axisType === numericAxisName && stackGroups,\n        dataStartIndex: dataStartIndex,\n        dataEndIndex: dataEndIndex\n      }))));\n    }, {});\n    var offset = calculateOffset(_objectSpread(_objectSpread({}, axisObj), {}, {\n      props: props,\n      graphicalItems: graphicalItems\n    }), prevState === null || prevState === void 0 ? void 0 : prevState.legendBBox);\n    Object.keys(axisObj).forEach(function (key) {\n      axisObj[key] = formatAxisMap(props, axisObj[key], offset, key.replace('Map', ''), chartName);\n    });\n    var cateAxisMap = axisObj[\"\".concat(cateAxisName, \"Map\")];\n    var ticksObj = tooltipTicksGenerator(cateAxisMap);\n    var formattedGraphicalItems = getFormatItems(props, _objectSpread(_objectSpread({}, axisObj), {}, {\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex,\n      updateId: updateId,\n      graphicalItems: graphicalItems,\n      stackGroups: stackGroups,\n      offset: offset\n    }));\n    return _objectSpread(_objectSpread({\n      formattedGraphicalItems: formattedGraphicalItems,\n      graphicalItems: graphicalItems,\n      offset: offset,\n      stackGroups: stackGroups\n    }, ticksObj), axisObj);\n  };\n  var CategoricalChartWrapper = /*#__PURE__*/function (_Component) {\n    function CategoricalChartWrapper(_props) {\n      var _props$id, _props$throttleDelay;\n      var _this;\n      _classCallCheck(this, CategoricalChartWrapper);\n      _this = _callSuper(this, CategoricalChartWrapper, [_props]);\n      _defineProperty(_this, \"eventEmitterSymbol\", Symbol('rechartsEventEmitter'));\n      _defineProperty(_this, \"accessibilityManager\", new AccessibilityManager());\n      _defineProperty(_this, \"handleLegendBBoxUpdate\", function (box) {\n        if (box) {\n          var _this$state = _this.state,\n            dataStartIndex = _this$state.dataStartIndex,\n            dataEndIndex = _this$state.dataEndIndex,\n            updateId = _this$state.updateId;\n          _this.setState(_objectSpread({\n            legendBBox: box\n          }, updateStateOfAxisMapsOffsetAndStackGroups({\n            props: _this.props,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex,\n            updateId: updateId\n          }, _objectSpread(_objectSpread({}, _this.state), {}, {\n            legendBBox: box\n          }))));\n        }\n      });\n      _defineProperty(_this, \"handleReceiveSyncEvent\", function (cId, data, emitter) {\n        if (_this.props.syncId === cId) {\n          if (emitter === _this.eventEmitterSymbol && typeof _this.props.syncMethod !== 'function') {\n            return;\n          }\n          _this.applySyncEvent(data);\n        }\n      });\n      _defineProperty(_this, \"handleBrushChange\", function (_ref9) {\n        var startIndex = _ref9.startIndex,\n          endIndex = _ref9.endIndex;\n        // Only trigger changes if the extents of the brush have actually changed\n        if (startIndex !== _this.state.dataStartIndex || endIndex !== _this.state.dataEndIndex) {\n          var updateId = _this.state.updateId;\n          _this.setState(function () {\n            return _objectSpread({\n              dataStartIndex: startIndex,\n              dataEndIndex: endIndex\n            }, updateStateOfAxisMapsOffsetAndStackGroups({\n              props: _this.props,\n              dataStartIndex: startIndex,\n              dataEndIndex: endIndex,\n              updateId: updateId\n            }, _this.state));\n          });\n          _this.triggerSyncEvent({\n            dataStartIndex: startIndex,\n            dataEndIndex: endIndex\n          });\n        }\n      });\n      /**\n       * The handler of mouse entering chart\n       * @param  {Object} e              Event object\n       * @return {Null}                  null\n       */\n      _defineProperty(_this, \"handleMouseEnter\", function (e) {\n        var mouse = _this.getMouseInfo(e);\n        if (mouse) {\n          var _nextState = _objectSpread(_objectSpread({}, mouse), {}, {\n            isTooltipActive: true\n          });\n          _this.setState(_nextState);\n          _this.triggerSyncEvent(_nextState);\n          var onMouseEnter = _this.props.onMouseEnter;\n          if (isFunction(onMouseEnter)) {\n            onMouseEnter(_nextState, e);\n          }\n        }\n      });\n      _defineProperty(_this, \"triggeredAfterMouseMove\", function (e) {\n        var mouse = _this.getMouseInfo(e);\n        var nextState = mouse ? _objectSpread(_objectSpread({}, mouse), {}, {\n          isTooltipActive: true\n        }) : {\n          isTooltipActive: false\n        };\n        _this.setState(nextState);\n        _this.triggerSyncEvent(nextState);\n        var onMouseMove = _this.props.onMouseMove;\n        if (isFunction(onMouseMove)) {\n          onMouseMove(nextState, e);\n        }\n      });\n      /**\n       * The handler of mouse entering a scatter\n       * @param {Object} el The active scatter\n       * @return {Object} no return\n       */\n      _defineProperty(_this, \"handleItemMouseEnter\", function (el) {\n        _this.setState(function () {\n          return {\n            isTooltipActive: true,\n            activeItem: el,\n            activePayload: el.tooltipPayload,\n            activeCoordinate: el.tooltipPosition || {\n              x: el.cx,\n              y: el.cy\n            }\n          };\n        });\n      });\n      /**\n       * The handler of mouse leaving a scatter\n       * @return {Object} no return\n       */\n      _defineProperty(_this, \"handleItemMouseLeave\", function () {\n        _this.setState(function () {\n          return {\n            isTooltipActive: false\n          };\n        });\n      });\n      /**\n       * The handler of mouse moving in chart\n       * @param  {React.MouseEvent} e        Event object\n       * @return {void} no return\n       */\n      _defineProperty(_this, \"handleMouseMove\", function (e) {\n        e.persist();\n        _this.throttleTriggeredAfterMouseMove(e);\n      });\n      /**\n       * The handler if mouse leaving chart\n       * @param {Object} e Event object\n       * @return {Null} no return\n       */\n      _defineProperty(_this, \"handleMouseLeave\", function (e) {\n        _this.throttleTriggeredAfterMouseMove.cancel();\n        var nextState = {\n          isTooltipActive: false\n        };\n        _this.setState(nextState);\n        _this.triggerSyncEvent(nextState);\n        var onMouseLeave = _this.props.onMouseLeave;\n        if (isFunction(onMouseLeave)) {\n          onMouseLeave(nextState, e);\n        }\n      });\n      _defineProperty(_this, \"handleOuterEvent\", function (e) {\n        var eventName = getReactEventByType(e);\n        var event = get(_this.props, \"\".concat(eventName));\n        if (eventName && isFunction(event)) {\n          var _mouse;\n          var mouse;\n          if (/.*touch.*/i.test(eventName)) {\n            mouse = _this.getMouseInfo(e.changedTouches[0]);\n          } else {\n            mouse = _this.getMouseInfo(e);\n          }\n          event((_mouse = mouse) !== null && _mouse !== void 0 ? _mouse : {}, e);\n        }\n      });\n      _defineProperty(_this, \"handleClick\", function (e) {\n        var mouse = _this.getMouseInfo(e);\n        if (mouse) {\n          var _nextState2 = _objectSpread(_objectSpread({}, mouse), {}, {\n            isTooltipActive: true\n          });\n          _this.setState(_nextState2);\n          _this.triggerSyncEvent(_nextState2);\n          var onClick = _this.props.onClick;\n          if (isFunction(onClick)) {\n            onClick(_nextState2, e);\n          }\n        }\n      });\n      _defineProperty(_this, \"handleMouseDown\", function (e) {\n        var onMouseDown = _this.props.onMouseDown;\n        if (isFunction(onMouseDown)) {\n          var _nextState3 = _this.getMouseInfo(e);\n          onMouseDown(_nextState3, e);\n        }\n      });\n      _defineProperty(_this, \"handleMouseUp\", function (e) {\n        var onMouseUp = _this.props.onMouseUp;\n        if (isFunction(onMouseUp)) {\n          var _nextState4 = _this.getMouseInfo(e);\n          onMouseUp(_nextState4, e);\n        }\n      });\n      _defineProperty(_this, \"handleTouchMove\", function (e) {\n        if (e.changedTouches != null && e.changedTouches.length > 0) {\n          _this.throttleTriggeredAfterMouseMove(e.changedTouches[0]);\n        }\n      });\n      _defineProperty(_this, \"handleTouchStart\", function (e) {\n        if (e.changedTouches != null && e.changedTouches.length > 0) {\n          _this.handleMouseDown(e.changedTouches[0]);\n        }\n      });\n      _defineProperty(_this, \"handleTouchEnd\", function (e) {\n        if (e.changedTouches != null && e.changedTouches.length > 0) {\n          _this.handleMouseUp(e.changedTouches[0]);\n        }\n      });\n      _defineProperty(_this, \"handleDoubleClick\", function (e) {\n        var onDoubleClick = _this.props.onDoubleClick;\n        if (isFunction(onDoubleClick)) {\n          var _nextState5 = _this.getMouseInfo(e);\n          onDoubleClick(_nextState5, e);\n        }\n      });\n      _defineProperty(_this, \"handleContextMenu\", function (e) {\n        var onContextMenu = _this.props.onContextMenu;\n        if (isFunction(onContextMenu)) {\n          var _nextState6 = _this.getMouseInfo(e);\n          onContextMenu(_nextState6, e);\n        }\n      });\n      _defineProperty(_this, \"triggerSyncEvent\", function (data) {\n        if (_this.props.syncId !== undefined) {\n          eventCenter.emit(SYNC_EVENT, _this.props.syncId, data, _this.eventEmitterSymbol);\n        }\n      });\n      _defineProperty(_this, \"applySyncEvent\", function (data) {\n        var _this$props = _this.props,\n          layout = _this$props.layout,\n          syncMethod = _this$props.syncMethod;\n        var updateId = _this.state.updateId;\n        var dataStartIndex = data.dataStartIndex,\n          dataEndIndex = data.dataEndIndex;\n        if (data.dataStartIndex !== undefined || data.dataEndIndex !== undefined) {\n          _this.setState(_objectSpread({\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex\n          }, updateStateOfAxisMapsOffsetAndStackGroups({\n            props: _this.props,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex,\n            updateId: updateId\n          }, _this.state)));\n        } else if (data.activeTooltipIndex !== undefined) {\n          var chartX = data.chartX,\n            chartY = data.chartY;\n          var activeTooltipIndex = data.activeTooltipIndex;\n          var _this$state2 = _this.state,\n            offset = _this$state2.offset,\n            tooltipTicks = _this$state2.tooltipTicks;\n          if (!offset) {\n            return;\n          }\n          if (typeof syncMethod === 'function') {\n            // Call a callback function. If there is an application specific algorithm\n            activeTooltipIndex = syncMethod(tooltipTicks, data);\n          } else if (syncMethod === 'value') {\n            // Set activeTooltipIndex to the index with the same value as data.activeLabel\n            // For loop instead of findIndex because the latter is very slow in some browsers\n            activeTooltipIndex = -1; // in case we cannot find the element\n            for (var i = 0; i < tooltipTicks.length; i++) {\n              if (tooltipTicks[i].value === data.activeLabel) {\n                activeTooltipIndex = i;\n                break;\n              }\n            }\n          }\n          var viewBox = _objectSpread(_objectSpread({}, offset), {}, {\n            x: offset.left,\n            y: offset.top\n          });\n          // When a categorical chart is combined with another chart, the value of chartX\n          // and chartY may beyond the boundaries.\n          var validateChartX = Math.min(chartX, viewBox.x + viewBox.width);\n          var validateChartY = Math.min(chartY, viewBox.y + viewBox.height);\n          var activeLabel = tooltipTicks[activeTooltipIndex] && tooltipTicks[activeTooltipIndex].value;\n          var activePayload = getTooltipContent(_this.state, _this.props.data, activeTooltipIndex);\n          var activeCoordinate = tooltipTicks[activeTooltipIndex] ? {\n            x: layout === 'horizontal' ? tooltipTicks[activeTooltipIndex].coordinate : validateChartX,\n            y: layout === 'horizontal' ? validateChartY : tooltipTicks[activeTooltipIndex].coordinate\n          } : originCoordinate;\n          _this.setState(_objectSpread(_objectSpread({}, data), {}, {\n            activeLabel: activeLabel,\n            activeCoordinate: activeCoordinate,\n            activePayload: activePayload,\n            activeTooltipIndex: activeTooltipIndex\n          }));\n        } else {\n          _this.setState(data);\n        }\n      });\n      _defineProperty(_this, \"renderCursor\", function (element) {\n        var _element$props$active;\n        var _this$state3 = _this.state,\n          isTooltipActive = _this$state3.isTooltipActive,\n          activeCoordinate = _this$state3.activeCoordinate,\n          activePayload = _this$state3.activePayload,\n          offset = _this$state3.offset,\n          activeTooltipIndex = _this$state3.activeTooltipIndex,\n          tooltipAxisBandSize = _this$state3.tooltipAxisBandSize;\n        var tooltipEventType = _this.getTooltipEventType();\n        // The cursor is a part of the Tooltip, and it should be shown (by default) when the Tooltip is active.\n        var isActive = (_element$props$active = element.props.active) !== null && _element$props$active !== void 0 ? _element$props$active : isTooltipActive;\n        var layout = _this.props.layout;\n        var key = element.key || '_recharts-cursor';\n        return /*#__PURE__*/React.createElement(Cursor, {\n          key: key,\n          activeCoordinate: activeCoordinate,\n          activePayload: activePayload,\n          activeTooltipIndex: activeTooltipIndex,\n          chartName: chartName,\n          element: element,\n          isActive: isActive,\n          layout: layout,\n          offset: offset,\n          tooltipAxisBandSize: tooltipAxisBandSize,\n          tooltipEventType: tooltipEventType\n        });\n      });\n      _defineProperty(_this, \"renderPolarAxis\", function (element, displayName, index) {\n        var axisType = get(element, 'type.axisType');\n        var axisMap = get(_this.state, \"\".concat(axisType, \"Map\"));\n        var elementDefaultProps = element.type.defaultProps;\n        var elementProps = elementDefaultProps !== undefined ? _objectSpread(_objectSpread({}, elementDefaultProps), element.props) : element.props;\n        var axisOption = axisMap && axisMap[elementProps[\"\".concat(axisType, \"Id\")]];\n        return /*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread({}, axisOption), {}, {\n          className: clsx(axisType, axisOption.className),\n          key: element.key || \"\".concat(displayName, \"-\").concat(index),\n          ticks: getTicksOfAxis(axisOption, true)\n        }));\n      });\n      _defineProperty(_this, \"renderPolarGrid\", function (element) {\n        var _element$props = element.props,\n          radialLines = _element$props.radialLines,\n          polarAngles = _element$props.polarAngles,\n          polarRadius = _element$props.polarRadius;\n        var _this$state4 = _this.state,\n          radiusAxisMap = _this$state4.radiusAxisMap,\n          angleAxisMap = _this$state4.angleAxisMap;\n        var radiusAxis = getAnyElementOfObject(radiusAxisMap);\n        var angleAxis = getAnyElementOfObject(angleAxisMap);\n        var cx = angleAxis.cx,\n          cy = angleAxis.cy,\n          innerRadius = angleAxis.innerRadius,\n          outerRadius = angleAxis.outerRadius;\n        return /*#__PURE__*/cloneElement(element, {\n          polarAngles: Array.isArray(polarAngles) ? polarAngles : getTicksOfAxis(angleAxis, true).map(function (entry) {\n            return entry.coordinate;\n          }),\n          polarRadius: Array.isArray(polarRadius) ? polarRadius : getTicksOfAxis(radiusAxis, true).map(function (entry) {\n            return entry.coordinate;\n          }),\n          cx: cx,\n          cy: cy,\n          innerRadius: innerRadius,\n          outerRadius: outerRadius,\n          key: element.key || 'polar-grid',\n          radialLines: radialLines\n        });\n      });\n      /**\n       * Draw legend\n       * @return {ReactElement}            The instance of Legend\n       */\n      _defineProperty(_this, \"renderLegend\", function () {\n        var formattedGraphicalItems = _this.state.formattedGraphicalItems;\n        var _this$props2 = _this.props,\n          children = _this$props2.children,\n          width = _this$props2.width,\n          height = _this$props2.height;\n        var margin = _this.props.margin || {};\n        var legendWidth = width - (margin.left || 0) - (margin.right || 0);\n        var props = getLegendProps({\n          children: children,\n          formattedGraphicalItems: formattedGraphicalItems,\n          legendWidth: legendWidth,\n          legendContent: legendContent\n        });\n        if (!props) {\n          return null;\n        }\n        var item = props.item,\n          otherProps = _objectWithoutProperties(props, _excluded);\n        return /*#__PURE__*/cloneElement(item, _objectSpread(_objectSpread({}, otherProps), {}, {\n          chartWidth: width,\n          chartHeight: height,\n          margin: margin,\n          onBBoxUpdate: _this.handleLegendBBoxUpdate\n        }));\n      });\n      /**\n       * Draw Tooltip\n       * @return {ReactElement}  The instance of Tooltip\n       */\n      _defineProperty(_this, \"renderTooltip\", function () {\n        var _tooltipItem$props$ac;\n        var _this$props3 = _this.props,\n          children = _this$props3.children,\n          accessibilityLayer = _this$props3.accessibilityLayer;\n        var tooltipItem = findChildByType(children, Tooltip);\n        if (!tooltipItem) {\n          return null;\n        }\n        var _this$state5 = _this.state,\n          isTooltipActive = _this$state5.isTooltipActive,\n          activeCoordinate = _this$state5.activeCoordinate,\n          activePayload = _this$state5.activePayload,\n          activeLabel = _this$state5.activeLabel,\n          offset = _this$state5.offset;\n\n        // The user can set isActive on the Tooltip,\n        // and we respect the user to enable customisation.\n        // The Tooltip is active if the user has set isActive, or if the tooltip is active due to a mouse event.\n        var isActive = (_tooltipItem$props$ac = tooltipItem.props.active) !== null && _tooltipItem$props$ac !== void 0 ? _tooltipItem$props$ac : isTooltipActive;\n        return /*#__PURE__*/cloneElement(tooltipItem, {\n          viewBox: _objectSpread(_objectSpread({}, offset), {}, {\n            x: offset.left,\n            y: offset.top\n          }),\n          active: isActive,\n          label: activeLabel,\n          payload: isActive ? activePayload : [],\n          coordinate: activeCoordinate,\n          accessibilityLayer: accessibilityLayer\n        });\n      });\n      _defineProperty(_this, \"renderBrush\", function (element) {\n        var _this$props4 = _this.props,\n          margin = _this$props4.margin,\n          data = _this$props4.data;\n        var _this$state6 = _this.state,\n          offset = _this$state6.offset,\n          dataStartIndex = _this$state6.dataStartIndex,\n          dataEndIndex = _this$state6.dataEndIndex,\n          updateId = _this$state6.updateId;\n\n        // TODO: update brush when children update\n        return /*#__PURE__*/cloneElement(element, {\n          key: element.key || '_recharts-brush',\n          onChange: combineEventHandlers(_this.handleBrushChange, element.props.onChange),\n          data: data,\n          x: isNumber(element.props.x) ? element.props.x : offset.left,\n          y: isNumber(element.props.y) ? element.props.y : offset.top + offset.height + offset.brushBottom - (margin.bottom || 0),\n          width: isNumber(element.props.width) ? element.props.width : offset.width,\n          startIndex: dataStartIndex,\n          endIndex: dataEndIndex,\n          updateId: \"brush-\".concat(updateId)\n        });\n      });\n      _defineProperty(_this, \"renderReferenceElement\", function (element, displayName, index) {\n        if (!element) {\n          return null;\n        }\n        var _this2 = _this,\n          clipPathId = _this2.clipPathId;\n        var _this$state7 = _this.state,\n          xAxisMap = _this$state7.xAxisMap,\n          yAxisMap = _this$state7.yAxisMap,\n          offset = _this$state7.offset;\n        var elementDefaultProps = element.type.defaultProps || {};\n        var _element$props2 = element.props,\n          _element$props2$xAxis = _element$props2.xAxisId,\n          xAxisId = _element$props2$xAxis === void 0 ? elementDefaultProps.xAxisId : _element$props2$xAxis,\n          _element$props2$yAxis = _element$props2.yAxisId,\n          yAxisId = _element$props2$yAxis === void 0 ? elementDefaultProps.yAxisId : _element$props2$yAxis;\n        return /*#__PURE__*/cloneElement(element, {\n          key: element.key || \"\".concat(displayName, \"-\").concat(index),\n          xAxis: xAxisMap[xAxisId],\n          yAxis: yAxisMap[yAxisId],\n          viewBox: {\n            x: offset.left,\n            y: offset.top,\n            width: offset.width,\n            height: offset.height\n          },\n          clipPathId: clipPathId\n        });\n      });\n      _defineProperty(_this, \"renderActivePoints\", function (_ref10) {\n        var item = _ref10.item,\n          activePoint = _ref10.activePoint,\n          basePoint = _ref10.basePoint,\n          childIndex = _ref10.childIndex,\n          isRange = _ref10.isRange;\n        var result = [];\n        // item is not a React Element so we don't need to resolve defaultProps.\n        var key = item.props.key;\n        var itemItemProps = item.item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.item.type.defaultProps), item.item.props) : item.item.props;\n        var activeDot = itemItemProps.activeDot,\n          dataKey = itemItemProps.dataKey;\n        var dotProps = _objectSpread(_objectSpread({\n          index: childIndex,\n          dataKey: dataKey,\n          cx: activePoint.x,\n          cy: activePoint.y,\n          r: 4,\n          fill: getMainColorOfGraphicItem(item.item),\n          strokeWidth: 2,\n          stroke: '#fff',\n          payload: activePoint.payload,\n          value: activePoint.value\n        }, filterProps(activeDot, false)), adaptEventHandlers(activeDot));\n        result.push(CategoricalChartWrapper.renderActiveDot(activeDot, dotProps, \"\".concat(key, \"-activePoint-\").concat(childIndex)));\n        if (basePoint) {\n          result.push(CategoricalChartWrapper.renderActiveDot(activeDot, _objectSpread(_objectSpread({}, dotProps), {}, {\n            cx: basePoint.x,\n            cy: basePoint.y\n          }), \"\".concat(key, \"-basePoint-\").concat(childIndex)));\n        } else if (isRange) {\n          result.push(null);\n        }\n        return result;\n      });\n      _defineProperty(_this, \"renderGraphicChild\", function (element, displayName, index) {\n        var item = _this.filterFormatItem(element, displayName, index);\n        if (!item) {\n          return null;\n        }\n        var tooltipEventType = _this.getTooltipEventType();\n        var _this$state8 = _this.state,\n          isTooltipActive = _this$state8.isTooltipActive,\n          tooltipAxis = _this$state8.tooltipAxis,\n          activeTooltipIndex = _this$state8.activeTooltipIndex,\n          activeLabel = _this$state8.activeLabel;\n        var children = _this.props.children;\n        var tooltipItem = findChildByType(children, Tooltip);\n        // item is not a React Element so we don't need to resolve defaultProps\n        var _item$props = item.props,\n          points = _item$props.points,\n          isRange = _item$props.isRange,\n          baseLine = _item$props.baseLine;\n        var itemItemProps = item.item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.item.type.defaultProps), item.item.props) : item.item.props;\n        var activeDot = itemItemProps.activeDot,\n          hide = itemItemProps.hide,\n          activeBar = itemItemProps.activeBar,\n          activeShape = itemItemProps.activeShape;\n        var hasActive = Boolean(!hide && isTooltipActive && tooltipItem && (activeDot || activeBar || activeShape));\n        var itemEvents = {};\n        if (tooltipEventType !== 'axis' && tooltipItem && tooltipItem.props.trigger === 'click') {\n          itemEvents = {\n            onClick: combineEventHandlers(_this.handleItemMouseEnter, element.props.onClick)\n          };\n        } else if (tooltipEventType !== 'axis') {\n          itemEvents = {\n            onMouseLeave: combineEventHandlers(_this.handleItemMouseLeave, element.props.onMouseLeave),\n            onMouseEnter: combineEventHandlers(_this.handleItemMouseEnter, element.props.onMouseEnter)\n          };\n        }\n        var graphicalItem = /*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread({}, item.props), itemEvents));\n        function findWithPayload(entry) {\n          // TODO needs to verify dataKey is Function\n          return typeof tooltipAxis.dataKey === 'function' ? tooltipAxis.dataKey(entry.payload) : null;\n        }\n        if (hasActive) {\n          if (activeTooltipIndex >= 0) {\n            var activePoint, basePoint;\n            if (tooltipAxis.dataKey && !tooltipAxis.allowDuplicatedCategory) {\n              // number transform to string\n              var specifiedKey = typeof tooltipAxis.dataKey === 'function' ? findWithPayload : 'payload.'.concat(tooltipAxis.dataKey.toString());\n              activePoint = findEntryInArray(points, specifiedKey, activeLabel);\n              basePoint = isRange && baseLine && findEntryInArray(baseLine, specifiedKey, activeLabel);\n            } else {\n              activePoint = points === null || points === void 0 ? void 0 : points[activeTooltipIndex];\n              basePoint = isRange && baseLine && baseLine[activeTooltipIndex];\n            }\n            if (activeShape || activeBar) {\n              var activeIndex = element.props.activeIndex !== undefined ? element.props.activeIndex : activeTooltipIndex;\n              return [/*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread(_objectSpread({}, item.props), itemEvents), {}, {\n                activeIndex: activeIndex\n              })), null, null];\n            }\n            if (!isNil(activePoint)) {\n              return [graphicalItem].concat(_toConsumableArray(_this.renderActivePoints({\n                item: item,\n                activePoint: activePoint,\n                basePoint: basePoint,\n                childIndex: activeTooltipIndex,\n                isRange: isRange\n              })));\n            }\n          } else {\n            var _this$getItemByXY;\n            /**\n             * We hit this block if consumer uses a Tooltip without XAxis and/or YAxis.\n             * In which case, this.state.activeTooltipIndex never gets set\n             * because the mouse events that trigger that value getting set never get trigged without the axis components.\n             *\n             * An example usage case is a FunnelChart\n             */\n            var _ref11 = (_this$getItemByXY = _this.getItemByXY(_this.state.activeCoordinate)) !== null && _this$getItemByXY !== void 0 ? _this$getItemByXY : {\n                graphicalItem: graphicalItem\n              },\n              _ref11$graphicalItem = _ref11.graphicalItem,\n              _ref11$graphicalItem$ = _ref11$graphicalItem.item,\n              xyItem = _ref11$graphicalItem$ === void 0 ? element : _ref11$graphicalItem$,\n              childIndex = _ref11$graphicalItem.childIndex;\n            var elementProps = _objectSpread(_objectSpread(_objectSpread({}, item.props), itemEvents), {}, {\n              activeIndex: childIndex\n            });\n            return [/*#__PURE__*/cloneElement(xyItem, elementProps), null, null];\n          }\n        }\n        if (isRange) {\n          return [graphicalItem, null, null];\n        }\n        return [graphicalItem, null];\n      });\n      _defineProperty(_this, \"renderCustomized\", function (element, displayName, index) {\n        return /*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread({\n          key: \"recharts-customized-\".concat(index)\n        }, _this.props), _this.state));\n      });\n      _defineProperty(_this, \"renderMap\", {\n        CartesianGrid: {\n          handler: renderAsIs,\n          once: true\n        },\n        ReferenceArea: {\n          handler: _this.renderReferenceElement\n        },\n        ReferenceLine: {\n          handler: renderAsIs\n        },\n        ReferenceDot: {\n          handler: _this.renderReferenceElement\n        },\n        XAxis: {\n          handler: renderAsIs\n        },\n        YAxis: {\n          handler: renderAsIs\n        },\n        Brush: {\n          handler: _this.renderBrush,\n          once: true\n        },\n        Bar: {\n          handler: _this.renderGraphicChild\n        },\n        Line: {\n          handler: _this.renderGraphicChild\n        },\n        Area: {\n          handler: _this.renderGraphicChild\n        },\n        Radar: {\n          handler: _this.renderGraphicChild\n        },\n        RadialBar: {\n          handler: _this.renderGraphicChild\n        },\n        Scatter: {\n          handler: _this.renderGraphicChild\n        },\n        Pie: {\n          handler: _this.renderGraphicChild\n        },\n        Funnel: {\n          handler: _this.renderGraphicChild\n        },\n        Tooltip: {\n          handler: _this.renderCursor,\n          once: true\n        },\n        PolarGrid: {\n          handler: _this.renderPolarGrid,\n          once: true\n        },\n        PolarAngleAxis: {\n          handler: _this.renderPolarAxis\n        },\n        PolarRadiusAxis: {\n          handler: _this.renderPolarAxis\n        },\n        Customized: {\n          handler: _this.renderCustomized\n        }\n      });\n      _this.clipPathId = \"\".concat((_props$id = _props.id) !== null && _props$id !== void 0 ? _props$id : uniqueId('recharts'), \"-clip\");\n\n      // trigger 60fps\n      _this.throttleTriggeredAfterMouseMove = throttle(_this.triggeredAfterMouseMove, (_props$throttleDelay = _props.throttleDelay) !== null && _props$throttleDelay !== void 0 ? _props$throttleDelay : 1000 / 60);\n      _this.state = {};\n      return _this;\n    }\n    _inherits(CategoricalChartWrapper, _Component);\n    return _createClass(CategoricalChartWrapper, [{\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        var _this$props$margin$le, _this$props$margin$to;\n        this.addListener();\n        this.accessibilityManager.setDetails({\n          container: this.container,\n          offset: {\n            left: (_this$props$margin$le = this.props.margin.left) !== null && _this$props$margin$le !== void 0 ? _this$props$margin$le : 0,\n            top: (_this$props$margin$to = this.props.margin.top) !== null && _this$props$margin$to !== void 0 ? _this$props$margin$to : 0\n          },\n          coordinateList: this.state.tooltipTicks,\n          mouseHandlerCallback: this.triggeredAfterMouseMove,\n          layout: this.props.layout\n        });\n        this.displayDefaultTooltip();\n      }\n    }, {\n      key: \"displayDefaultTooltip\",\n      value: function displayDefaultTooltip() {\n        var _this$props5 = this.props,\n          children = _this$props5.children,\n          data = _this$props5.data,\n          height = _this$props5.height,\n          layout = _this$props5.layout;\n        var tooltipElem = findChildByType(children, Tooltip);\n        // If the chart doesn't include a <Tooltip /> element, there's no tooltip to display\n        if (!tooltipElem) {\n          return;\n        }\n        var defaultIndex = tooltipElem.props.defaultIndex;\n\n        // Protect against runtime errors\n        if (typeof defaultIndex !== 'number' || defaultIndex < 0 || defaultIndex > this.state.tooltipTicks.length - 1) {\n          return;\n        }\n        var activeLabel = this.state.tooltipTicks[defaultIndex] && this.state.tooltipTicks[defaultIndex].value;\n        var activePayload = getTooltipContent(this.state, data, defaultIndex, activeLabel);\n        var independentAxisCoord = this.state.tooltipTicks[defaultIndex].coordinate;\n        var dependentAxisCoord = (this.state.offset.top + height) / 2;\n        var isHorizontal = layout === 'horizontal';\n        var activeCoordinate = isHorizontal ? {\n          x: independentAxisCoord,\n          y: dependentAxisCoord\n        } : {\n          y: independentAxisCoord,\n          x: dependentAxisCoord\n        };\n\n        // Unlike other chart types, scatter plot's tooltip positions rely on both X and Y coordinates. Only the scatter plot\n        // element knows its own Y coordinates.\n        // If there's a scatter plot, we'll want to grab that element for an interrogation.\n        var scatterPlotElement = this.state.formattedGraphicalItems.find(function (_ref12) {\n          var item = _ref12.item;\n          return item.type.name === 'Scatter';\n        });\n        if (scatterPlotElement) {\n          activeCoordinate = _objectSpread(_objectSpread({}, activeCoordinate), scatterPlotElement.props.points[defaultIndex].tooltipPosition);\n          activePayload = scatterPlotElement.props.points[defaultIndex].tooltipPayload;\n        }\n        var nextState = {\n          activeTooltipIndex: defaultIndex,\n          isTooltipActive: true,\n          activeLabel: activeLabel,\n          activePayload: activePayload,\n          activeCoordinate: activeCoordinate\n        };\n        this.setState(nextState);\n        this.renderCursor(tooltipElem);\n\n        // Make sure that anyone who keyboard-only users who tab to the chart will start their\n        // cursors at defaultIndex\n        this.accessibilityManager.setIndex(defaultIndex);\n      }\n    }, {\n      key: \"getSnapshotBeforeUpdate\",\n      value: function getSnapshotBeforeUpdate(prevProps, prevState) {\n        if (!this.props.accessibilityLayer) {\n          return null;\n        }\n        if (this.state.tooltipTicks !== prevState.tooltipTicks) {\n          this.accessibilityManager.setDetails({\n            coordinateList: this.state.tooltipTicks\n          });\n        }\n        if (this.props.layout !== prevProps.layout) {\n          this.accessibilityManager.setDetails({\n            layout: this.props.layout\n          });\n        }\n        if (this.props.margin !== prevProps.margin) {\n          var _this$props$margin$le2, _this$props$margin$to2;\n          this.accessibilityManager.setDetails({\n            offset: {\n              left: (_this$props$margin$le2 = this.props.margin.left) !== null && _this$props$margin$le2 !== void 0 ? _this$props$margin$le2 : 0,\n              top: (_this$props$margin$to2 = this.props.margin.top) !== null && _this$props$margin$to2 !== void 0 ? _this$props$margin$to2 : 0\n            }\n          });\n        }\n\n        // Something has to be returned for getSnapshotBeforeUpdate\n        return null;\n      }\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate(prevProps) {\n        // Check to see if the Tooltip updated. If so, re-check default tooltip position\n        if (!isChildrenEqual([findChildByType(prevProps.children, Tooltip)], [findChildByType(this.props.children, Tooltip)])) {\n          this.displayDefaultTooltip();\n        }\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        this.removeListener();\n        this.throttleTriggeredAfterMouseMove.cancel();\n      }\n    }, {\n      key: \"getTooltipEventType\",\n      value: function getTooltipEventType() {\n        var tooltipItem = findChildByType(this.props.children, Tooltip);\n        if (tooltipItem && typeof tooltipItem.props.shared === 'boolean') {\n          var eventType = tooltipItem.props.shared ? 'axis' : 'item';\n          return validateTooltipEventTypes.indexOf(eventType) >= 0 ? eventType : defaultTooltipEventType;\n        }\n        return defaultTooltipEventType;\n      }\n\n      /**\n       * Get the information of mouse in chart, return null when the mouse is not in the chart\n       * @param  {MousePointer} event    The event object\n       * @return {Object}          Mouse data\n       */\n    }, {\n      key: \"getMouseInfo\",\n      value: function getMouseInfo(event) {\n        if (!this.container) {\n          return null;\n        }\n        var element = this.container;\n        var boundingRect = element.getBoundingClientRect();\n        var containerOffset = getOffset(boundingRect);\n        var e = {\n          chartX: Math.round(event.pageX - containerOffset.left),\n          chartY: Math.round(event.pageY - containerOffset.top)\n        };\n        var scale = boundingRect.width / element.offsetWidth || 1;\n        var rangeObj = this.inRange(e.chartX, e.chartY, scale);\n        if (!rangeObj) {\n          return null;\n        }\n        var _this$state9 = this.state,\n          xAxisMap = _this$state9.xAxisMap,\n          yAxisMap = _this$state9.yAxisMap;\n        var tooltipEventType = this.getTooltipEventType();\n        var toolTipData = getTooltipData(this.state, this.props.data, this.props.layout, rangeObj);\n        if (tooltipEventType !== 'axis' && xAxisMap && yAxisMap) {\n          var xScale = getAnyElementOfObject(xAxisMap).scale;\n          var yScale = getAnyElementOfObject(yAxisMap).scale;\n          var xValue = xScale && xScale.invert ? xScale.invert(e.chartX) : null;\n          var yValue = yScale && yScale.invert ? yScale.invert(e.chartY) : null;\n          return _objectSpread(_objectSpread({}, e), {}, {\n            xValue: xValue,\n            yValue: yValue\n          }, toolTipData);\n        }\n        if (toolTipData) {\n          return _objectSpread(_objectSpread({}, e), toolTipData);\n        }\n        return null;\n      }\n    }, {\n      key: \"inRange\",\n      value: function inRange(x, y) {\n        var scale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n        var layout = this.props.layout;\n        var scaledX = x / scale,\n          scaledY = y / scale;\n        if (layout === 'horizontal' || layout === 'vertical') {\n          var offset = this.state.offset;\n          var isInRange = scaledX >= offset.left && scaledX <= offset.left + offset.width && scaledY >= offset.top && scaledY <= offset.top + offset.height;\n          return isInRange ? {\n            x: scaledX,\n            y: scaledY\n          } : null;\n        }\n        var _this$state10 = this.state,\n          angleAxisMap = _this$state10.angleAxisMap,\n          radiusAxisMap = _this$state10.radiusAxisMap;\n        if (angleAxisMap && radiusAxisMap) {\n          var angleAxis = getAnyElementOfObject(angleAxisMap);\n          return inRangeOfSector({\n            x: scaledX,\n            y: scaledY\n          }, angleAxis);\n        }\n        return null;\n      }\n    }, {\n      key: \"parseEventsOfWrapper\",\n      value: function parseEventsOfWrapper() {\n        var children = this.props.children;\n        var tooltipEventType = this.getTooltipEventType();\n        var tooltipItem = findChildByType(children, Tooltip);\n        var tooltipEvents = {};\n        if (tooltipItem && tooltipEventType === 'axis') {\n          if (tooltipItem.props.trigger === 'click') {\n            tooltipEvents = {\n              onClick: this.handleClick\n            };\n          } else {\n            tooltipEvents = {\n              onMouseEnter: this.handleMouseEnter,\n              onDoubleClick: this.handleDoubleClick,\n              onMouseMove: this.handleMouseMove,\n              onMouseLeave: this.handleMouseLeave,\n              onTouchMove: this.handleTouchMove,\n              onTouchStart: this.handleTouchStart,\n              onTouchEnd: this.handleTouchEnd,\n              onContextMenu: this.handleContextMenu\n            };\n          }\n        }\n\n        // @ts-expect-error adaptEventHandlers expects DOM Event but generateCategoricalChart works with React UIEvents\n        var outerEvents = adaptEventHandlers(this.props, this.handleOuterEvent);\n        return _objectSpread(_objectSpread({}, outerEvents), tooltipEvents);\n      }\n    }, {\n      key: \"addListener\",\n      value: function addListener() {\n        eventCenter.on(SYNC_EVENT, this.handleReceiveSyncEvent);\n      }\n    }, {\n      key: \"removeListener\",\n      value: function removeListener() {\n        eventCenter.removeListener(SYNC_EVENT, this.handleReceiveSyncEvent);\n      }\n    }, {\n      key: \"filterFormatItem\",\n      value: function filterFormatItem(item, displayName, childIndex) {\n        var formattedGraphicalItems = this.state.formattedGraphicalItems;\n        for (var i = 0, len = formattedGraphicalItems.length; i < len; i++) {\n          var entry = formattedGraphicalItems[i];\n          if (entry.item === item || entry.props.key === item.key || displayName === getDisplayName(entry.item.type) && childIndex === entry.childIndex) {\n            return entry;\n          }\n        }\n        return null;\n      }\n    }, {\n      key: \"renderClipPath\",\n      value: function renderClipPath() {\n        var clipPathId = this.clipPathId;\n        var _this$state$offset = this.state.offset,\n          left = _this$state$offset.left,\n          top = _this$state$offset.top,\n          height = _this$state$offset.height,\n          width = _this$state$offset.width;\n        return /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n          id: clipPathId\n        }, /*#__PURE__*/React.createElement(\"rect\", {\n          x: left,\n          y: top,\n          height: height,\n          width: width\n        })));\n      }\n    }, {\n      key: \"getXScales\",\n      value: function getXScales() {\n        var xAxisMap = this.state.xAxisMap;\n        return xAxisMap ? Object.entries(xAxisMap).reduce(function (res, _ref13) {\n          var _ref14 = _slicedToArray(_ref13, 2),\n            axisId = _ref14[0],\n            axisProps = _ref14[1];\n          return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, axisId, axisProps.scale));\n        }, {}) : null;\n      }\n    }, {\n      key: \"getYScales\",\n      value: function getYScales() {\n        var yAxisMap = this.state.yAxisMap;\n        return yAxisMap ? Object.entries(yAxisMap).reduce(function (res, _ref15) {\n          var _ref16 = _slicedToArray(_ref15, 2),\n            axisId = _ref16[0],\n            axisProps = _ref16[1];\n          return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, axisId, axisProps.scale));\n        }, {}) : null;\n      }\n    }, {\n      key: \"getXScaleByAxisId\",\n      value: function getXScaleByAxisId(axisId) {\n        var _this$state$xAxisMap;\n        return (_this$state$xAxisMap = this.state.xAxisMap) === null || _this$state$xAxisMap === void 0 || (_this$state$xAxisMap = _this$state$xAxisMap[axisId]) === null || _this$state$xAxisMap === void 0 ? void 0 : _this$state$xAxisMap.scale;\n      }\n    }, {\n      key: \"getYScaleByAxisId\",\n      value: function getYScaleByAxisId(axisId) {\n        var _this$state$yAxisMap;\n        return (_this$state$yAxisMap = this.state.yAxisMap) === null || _this$state$yAxisMap === void 0 || (_this$state$yAxisMap = _this$state$yAxisMap[axisId]) === null || _this$state$yAxisMap === void 0 ? void 0 : _this$state$yAxisMap.scale;\n      }\n    }, {\n      key: \"getItemByXY\",\n      value: function getItemByXY(chartXY) {\n        var _this$state11 = this.state,\n          formattedGraphicalItems = _this$state11.formattedGraphicalItems,\n          activeItem = _this$state11.activeItem;\n        if (formattedGraphicalItems && formattedGraphicalItems.length) {\n          for (var i = 0, len = formattedGraphicalItems.length; i < len; i++) {\n            var graphicalItem = formattedGraphicalItems[i];\n            // graphicalItem is not a React Element so we don't need to resolve defaultProps\n            var props = graphicalItem.props,\n              item = graphicalItem.item;\n            var itemProps = item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n            var itemDisplayName = getDisplayName(item.type);\n            if (itemDisplayName === 'Bar') {\n              var activeBarItem = (props.data || []).find(function (entry) {\n                return isInRectangle(chartXY, entry);\n              });\n              if (activeBarItem) {\n                return {\n                  graphicalItem: graphicalItem,\n                  payload: activeBarItem\n                };\n              }\n            } else if (itemDisplayName === 'RadialBar') {\n              var _activeBarItem = (props.data || []).find(function (entry) {\n                return inRangeOfSector(chartXY, entry);\n              });\n              if (_activeBarItem) {\n                return {\n                  graphicalItem: graphicalItem,\n                  payload: _activeBarItem\n                };\n              }\n            } else if (isFunnel(graphicalItem, activeItem) || isPie(graphicalItem, activeItem) || isScatter(graphicalItem, activeItem)) {\n              var activeIndex = getActiveShapeIndexForTooltip({\n                graphicalItem: graphicalItem,\n                activeTooltipItem: activeItem,\n                itemData: itemProps.data\n              });\n              var childIndex = itemProps.activeIndex === undefined ? activeIndex : itemProps.activeIndex;\n              return {\n                graphicalItem: _objectSpread(_objectSpread({}, graphicalItem), {}, {\n                  childIndex: childIndex\n                }),\n                payload: isScatter(graphicalItem, activeItem) ? itemProps.data[activeIndex] : graphicalItem.props.data[activeIndex]\n              };\n            }\n          }\n        }\n        return null;\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var _this3 = this;\n        if (!validateWidthHeight(this)) {\n          return null;\n        }\n        var _this$props6 = this.props,\n          children = _this$props6.children,\n          className = _this$props6.className,\n          width = _this$props6.width,\n          height = _this$props6.height,\n          style = _this$props6.style,\n          compact = _this$props6.compact,\n          title = _this$props6.title,\n          desc = _this$props6.desc,\n          others = _objectWithoutProperties(_this$props6, _excluded2);\n        var attrs = filterProps(others, false);\n\n        // The \"compact\" mode is mainly used as the panorama within Brush\n        if (compact) {\n          return /*#__PURE__*/React.createElement(ChartLayoutContextProvider, {\n            state: this.state,\n            width: this.props.width,\n            height: this.props.height,\n            clipPathId: this.clipPathId\n          }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n            width: width,\n            height: height,\n            title: title,\n            desc: desc\n          }), this.renderClipPath(), renderByOrder(children, this.renderMap)));\n        }\n        if (this.props.accessibilityLayer) {\n          var _this$props$tabIndex, _this$props$role;\n          // Set tabIndex to 0 by default (can be overwritten)\n          attrs.tabIndex = (_this$props$tabIndex = this.props.tabIndex) !== null && _this$props$tabIndex !== void 0 ? _this$props$tabIndex : 0;\n          // Set role to img by default (can be overwritten)\n          attrs.role = (_this$props$role = this.props.role) !== null && _this$props$role !== void 0 ? _this$props$role : 'application';\n          attrs.onKeyDown = function (e) {\n            _this3.accessibilityManager.keyboardEvent(e);\n            // 'onKeyDown' is not currently a supported prop that can be passed through\n            // if it's added, this should be added: this.props.onKeyDown(e);\n          };\n          attrs.onFocus = function () {\n            _this3.accessibilityManager.focus();\n            // 'onFocus' is not currently a supported prop that can be passed through\n            // if it's added, the focus event should be forwarded to the prop\n          };\n        }\n        var events = this.parseEventsOfWrapper();\n        return /*#__PURE__*/React.createElement(ChartLayoutContextProvider, {\n          state: this.state,\n          width: this.props.width,\n          height: this.props.height,\n          clipPathId: this.clipPathId\n        }, /*#__PURE__*/React.createElement(\"div\", _extends({\n          className: clsx('recharts-wrapper', className),\n          style: _objectSpread({\n            position: 'relative',\n            cursor: 'default',\n            width: width,\n            height: height\n          }, style)\n        }, events, {\n          ref: function ref(node) {\n            _this3.container = node;\n          }\n        }), /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n          width: width,\n          height: height,\n          title: title,\n          desc: desc,\n          style: FULL_WIDTH_AND_HEIGHT\n        }), this.renderClipPath(), renderByOrder(children, this.renderMap)), this.renderLegend(), this.renderTooltip()));\n      }\n    }]);\n  }(Component);\n  _defineProperty(CategoricalChartWrapper, \"displayName\", chartName);\n  // todo join specific chart propTypes\n  _defineProperty(CategoricalChartWrapper, \"defaultProps\", _objectSpread({\n    layout: 'horizontal',\n    stackOffset: 'none',\n    barCategoryGap: '10%',\n    barGap: 4,\n    margin: {\n      top: 5,\n      right: 5,\n      bottom: 5,\n      left: 5\n    },\n    reverseStackOrder: false,\n    syncMethod: 'index'\n  }, defaultProps));\n  _defineProperty(CategoricalChartWrapper, \"getDerivedStateFromProps\", function (nextProps, prevState) {\n    var dataKey = nextProps.dataKey,\n      data = nextProps.data,\n      children = nextProps.children,\n      width = nextProps.width,\n      height = nextProps.height,\n      layout = nextProps.layout,\n      stackOffset = nextProps.stackOffset,\n      margin = nextProps.margin;\n    var dataStartIndex = prevState.dataStartIndex,\n      dataEndIndex = prevState.dataEndIndex;\n    if (prevState.updateId === undefined) {\n      var defaultState = createDefaultState(nextProps);\n      return _objectSpread(_objectSpread(_objectSpread({}, defaultState), {}, {\n        updateId: 0\n      }, updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread(_objectSpread({\n        props: nextProps\n      }, defaultState), {}, {\n        updateId: 0\n      }), prevState)), {}, {\n        prevDataKey: dataKey,\n        prevData: data,\n        prevWidth: width,\n        prevHeight: height,\n        prevLayout: layout,\n        prevStackOffset: stackOffset,\n        prevMargin: margin,\n        prevChildren: children\n      });\n    }\n    if (dataKey !== prevState.prevDataKey || data !== prevState.prevData || width !== prevState.prevWidth || height !== prevState.prevHeight || layout !== prevState.prevLayout || stackOffset !== prevState.prevStackOffset || !shallowEqual(margin, prevState.prevMargin)) {\n      var _defaultState = createDefaultState(nextProps);\n\n      // Fixes https://github.com/recharts/recharts/issues/2143\n      var keepFromPrevState = {\n        // (chartX, chartY) are (0,0) in default state, but we want to keep the last mouse position to avoid\n        // any flickering\n        chartX: prevState.chartX,\n        chartY: prevState.chartY,\n        // The tooltip should stay active when it was active in the previous render. If this is not\n        // the case, the tooltip disappears and immediately re-appears, causing a flickering effect\n        isTooltipActive: prevState.isTooltipActive\n      };\n      var updatesToState = _objectSpread(_objectSpread({}, getTooltipData(prevState, data, layout)), {}, {\n        updateId: prevState.updateId + 1\n      });\n      var newState = _objectSpread(_objectSpread(_objectSpread({}, _defaultState), keepFromPrevState), updatesToState);\n      return _objectSpread(_objectSpread(_objectSpread({}, newState), updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread({\n        props: nextProps\n      }, newState), prevState)), {}, {\n        prevDataKey: dataKey,\n        prevData: data,\n        prevWidth: width,\n        prevHeight: height,\n        prevLayout: layout,\n        prevStackOffset: stackOffset,\n        prevMargin: margin,\n        prevChildren: children\n      });\n    }\n    if (!isChildrenEqual(children, prevState.prevChildren)) {\n      var _brush$props$startInd, _brush$props, _brush$props$endIndex, _brush$props2;\n      // specifically check for Brush - if it exists and the start and end indexes are different, re-render with the new ones\n      var brush = findChildByType(children, Brush);\n      var startIndex = brush ? (_brush$props$startInd = (_brush$props = brush.props) === null || _brush$props === void 0 ? void 0 : _brush$props.startIndex) !== null && _brush$props$startInd !== void 0 ? _brush$props$startInd : dataStartIndex : dataStartIndex;\n      var endIndex = brush ? (_brush$props$endIndex = (_brush$props2 = brush.props) === null || _brush$props2 === void 0 ? void 0 : _brush$props2.endIndex) !== null && _brush$props$endIndex !== void 0 ? _brush$props$endIndex : dataEndIndex : dataEndIndex;\n      var hasDifferentStartOrEndIndex = startIndex !== dataStartIndex || endIndex !== dataEndIndex;\n\n      // update configuration in children\n      var hasGlobalData = !isNil(data);\n      var newUpdateId = hasGlobalData && !hasDifferentStartOrEndIndex ? prevState.updateId : prevState.updateId + 1;\n      return _objectSpread(_objectSpread({\n        updateId: newUpdateId\n      }, updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread(_objectSpread({\n        props: nextProps\n      }, prevState), {}, {\n        updateId: newUpdateId,\n        dataStartIndex: startIndex,\n        dataEndIndex: endIndex\n      }), prevState)), {}, {\n        prevChildren: children,\n        dataStartIndex: startIndex,\n        dataEndIndex: endIndex\n      });\n    }\n    return null;\n  });\n  _defineProperty(CategoricalChartWrapper, \"renderActiveDot\", function (option, props, key) {\n    var dot;\n    if ( /*#__PURE__*/isValidElement(option)) {\n      dot = /*#__PURE__*/cloneElement(option, props);\n    } else if (isFunction(option)) {\n      dot = option(props);\n    } else {\n      dot = /*#__PURE__*/React.createElement(Dot, props);\n    }\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: \"recharts-active-dot\",\n      key: key\n    }, dot);\n  });\n  var CategoricalChart = /*#__PURE__*/forwardRef(function CategoricalChart(props, ref) {\n    return /*#__PURE__*/React.createElement(CategoricalChartWrapper, _extends({}, props, {\n      ref: ref\n    }));\n  });\n  CategoricalChart.displayName = CategoricalChartWrapper.displayName;\n  return CategoricalChart;\n};"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,MAAM,CAAC;EACtBC,UAAU,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;AAChG,SAASC,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,cAAcA,CAACC,GAAG,EAAET,CAAC,EAAE;EAAE,OAAOU,eAAe,CAACD,GAAG,CAAC,IAAIE,qBAAqB,CAACF,GAAG,EAAET,CAAC,CAAC,IAAIY,2BAA2B,CAACH,GAAG,EAAET,CAAC,CAAC,IAAIa,gBAAgB,CAAC,CAAC;AAAE;AAC7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAChM,SAASH,qBAAqBA,CAACI,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAG,IAAI,IAAIF,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOxB,MAAM,IAAIwB,CAAC,CAACxB,MAAM,CAACC,QAAQ,CAAC,IAAIuB,CAAC,CAAC,YAAY,CAAC;EAAE,IAAI,IAAI,IAAIE,CAAC,EAAE;IAAE,IAAIC,CAAC;MAAEC,CAAC;MAAEnB,CAAC;MAAEoB,CAAC;MAAEC,CAAC,GAAG,EAAE;MAAEC,CAAC,GAAG,CAAC,CAAC;MAAEhC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAI;MAAE,IAAIU,CAAC,GAAG,CAACiB,CAAC,GAAGA,CAAC,CAACX,IAAI,CAACS,CAAC,CAAC,EAAEQ,IAAI,EAAE,CAAC,KAAKP,CAAC,EAAE;QAAE,IAAIpB,MAAM,CAACqB,CAAC,CAAC,KAAKA,CAAC,EAAE;QAAQK,CAAC,GAAG,CAAC,CAAC;MAAE,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACJ,CAAC,GAAGlB,CAAC,CAACM,IAAI,CAACW,CAAC,CAAC,EAAEO,IAAI,CAAC,KAAKH,CAAC,CAACI,IAAI,CAACP,CAAC,CAACQ,KAAK,CAAC,EAAEL,CAAC,CAACnB,MAAM,KAAKc,CAAC,CAAC,EAAEM,CAAC,GAAG,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,OAAOP,CAAC,EAAE;MAAEzB,CAAC,GAAG,CAAC,CAAC,EAAE6B,CAAC,GAAGJ,CAAC;IAAE,CAAC,SAAS;MAAE,IAAI;QAAE,IAAI,CAACO,CAAC,IAAI,IAAI,IAAIL,CAAC,CAAC,QAAQ,CAAC,KAAKG,CAAC,GAAGH,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAErB,MAAM,CAACwB,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MAAQ,CAAC,SAAS;QAAE,IAAI9B,CAAC,EAAE,MAAM6B,CAAC;MAAE;IAAE;IAAE,OAAOE,CAAC;EAAE;AAAE;AACzhB,SAASX,eAAeA,CAACD,GAAG,EAAE;EAAE,IAAIkB,KAAK,CAACC,OAAO,CAACnB,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AACpE,SAASoB,wBAAwBA,CAAC1B,MAAM,EAAE2B,QAAQ,EAAE;EAAE,IAAI3B,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAGgC,6BAA6B,CAAC5B,MAAM,EAAE2B,QAAQ,CAAC;EAAE,IAAI1B,GAAG,EAAEJ,CAAC;EAAE,IAAIJ,MAAM,CAACoC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGrC,MAAM,CAACoC,qBAAqB,CAAC7B,MAAM,CAAC;IAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,gBAAgB,CAAC/B,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAEI,GAAG,GAAG6B,gBAAgB,CAACjC,CAAC,CAAC;MAAE,IAAI8B,QAAQ,CAACI,OAAO,CAAC9B,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACR,MAAM,CAACF,SAAS,CAACyC,oBAAoB,CAAC7B,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AAC3e,SAASgC,6BAA6BA,CAAC5B,MAAM,EAAE2B,QAAQ,EAAE;EAAE,IAAI3B,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIK,GAAG,IAAID,MAAM,EAAE;IAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAE,IAAI0B,QAAQ,CAACI,OAAO,CAAC9B,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AACtR,SAASqC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIxB,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASyB,iBAAiBA,CAACxC,MAAM,EAAEyC,KAAK,EAAE;EAAE,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,KAAK,CAACtC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIyC,UAAU,GAAGD,KAAK,CAACxC,CAAC,CAAC;IAAEyC,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEhD,MAAM,CAACiD,cAAc,CAAC9C,MAAM,EAAE+C,cAAc,CAACL,UAAU,CAACrC,GAAG,CAAC,EAAEqC,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASM,YAAYA,CAACT,WAAW,EAAEU,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAET,iBAAiB,CAACD,WAAW,CAAC5C,SAAS,EAAEsD,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEV,iBAAiB,CAACD,WAAW,EAAEW,WAAW,CAAC;EAAErD,MAAM,CAACiD,cAAc,CAACP,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASY,UAAUA,CAACjC,CAAC,EAAE3B,CAAC,EAAE4B,CAAC,EAAE;EAAE,OAAO5B,CAAC,GAAG6D,eAAe,CAAC7D,CAAC,CAAC,EAAE8D,0BAA0B,CAACnC,CAAC,EAAEoC,yBAAyB,CAAC,CAAC,GAAGC,OAAO,CAACC,SAAS,CAACjE,CAAC,EAAE4B,CAAC,IAAI,EAAE,EAAEiC,eAAe,CAAClC,CAAC,CAAC,CAACxB,WAAW,CAAC,GAAGH,CAAC,CAACiB,KAAK,CAACU,CAAC,EAAEC,CAAC,CAAC,CAAC;AAAE;AAC1M,SAASkC,0BAA0BA,CAACI,IAAI,EAAElD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIQ,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAO2C,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI;IAAE,IAAIpC,CAAC,GAAG,CAAC0C,OAAO,CAACjE,SAAS,CAACkE,OAAO,CAACtD,IAAI,CAACgD,OAAO,CAACC,SAAS,CAACI,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,OAAO1C,CAAC,EAAE,CAAC;EAAE,OAAO,CAACoC,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IAAE,OAAO,CAAC,CAACpC,CAAC;EAAE,CAAC,EAAE,CAAC;AAAE;AAClP,SAASkC,eAAeA,CAAC7D,CAAC,EAAE;EAAE6D,eAAe,GAAGvD,MAAM,CAACiE,cAAc,GAAGjE,MAAM,CAACkE,cAAc,CAAChE,IAAI,CAAC,CAAC,GAAG,SAASqD,eAAeA,CAAC7D,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACyE,SAAS,IAAInE,MAAM,CAACkE,cAAc,CAACxE,CAAC,CAAC;EAAE,CAAC;EAAE,OAAO6D,eAAe,CAAC7D,CAAC,CAAC;AAAE;AACnN,SAAS0E,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIpD,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEmD,QAAQ,CAACvE,SAAS,GAAGE,MAAM,CAACuE,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACxE,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEiC,KAAK,EAAEuC,QAAQ;MAAErB,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE/C,MAAM,CAACiD,cAAc,CAACoB,QAAQ,EAAE,WAAW,EAAE;IAAErB,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIsB,UAAU,EAAEE,eAAe,CAACH,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASE,eAAeA,CAAC9E,CAAC,EAAE+E,CAAC,EAAE;EAAED,eAAe,GAAGxE,MAAM,CAACiE,cAAc,GAAGjE,MAAM,CAACiE,cAAc,CAAC/D,IAAI,CAAC,CAAC,GAAG,SAASsE,eAAeA,CAAC9E,CAAC,EAAE+E,CAAC,EAAE;IAAE/E,CAAC,CAACyE,SAAS,GAAGM,CAAC;IAAE,OAAO/E,CAAC;EAAE,CAAC;EAAE,OAAO8E,eAAe,CAAC9E,CAAC,EAAE+E,CAAC,CAAC;AAAE;AACvM,SAASC,kBAAkBA,CAAC7D,GAAG,EAAE;EAAE,OAAO8D,kBAAkB,CAAC9D,GAAG,CAAC,IAAI+D,gBAAgB,CAAC/D,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAIgE,kBAAkB,CAAC,CAAC;AAAE;AACxJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAI3D,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAC7L,SAASF,2BAA2BA,CAACtB,CAAC,EAAEoF,MAAM,EAAE;EAAE,IAAI,CAACpF,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOqF,iBAAiB,CAACrF,CAAC,EAAEoF,MAAM,CAAC;EAAE,IAAIvD,CAAC,GAAGvB,MAAM,CAACF,SAAS,CAACkF,QAAQ,CAACtE,IAAI,CAAChB,CAAC,CAAC,CAACuF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAI1D,CAAC,KAAK,QAAQ,IAAI7B,CAAC,CAACG,WAAW,EAAE0B,CAAC,GAAG7B,CAAC,CAACG,WAAW,CAACqF,IAAI;EAAE,IAAI3D,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOQ,KAAK,CAACoD,IAAI,CAACzF,CAAC,CAAC;EAAE,IAAI6B,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAAC6D,IAAI,CAAC7D,CAAC,CAAC,EAAE,OAAOwD,iBAAiB,CAACrF,CAAC,EAAEoF,MAAM,CAAC;AAAE;AAC/Z,SAASF,gBAAgBA,CAACS,IAAI,EAAE;EAAE,IAAI,OAAO1F,MAAM,KAAK,WAAW,IAAI0F,IAAI,CAAC1F,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIyF,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOtD,KAAK,CAACoD,IAAI,CAACE,IAAI,CAAC;AAAE;AAC7J,SAASV,kBAAkBA,CAAC9D,GAAG,EAAE;EAAE,IAAIkB,KAAK,CAACC,OAAO,CAACnB,GAAG,CAAC,EAAE,OAAOkE,iBAAiB,CAAClE,GAAG,CAAC;AAAE;AAC1F,SAASkE,iBAAiBA,CAAClE,GAAG,EAAEyE,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGzE,GAAG,CAACP,MAAM,EAAEgF,GAAG,GAAGzE,GAAG,CAACP,MAAM;EAAE,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEmF,IAAI,GAAG,IAAIxD,KAAK,CAACuD,GAAG,CAAC,EAAElF,CAAC,GAAGkF,GAAG,EAAElF,CAAC,EAAE,EAAEmF,IAAI,CAACnF,CAAC,CAAC,GAAGS,GAAG,CAACT,CAAC,CAAC;EAAE,OAAOmF,IAAI;AAAE;AAClL,SAASC,OAAOA,CAAClE,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAIE,CAAC,GAAGrB,MAAM,CAACyF,IAAI,CAACnE,CAAC,CAAC;EAAE,IAAItB,MAAM,CAACoC,qBAAqB,EAAE;IAAE,IAAI1C,CAAC,GAAGM,MAAM,CAACoC,qBAAqB,CAACd,CAAC,CAAC;IAAEH,CAAC,KAAKzB,CAAC,GAAGA,CAAC,CAACgG,MAAM,CAAC,UAAUvE,CAAC,EAAE;MAAE,OAAOnB,MAAM,CAAC2F,wBAAwB,CAACrE,CAAC,EAAEH,CAAC,CAAC,CAAC2B,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEzB,CAAC,CAACQ,IAAI,CAAClB,KAAK,CAACU,CAAC,EAAE3B,CAAC,CAAC;EAAE;EAAE,OAAO2B,CAAC;AAAE;AAC9P,SAASuE,aAAaA,CAACtE,CAAC,EAAE;EAAE,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,SAAS,CAACC,MAAM,EAAEa,CAAC,EAAE,EAAE;IAAE,IAAIE,CAAC,GAAG,IAAI,IAAIhB,SAAS,CAACc,CAAC,CAAC,GAAGd,SAAS,CAACc,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGqE,OAAO,CAACxF,MAAM,CAACqB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACwE,OAAO,CAAC,UAAU1E,CAAC,EAAE;MAAE2E,eAAe,CAACxE,CAAC,EAAEH,CAAC,EAAEE,CAAC,CAACF,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGnB,MAAM,CAAC+F,yBAAyB,GAAG/F,MAAM,CAACgG,gBAAgB,CAAC1E,CAAC,EAAEtB,MAAM,CAAC+F,yBAAyB,CAAC1E,CAAC,CAAC,CAAC,GAAGmE,OAAO,CAACxF,MAAM,CAACqB,CAAC,CAAC,CAAC,CAACwE,OAAO,CAAC,UAAU1E,CAAC,EAAE;MAAEnB,MAAM,CAACiD,cAAc,CAAC3B,CAAC,EAAEH,CAAC,EAAEnB,MAAM,CAAC2F,wBAAwB,CAACtE,CAAC,EAAEF,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOG,CAAC;AAAE;AACtb,SAASwE,eAAeA,CAACG,GAAG,EAAEzF,GAAG,EAAEsB,KAAK,EAAE;EAAEtB,GAAG,GAAG0C,cAAc,CAAC1C,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIyF,GAAG,EAAE;IAAEjG,MAAM,CAACiD,cAAc,CAACgD,GAAG,EAAEzF,GAAG,EAAE;MAAEsB,KAAK,EAAEA,KAAK;MAAEgB,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEiD,GAAG,CAACzF,GAAG,CAAC,GAAGsB,KAAK;EAAE;EAAE,OAAOmE,GAAG;AAAE;AAC3O,SAAS/C,cAAcA,CAAC7B,CAAC,EAAE;EAAE,IAAIjB,CAAC,GAAG8F,YAAY,CAAC7E,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI5B,OAAO,CAACW,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAAS8F,YAAYA,CAAC7E,CAAC,EAAEF,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI1B,OAAO,CAAC4B,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIC,CAAC,GAAGD,CAAC,CAAC1B,MAAM,CAACwG,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK7E,CAAC,EAAE;IAAE,IAAIlB,CAAC,GAAGkB,CAAC,CAACZ,IAAI,CAACW,CAAC,EAAEF,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI1B,OAAO,CAACW,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIc,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKC,CAAC,GAAGiF,MAAM,GAAGC,MAAM,EAAEhF,CAAC,CAAC;AAAE;AAC3T,OAAOiF,KAAK,IAAIC,SAAS,EAAEC,YAAY,EAAEC,cAAc,EAAEC,UAAU,QAAQ,OAAO;AAClF,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAOC,MAAM,MAAM,eAAe;AAClC,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,OAAOC,IAAI,MAAM,MAAM;AACvB;;AAEA,OAAOC,SAAS,MAAM,gBAAgB;AACtC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,aAAa,EAAEC,mBAAmB,QAAQ,oBAAoB;AAC3L,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,gBAAgB,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,mBAAmB;AAC7G,SAASC,oBAAoB,EAAEC,wBAAwB,EAAEC,oBAAoB,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,oBAAoB,EAAEC,4BAA4B,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,yBAAyB,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,yBAAyB,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,oBAAoB;AAC3b,SAASC,6BAA6B,QAAQ,uCAAuC;AACrF,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,oBAAoB;AACtE,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AACxD,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,uBAAuB,QAAQ,iCAAiC;AACzE,SAASC,6BAA6B,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,QAAQ,0BAA0B;AACpG,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,0BAA0B,QAAQ,+BAA+B;AAC1E,IAAIC,UAAU,GAAG;EACfC,KAAK,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC;EACxBC,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO;AACzB,CAAC;AACD,IAAIC,qBAAqB,GAAG;EAC1BC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,gBAAgB,GAAG;EACrBC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,OAAO,EAAE;EAC3B,OAAOA,OAAO;AAChB;AACA,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EACvE,IAAIA,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAOD,QAAQ,CAACL,CAAC;EACnB;EACA,IAAIM,MAAM,KAAK,UAAU,EAAE;IACzB,OAAOD,QAAQ,CAACJ,CAAC;EACnB;EACA,IAAIK,MAAM,KAAK,SAAS,EAAE;IACxB,OAAOD,QAAQ,CAACE,KAAK;EACvB;EACA,OAAOF,QAAQ,CAACG,MAAM;AACxB,CAAC;AACD,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACH,MAAM,EAAEI,YAAY,EAAEC,WAAW,EAAEN,QAAQ,EAAE;EAClG,IAAIO,KAAK,GAAGF,YAAY,CAACG,IAAI,CAAC,UAAUC,IAAI,EAAE;IAC5C,OAAOA,IAAI,IAAIA,IAAI,CAACC,KAAK,KAAKJ,WAAW;EAC3C,CAAC,CAAC;EACF,IAAIC,KAAK,EAAE;IACT,IAAIN,MAAM,KAAK,YAAY,EAAE;MAC3B,OAAO;QACLN,CAAC,EAAEY,KAAK,CAACI,UAAU;QACnBf,CAAC,EAAEI,QAAQ,CAACJ;MACd,CAAC;IACH;IACA,IAAIK,MAAM,KAAK,UAAU,EAAE;MACzB,OAAO;QACLN,CAAC,EAAEK,QAAQ,CAACL,CAAC;QACbC,CAAC,EAAEW,KAAK,CAACI;MACX,CAAC;IACH;IACA,IAAIV,MAAM,KAAK,SAAS,EAAE;MACxB,IAAIW,MAAM,GAAGL,KAAK,CAACI,UAAU;MAC7B,IAAIE,OAAO,GAAGb,QAAQ,CAACG,MAAM;MAC7B,OAAO9F,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2F,QAAQ,CAAC,EAAEzB,gBAAgB,CAACyB,QAAQ,CAACc,EAAE,EAAEd,QAAQ,CAACe,EAAE,EAAEF,OAAO,EAAED,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAChIV,KAAK,EAAEU,MAAM;QACbT,MAAM,EAAEU;MACV,CAAC,CAAC;IACJ;IACA,IAAIV,MAAM,GAAGI,KAAK,CAACI,UAAU;IAC7B,IAAIT,KAAK,GAAGF,QAAQ,CAACE,KAAK;IAC1B,OAAO7F,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2F,QAAQ,CAAC,EAAEzB,gBAAgB,CAACyB,QAAQ,CAACc,EAAE,EAAEd,QAAQ,CAACe,EAAE,EAAEZ,MAAM,EAAED,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9HA,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ;EACA,OAAOT,gBAAgB;AACzB,CAAC;AACD,IAAIsB,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC3D,IAAIC,cAAc,GAAGD,IAAI,CAACC,cAAc;IACtCC,cAAc,GAAGF,IAAI,CAACE,cAAc;IACpCC,YAAY,GAAGH,IAAI,CAACG,YAAY;EAClC,IAAIC,SAAS,GAAG,CAACH,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG,EAAE,EAAEI,MAAM,CAAC,UAAUC,MAAM,EAAEC,KAAK,EAAE;IAC3H,IAAIC,QAAQ,GAAGD,KAAK,CAACpK,KAAK,CAAC4J,IAAI;IAC/B,IAAIS,QAAQ,IAAIA,QAAQ,CAAC3M,MAAM,EAAE;MAC/B,OAAO,EAAE,CAAC4M,MAAM,CAACxI,kBAAkB,CAACqI,MAAM,CAAC,EAAErI,kBAAkB,CAACuI,QAAQ,CAAC,CAAC;IAC5E;IACA,OAAOF,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;EACN,IAAIF,SAAS,CAACvM,MAAM,GAAG,CAAC,EAAE;IACxB,OAAOuM,SAAS;EAClB;EACA,IAAIL,IAAI,IAAIA,IAAI,CAAClM,MAAM,IAAIiI,QAAQ,CAACoE,cAAc,CAAC,IAAIpE,QAAQ,CAACqE,YAAY,CAAC,EAAE;IAC7E,OAAOJ,IAAI,CAACvH,KAAK,CAAC0H,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;EACrD;EACA,OAAO,EAAE;AACX,CAAC;AACD,SAASO,0BAA0BA,CAACC,QAAQ,EAAE;EAC5C,OAAOA,QAAQ,KAAK,QAAQ,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,GAAGC,SAAS;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,SAAS,EAAE3B,WAAW,EAAE4B,WAAW,EAAE;EAC7F,IAAIf,cAAc,GAAGa,KAAK,CAACb,cAAc;IACvCgB,WAAW,GAAGH,KAAK,CAACG,WAAW;EACjC,IAAIC,aAAa,GAAGpB,gBAAgB,CAACiB,SAAS,EAAED,KAAK,CAAC;EACtD,IAAI1B,WAAW,GAAG,CAAC,IAAI,CAACa,cAAc,IAAI,CAACA,cAAc,CAACpM,MAAM,IAAIuL,WAAW,IAAI8B,aAAa,CAACrN,MAAM,EAAE;IACvG,OAAO,IAAI;EACb;EACA;EACA,OAAOoM,cAAc,CAACI,MAAM,CAAC,UAAUC,MAAM,EAAEC,KAAK,EAAE;IACpD,IAAIY,iBAAiB;IACrB;AACJ;AACA;AACA;AACA;IACI,IAAIpB,IAAI,GAAG,CAACoB,iBAAiB,GAAGZ,KAAK,CAACpK,KAAK,CAAC4J,IAAI,MAAM,IAAI,IAAIoB,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAGJ,SAAS;IAC1H,IAAIhB,IAAI,IAAIe,KAAK,CAACZ,cAAc,GAAGY,KAAK,CAACX,YAAY,KAAK,CAAC;IAC3D;IACA;IACAW,KAAK,CAACX,YAAY,GAAGW,KAAK,CAACZ,cAAc,IAAId,WAAW,EAAE;MACxDW,IAAI,GAAGA,IAAI,CAACvH,KAAK,CAACsI,KAAK,CAACZ,cAAc,EAAEY,KAAK,CAACX,YAAY,GAAG,CAAC,CAAC;IACjE;IACA,IAAIiB,OAAO;IACX,IAAIH,WAAW,CAACI,OAAO,IAAI,CAACJ,WAAW,CAACK,uBAAuB,EAAE;MAC/D;MACA,IAAIC,OAAO,GAAGxB,IAAI,KAAKa,SAAS,GAAGM,aAAa,GAAGnB,IAAI;MACvDqB,OAAO,GAAGzF,gBAAgB,CAAC4F,OAAO,EAAEN,WAAW,CAACI,OAAO,EAAEL,WAAW,CAAC;IACvE,CAAC,MAAM;MACLI,OAAO,GAAGrB,IAAI,IAAIA,IAAI,CAACX,WAAW,CAAC,IAAI8B,aAAa,CAAC9B,WAAW,CAAC;IACnE;IACA,IAAI,CAACgC,OAAO,EAAE;MACZ,OAAOd,MAAM;IACf;IACA,OAAO,EAAE,CAACG,MAAM,CAACxI,kBAAkB,CAACqI,MAAM,CAAC,EAAE,CAACxD,cAAc,CAACyD,KAAK,EAAEa,OAAO,CAAC,CAAC,CAAC;EAChF,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAII,cAAc,GAAG,SAASA,cAAcA,CAACV,KAAK,EAAEC,SAAS,EAAEhC,MAAM,EAAED,QAAQ,EAAE;EAC/E,IAAI2C,SAAS,GAAG3C,QAAQ,IAAI;IAC1BL,CAAC,EAAEqC,KAAK,CAACY,MAAM;IACfhD,CAAC,EAAEoC,KAAK,CAACa;EACX,CAAC;EACD,IAAIC,GAAG,GAAG/C,mBAAmB,CAAC4C,SAAS,EAAE1C,MAAM,CAAC;EAChD,IAAI8C,KAAK,GAAGf,KAAK,CAACgB,mBAAmB;IACnCC,IAAI,GAAGjB,KAAK,CAACG,WAAW;IACxB9B,YAAY,GAAG2B,KAAK,CAAC3B,YAAY;EACnC,IAAIC,WAAW,GAAGnD,wBAAwB,CAAC2F,GAAG,EAAEC,KAAK,EAAE1C,YAAY,EAAE4C,IAAI,CAAC;EAC1E,IAAI3C,WAAW,IAAI,CAAC,IAAID,YAAY,EAAE;IACpC,IAAI6B,WAAW,GAAG7B,YAAY,CAACC,WAAW,CAAC,IAAID,YAAY,CAACC,WAAW,CAAC,CAAC/J,KAAK;IAC9E,IAAI2M,aAAa,GAAGnB,iBAAiB,CAACC,KAAK,EAAEC,SAAS,EAAE3B,WAAW,EAAE4B,WAAW,CAAC;IACjF,IAAIiB,gBAAgB,GAAG/C,mBAAmB,CAACH,MAAM,EAAE8C,KAAK,EAAEzC,WAAW,EAAEqC,SAAS,CAAC;IACjF,OAAO;MACLS,kBAAkB,EAAE9C,WAAW;MAC/B4B,WAAW,EAAEA,WAAW;MACxBgB,aAAa,EAAEA,aAAa;MAC5BC,gBAAgB,EAAEA;IACpB,CAAC;EACH;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAAChM,KAAK,EAAEiM,KAAK,EAAE;EACpE,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACnBpC,cAAc,GAAGmC,KAAK,CAACnC,cAAc;IACrCU,QAAQ,GAAGyB,KAAK,CAACzB,QAAQ;IACzB2B,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BrC,cAAc,GAAGkC,KAAK,CAAClC,cAAc;IACrCC,YAAY,GAAGiC,KAAK,CAACjC,YAAY;EACnC,IAAIpB,MAAM,GAAG5I,KAAK,CAAC4I,MAAM;IACvByD,QAAQ,GAAGrM,KAAK,CAACqM,QAAQ;IACzBC,WAAW,GAAGtM,KAAK,CAACsM,WAAW;EACjC,IAAIC,aAAa,GAAG3F,iBAAiB,CAACgC,MAAM,EAAE4B,QAAQ,CAAC;;EAEvD;EACA,OAAO0B,IAAI,CAAChC,MAAM,CAAC,UAAUC,MAAM,EAAEC,KAAK,EAAE;IAC1C,IAAIoC,mBAAmB;IACvB,IAAIC,UAAU,GAAGrC,KAAK,CAACsC,IAAI,CAACC,YAAY,KAAKlC,SAAS,GAAGzH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoH,KAAK,CAACsC,IAAI,CAACC,YAAY,CAAC,EAAEvC,KAAK,CAACpK,KAAK,CAAC,GAAGoK,KAAK,CAACpK,KAAK;IAC7I,IAAI0M,IAAI,GAAGD,UAAU,CAACC,IAAI;MACxBxB,OAAO,GAAGuB,UAAU,CAACvB,OAAO;MAC5B0B,iBAAiB,GAAGH,UAAU,CAACG,iBAAiB;MAChDzB,uBAAuB,GAAGsB,UAAU,CAACtB,uBAAuB;MAC5D0B,KAAK,GAAGJ,UAAU,CAACI,KAAK;MACxBnB,KAAK,GAAGe,UAAU,CAACf,KAAK;MACxBoB,aAAa,GAAGL,UAAU,CAACK,aAAa;IAC1C,IAAIC,MAAM,GAAGN,UAAU,CAACN,SAAS,CAAC;IAClC,IAAIhC,MAAM,CAAC4C,MAAM,CAAC,EAAE;MAClB,OAAO5C,MAAM;IACf;IACA,IAAIY,aAAa,GAAGpB,gBAAgB,CAAC3J,KAAK,CAAC4J,IAAI,EAAE;MAC/CE,cAAc,EAAEA,cAAc,CAAChH,MAAM,CAAC,UAAUkK,IAAI,EAAE;QACpD,IAAIC,aAAa;QACjB,IAAIC,UAAU,GAAGf,SAAS,IAAIa,IAAI,CAAChN,KAAK,GAAGgN,IAAI,CAAChN,KAAK,CAACmM,SAAS,CAAC,GAAG,CAACc,aAAa,GAAGD,IAAI,CAACN,IAAI,CAACC,YAAY,MAAM,IAAI,IAAIM,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACd,SAAS,CAAC;QACpL,OAAOe,UAAU,KAAKH,MAAM;MAC9B,CAAC,CAAC;MACFhD,cAAc,EAAEA,cAAc;MAC9BC,YAAY,EAAEA;IAChB,CAAC,CAAC;IACF,IAAItH,GAAG,GAAGqI,aAAa,CAACrN,MAAM;IAC9B,IAAIyP,MAAM,EAAEC,eAAe,EAAEC,iBAAiB;;IAE9C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI7F,uBAAuB,CAACiF,UAAU,CAACU,MAAM,EAAEP,iBAAiB,EAAEF,IAAI,CAAC,EAAE;MACvES,MAAM,GAAGpG,oBAAoB,CAAC0F,UAAU,CAACU,MAAM,EAAE,IAAI,EAAEP,iBAAiB,CAAC;MACzE;AACN;AACA;AACA;MACM,IAAIL,aAAa,KAAKG,IAAI,KAAK,QAAQ,IAAIG,KAAK,KAAK,MAAM,CAAC,EAAE;QAC5DQ,iBAAiB,GAAGlH,oBAAoB,CAAC4E,aAAa,EAAEG,OAAO,EAAE,UAAU,CAAC;MAC9E;IACF;;IAEA;IACA,IAAIoC,aAAa,GAAG/C,0BAA0B,CAACmC,IAAI,CAAC;;IAEpD;IACA,IAAI,CAACS,MAAM,IAAIA,MAAM,CAACzP,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI6P,kBAAkB;MACtB,IAAIC,WAAW,GAAG,CAACD,kBAAkB,GAAGd,UAAU,CAACU,MAAM,MAAM,IAAI,IAAII,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAGD,aAAa;MACzI,IAAIpC,OAAO,EAAE;QACX;QACAiC,MAAM,GAAGhH,oBAAoB,CAAC4E,aAAa,EAAEG,OAAO,EAAEwB,IAAI,CAAC;QAC3D,IAAIA,IAAI,KAAK,UAAU,IAAIH,aAAa,EAAE;UACxC;UACA,IAAIkB,SAAS,GAAG/H,YAAY,CAACyH,MAAM,CAAC;UACpC,IAAIhC,uBAAuB,IAAIsC,SAAS,EAAE;YACxCL,eAAe,GAAGD,MAAM;YACxB;YACAA,MAAM,GAAGlJ,KAAK,CAAC,CAAC,EAAEvB,GAAG,CAAC;UACxB,CAAC,MAAM,IAAI,CAACyI,uBAAuB,EAAE;YACnC;YACAgC,MAAM,GAAGtG,yBAAyB,CAAC2G,WAAW,EAAEL,MAAM,EAAE/C,KAAK,CAAC,CAACF,MAAM,CAAC,UAAUwD,WAAW,EAAExE,KAAK,EAAE;cAClG,OAAOwE,WAAW,CAAChO,OAAO,CAACwJ,KAAK,CAAC,IAAI,CAAC,GAAGwE,WAAW,GAAG,EAAE,CAACpD,MAAM,CAACxI,kBAAkB,CAAC4L,WAAW,CAAC,EAAE,CAACxE,KAAK,CAAC,CAAC;YAC5G,CAAC,EAAE,EAAE,CAAC;UACR;QACF,CAAC,MAAM,IAAIwD,IAAI,KAAK,UAAU,EAAE;UAC9B;UACA,IAAI,CAACvB,uBAAuB,EAAE;YAC5BgC,MAAM,GAAGtG,yBAAyB,CAAC2G,WAAW,EAAEL,MAAM,EAAE/C,KAAK,CAAC,CAACF,MAAM,CAAC,UAAUwD,WAAW,EAAExE,KAAK,EAAE;cAClG,OAAOwE,WAAW,CAAChO,OAAO,CAACwJ,KAAK,CAAC,IAAI,CAAC,IAAIA,KAAK,KAAK,EAAE,IAAInF,KAAK,CAACmF,KAAK,CAAC,GAAGwE,WAAW,GAAG,EAAE,CAACpD,MAAM,CAACxI,kBAAkB,CAAC4L,WAAW,CAAC,EAAE,CAACxE,KAAK,CAAC,CAAC;YAC5I,CAAC,EAAE,EAAE,CAAC;UACR,CAAC,MAAM;YACL;YACAiE,MAAM,GAAGA,MAAM,CAACrK,MAAM,CAAC,UAAUoG,KAAK,EAAE;cACtC,OAAOA,KAAK,KAAK,EAAE,IAAI,CAACnF,KAAK,CAACmF,KAAK,CAAC;YACtC,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IAAIwD,IAAI,KAAK,QAAQ,EAAE;UAC5B;UACA,IAAIiB,eAAe,GAAG7G,oBAAoB,CAACiE,aAAa,EAAEjB,cAAc,CAAChH,MAAM,CAAC,UAAUkK,IAAI,EAAE;YAC9F,IAAIY,cAAc,EAAEC,cAAc;YAClC,IAAIX,UAAU,GAAGf,SAAS,IAAIa,IAAI,CAAChN,KAAK,GAAGgN,IAAI,CAAChN,KAAK,CAACmM,SAAS,CAAC,GAAG,CAACyB,cAAc,GAAGZ,IAAI,CAACN,IAAI,CAACC,YAAY,MAAM,IAAI,IAAIiB,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACzB,SAAS,CAAC;YACvL,IAAI2B,QAAQ,GAAG,MAAM,IAAId,IAAI,CAAChN,KAAK,GAAGgN,IAAI,CAAChN,KAAK,CAAC+N,IAAI,GAAG,CAACF,cAAc,GAAGb,IAAI,CAACN,IAAI,CAACC,YAAY,MAAM,IAAI,IAAIkB,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACE,IAAI;YACtK,OAAOb,UAAU,KAAKH,MAAM,KAAKD,aAAa,IAAI,CAACgB,QAAQ,CAAC;UAC9D,CAAC,CAAC,EAAE5C,OAAO,EAAEV,QAAQ,EAAE5B,MAAM,CAAC;UAC9B,IAAI+E,eAAe,EAAE;YACnBR,MAAM,GAAGQ,eAAe;UAC1B;QACF;QACA,IAAIpB,aAAa,KAAKG,IAAI,KAAK,QAAQ,IAAIG,KAAK,KAAK,MAAM,CAAC,EAAE;UAC5DQ,iBAAiB,GAAGlH,oBAAoB,CAAC4E,aAAa,EAAEG,OAAO,EAAE,UAAU,CAAC;QAC9E;MACF,CAAC,MAAM,IAAIqB,aAAa,EAAE;QACxB;QACAY,MAAM,GAAGlJ,KAAK,CAAC,CAAC,EAAEvB,GAAG,CAAC;MACxB,CAAC,MAAM,IAAI0J,WAAW,IAAIA,WAAW,CAACW,MAAM,CAAC,IAAIX,WAAW,CAACW,MAAM,CAAC,CAACiB,QAAQ,IAAItB,IAAI,KAAK,QAAQ,EAAE;QAClG;QACAS,MAAM,GAAGb,WAAW,KAAK,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGjG,sBAAsB,CAAC+F,WAAW,CAACW,MAAM,CAAC,CAACX,WAAW,EAAErC,cAAc,EAAEC,YAAY,CAAC;MACpI,CAAC,MAAM;QACLmD,MAAM,GAAG/G,4BAA4B,CAAC2E,aAAa,EAAEjB,cAAc,CAAChH,MAAM,CAAC,UAAUkK,IAAI,EAAE;UACzF,IAAIE,UAAU,GAAGf,SAAS,IAAIa,IAAI,CAAChN,KAAK,GAAGgN,IAAI,CAAChN,KAAK,CAACmM,SAAS,CAAC,GAAGa,IAAI,CAACN,IAAI,CAACC,YAAY,CAACR,SAAS,CAAC;UACpG,IAAI2B,QAAQ,GAAG,MAAM,IAAId,IAAI,CAAChN,KAAK,GAAGgN,IAAI,CAAChN,KAAK,CAAC+N,IAAI,GAAGf,IAAI,CAACN,IAAI,CAACC,YAAY,CAACoB,IAAI;UACnF,OAAOb,UAAU,KAAKH,MAAM,KAAKD,aAAa,IAAI,CAACgB,QAAQ,CAAC;QAC9D,CAAC,CAAC,EAAEpB,IAAI,EAAE9D,MAAM,EAAE,IAAI,CAAC;MACzB;MACA,IAAI8D,IAAI,KAAK,QAAQ,EAAE;QACrB;QACAS,MAAM,GAAGnG,6BAA6B,CAACqF,QAAQ,EAAEc,MAAM,EAAEJ,MAAM,EAAEvC,QAAQ,EAAEkB,KAAK,CAAC;QACjF,IAAI8B,WAAW,EAAE;UACfL,MAAM,GAAGpG,oBAAoB,CAACyG,WAAW,EAAEL,MAAM,EAAEP,iBAAiB,CAAC;QACvE;MACF,CAAC,MAAM,IAAIF,IAAI,KAAK,UAAU,IAAIc,WAAW,EAAE;QAC7C,IAAIS,UAAU,GAAGT,WAAW;QAC5B,IAAIU,aAAa,GAAGf,MAAM,CAACgB,KAAK,CAAC,UAAUjF,KAAK,EAAE;UAChD,OAAO+E,UAAU,CAACvO,OAAO,CAACwJ,KAAK,CAAC,IAAI,CAAC;QACvC,CAAC,CAAC;QACF,IAAIgF,aAAa,EAAE;UACjBf,MAAM,GAAGc,UAAU;QACrB;MACF;IACF;IACA,OAAOjL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmH,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEjH,eAAe,CAAC,CAAC,CAAC,EAAE6J,MAAM,EAAE/J,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyJ,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;MAC/HjC,QAAQ,EAAEA,QAAQ;MAClB2C,MAAM,EAAEA,MAAM;MACdE,iBAAiB,EAAEA,iBAAiB;MACpCD,eAAe,EAAEA,eAAe;MAChCgB,cAAc,EAAE,CAAC5B,mBAAmB,GAAGC,UAAU,CAACU,MAAM,MAAM,IAAI,IAAIX,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAGc,aAAa;MAC1If,aAAa,EAAEA,aAAa;MAC5B3D,MAAM,EAAEA;IACV,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIyF,iBAAiB,GAAG,SAASA,iBAAiBA,CAACrO,KAAK,EAAEsO,KAAK,EAAE;EAC/D,IAAIxE,cAAc,GAAGwE,KAAK,CAACxE,cAAc;IACvCyE,IAAI,GAAGD,KAAK,CAACC,IAAI;IACjB/D,QAAQ,GAAG8D,KAAK,CAAC9D,QAAQ;IACzB2B,SAAS,GAAGmC,KAAK,CAACnC,SAAS;IAC3BC,WAAW,GAAGkC,KAAK,CAAClC,WAAW;IAC/BrC,cAAc,GAAGuE,KAAK,CAACvE,cAAc;IACrCC,YAAY,GAAGsE,KAAK,CAACtE,YAAY;EACnC,IAAIpB,MAAM,GAAG5I,KAAK,CAAC4I,MAAM;IACvByD,QAAQ,GAAGrM,KAAK,CAACqM,QAAQ;EAC3B,IAAItB,aAAa,GAAGpB,gBAAgB,CAAC3J,KAAK,CAAC4J,IAAI,EAAE;IAC/CE,cAAc,EAAEA,cAAc;IAC9BC,cAAc,EAAEA,cAAc;IAC9BC,YAAY,EAAEA;EAChB,CAAC,CAAC;EACF,IAAItH,GAAG,GAAGqI,aAAa,CAACrN,MAAM;EAC9B,IAAI6O,aAAa,GAAG3F,iBAAiB,CAACgC,MAAM,EAAE4B,QAAQ,CAAC;EACvD,IAAInB,KAAK,GAAG,CAAC,CAAC;;EAEd;EACA;EACA;EACA;EACA,OAAOS,cAAc,CAACI,MAAM,CAAC,UAAUC,MAAM,EAAEC,KAAK,EAAE;IACpD,IAAIqC,UAAU,GAAGrC,KAAK,CAACsC,IAAI,CAACC,YAAY,KAAKlC,SAAS,GAAGzH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoH,KAAK,CAACsC,IAAI,CAACC,YAAY,CAAC,EAAEvC,KAAK,CAACpK,KAAK,CAAC,GAAGoK,KAAK,CAACpK,KAAK;IAC7I,IAAI+M,MAAM,GAAGN,UAAU,CAACN,SAAS,CAAC;IAClC,IAAIiC,cAAc,GAAG7D,0BAA0B,CAAC,QAAQ,CAAC;IACzD,IAAI,CAACJ,MAAM,CAAC4C,MAAM,CAAC,EAAE;MACnB1D,KAAK,EAAE;MACP,IAAI8D,MAAM;MACV,IAAIZ,aAAa,EAAE;QACjBY,MAAM,GAAGlJ,KAAK,CAAC,CAAC,EAAEvB,GAAG,CAAC;MACxB,CAAC,MAAM,IAAI0J,WAAW,IAAIA,WAAW,CAACW,MAAM,CAAC,IAAIX,WAAW,CAACW,MAAM,CAAC,CAACiB,QAAQ,EAAE;QAC7Eb,MAAM,GAAG9G,sBAAsB,CAAC+F,WAAW,CAACW,MAAM,CAAC,CAACX,WAAW,EAAErC,cAAc,EAAEC,YAAY,CAAC;QAC9FmD,MAAM,GAAGnG,6BAA6B,CAACqF,QAAQ,EAAEc,MAAM,EAAEJ,MAAM,EAAEvC,QAAQ,CAAC;MAC5E,CAAC,MAAM;QACL2C,MAAM,GAAGpG,oBAAoB,CAACqH,cAAc,EAAEhI,4BAA4B,CAAC2E,aAAa,EAAEjB,cAAc,CAAChH,MAAM,CAAC,UAAUkK,IAAI,EAAE;UAC9H,IAAIwB,cAAc,EAAEC,cAAc;UAClC,IAAIvB,UAAU,GAAGf,SAAS,IAAIa,IAAI,CAAChN,KAAK,GAAGgN,IAAI,CAAChN,KAAK,CAACmM,SAAS,CAAC,GAAG,CAACqC,cAAc,GAAGxB,IAAI,CAACN,IAAI,CAACC,YAAY,MAAM,IAAI,IAAI6B,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACrC,SAAS,CAAC;UACvL,IAAI2B,QAAQ,GAAG,MAAM,IAAId,IAAI,CAAChN,KAAK,GAAGgN,IAAI,CAAChN,KAAK,CAAC+N,IAAI,GAAG,CAACU,cAAc,GAAGzB,IAAI,CAACN,IAAI,CAACC,YAAY,MAAM,IAAI,IAAI8B,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACV,IAAI;UACtK,OAAOb,UAAU,KAAKH,MAAM,IAAI,CAACe,QAAQ;QAC3C,CAAC,CAAC,EAAE,QAAQ,EAAElF,MAAM,CAAC,EAAE2F,IAAI,CAAC5B,YAAY,CAACC,iBAAiB,CAAC;QAC3DO,MAAM,GAAGnG,6BAA6B,CAACqF,QAAQ,EAAEc,MAAM,EAAEJ,MAAM,EAAEvC,QAAQ,CAAC;MAC5E;MACA,OAAOxH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmH,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEjH,eAAe,CAAC,CAAC,CAAC,EAAE6J,MAAM,EAAE/J,aAAa,CAACA,aAAa,CAAC;QAC1GwH,QAAQ,EAAEA;MACZ,CAAC,EAAE+D,IAAI,CAAC5B,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;QACzBoB,IAAI,EAAE,IAAI;QACVW,WAAW,EAAExK,GAAG,CAAC6D,UAAU,EAAE,EAAE,CAACuC,MAAM,CAACE,QAAQ,EAAE,GAAG,CAAC,CAACF,MAAM,CAACjB,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;QAC9E8D,MAAM,EAAEA,MAAM;QACdiB,cAAc,EAAEA,cAAc;QAC9B7B,aAAa,EAAEA,aAAa;QAC5B3D,MAAM,EAAEA;QACR;QACA;MACF,CAAC,CAAC,CAAC,CAAC;IACN;IACA,OAAOuB,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIwE,UAAU,GAAG,SAASA,UAAUA,CAAC3O,KAAK,EAAE4O,KAAK,EAAE;EACjD,IAAIC,cAAc,GAAGD,KAAK,CAACpE,QAAQ;IACjCA,QAAQ,GAAGqE,cAAc,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,cAAc;IAC/DC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IACzBhF,cAAc,GAAG8E,KAAK,CAAC9E,cAAc;IACrCsC,WAAW,GAAGwC,KAAK,CAACxC,WAAW;IAC/BrC,cAAc,GAAG6E,KAAK,CAAC7E,cAAc;IACrCC,YAAY,GAAG4E,KAAK,CAAC5E,YAAY;EACnC,IAAIqC,QAAQ,GAAGrM,KAAK,CAACqM,QAAQ;EAC7B,IAAIF,SAAS,GAAG,EAAE,CAAC7B,MAAM,CAACE,QAAQ,EAAE,IAAI,CAAC;EACzC;EACA,IAAI0B,IAAI,GAAGpH,aAAa,CAACuH,QAAQ,EAAEyC,QAAQ,CAAC;EAC5C,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB,IAAI7C,IAAI,IAAIA,IAAI,CAACxO,MAAM,EAAE;IACvBqR,OAAO,GAAG/C,gBAAgB,CAAChM,KAAK,EAAE;MAChCkM,IAAI,EAAEA,IAAI;MACVpC,cAAc,EAAEA,cAAc;MAC9BU,QAAQ,EAAEA,QAAQ;MAClB2B,SAAS,EAAEA,SAAS;MACpBC,WAAW,EAAEA,WAAW;MACxBrC,cAAc,EAAEA,cAAc;MAC9BC,YAAY,EAAEA;IAChB,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIF,cAAc,IAAIA,cAAc,CAACpM,MAAM,EAAE;IAClDqR,OAAO,GAAGV,iBAAiB,CAACrO,KAAK,EAAE;MACjCuO,IAAI,EAAEO,QAAQ;MACdhF,cAAc,EAAEA,cAAc;MAC9BU,QAAQ,EAAEA,QAAQ;MAClB2B,SAAS,EAAEA,SAAS;MACpBC,WAAW,EAAEA,WAAW;MACxBrC,cAAc,EAAEA,cAAc;MAC9BC,YAAY,EAAEA;IAChB,CAAC,CAAC;EACJ;EACA,OAAO+E,OAAO;AAChB,CAAC;AACD,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACD,OAAO,EAAE;EAClE,IAAInD,IAAI,GAAGnG,qBAAqB,CAACsJ,OAAO,CAAC;EACzC,IAAI/F,YAAY,GAAGtC,cAAc,CAACkF,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;EACpD,OAAO;IACL5C,YAAY,EAAEA,YAAY;IAC1B2C,mBAAmB,EAAExH,MAAM,CAAC6E,YAAY,EAAE,UAAUlM,CAAC,EAAE;MACrD,OAAOA,CAAC,CAACwM,UAAU;IACrB,CAAC,CAAC;IACFwB,WAAW,EAAEc,IAAI;IACjBqD,mBAAmB,EAAEjJ,iBAAiB,CAAC4F,IAAI,EAAE5C,YAAY;EAC3D,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIkG,kBAAkB,GAAG,SAASA,kBAAkBA,CAAClP,KAAK,EAAE;EACjE,IAAIqM,QAAQ,GAAGrM,KAAK,CAACqM,QAAQ;IAC3B8C,kBAAkB,GAAGnP,KAAK,CAACmP,kBAAkB;EAC/C,IAAIC,SAAS,GAAGrK,eAAe,CAACsH,QAAQ,EAAE/G,KAAK,CAAC;EAChD,IAAI+J,UAAU,GAAG,CAAC;EAClB,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAItP,KAAK,CAAC4J,IAAI,IAAI5J,KAAK,CAAC4J,IAAI,CAAClM,MAAM,KAAK,CAAC,EAAE;IACzC4R,QAAQ,GAAGtP,KAAK,CAAC4J,IAAI,CAAClM,MAAM,GAAG,CAAC;EAClC;EACA,IAAI0R,SAAS,IAAIA,SAAS,CAACpP,KAAK,EAAE;IAChC,IAAIoP,SAAS,CAACpP,KAAK,CAACqP,UAAU,IAAI,CAAC,EAAE;MACnCA,UAAU,GAAGD,SAAS,CAACpP,KAAK,CAACqP,UAAU;IACzC;IACA,IAAID,SAAS,CAACpP,KAAK,CAACsP,QAAQ,IAAI,CAAC,EAAE;MACjCA,QAAQ,GAAGF,SAAS,CAACpP,KAAK,CAACsP,QAAQ;IACrC;EACF;EACA,OAAO;IACL/D,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTzB,cAAc,EAAEsF,UAAU;IAC1BrF,YAAY,EAAEsF,QAAQ;IACtBvD,kBAAkB,EAAE,CAAC,CAAC;IACtBwD,eAAe,EAAEpO,OAAO,CAACgO,kBAAkB;EAC7C,CAAC;AACH,CAAC;AACD,IAAIK,mBAAmB,GAAG,SAASA,mBAAmBA,CAAC1F,cAAc,EAAE;EACrE,IAAI,CAACA,cAAc,IAAI,CAACA,cAAc,CAACpM,MAAM,EAAE;IAC7C,OAAO,KAAK;EACd;EACA,OAAOoM,cAAc,CAAC2F,IAAI,CAAC,UAAUzC,IAAI,EAAE;IACzC,IAAI1K,IAAI,GAAG0C,cAAc,CAACgI,IAAI,IAAIA,IAAI,CAACN,IAAI,CAAC;IAC5C,OAAOpK,IAAI,IAAIA,IAAI,CAAC5C,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;EACzC,CAAC,CAAC;AACJ,CAAC;AACD,IAAIgQ,mBAAmB,GAAG,SAASA,mBAAmBA,CAAC9G,MAAM,EAAE;EAC7D,IAAIA,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAO;MACL+G,eAAe,EAAE,OAAO;MACxBC,YAAY,EAAE;IAChB,CAAC;EACH;EACA,IAAIhH,MAAM,KAAK,UAAU,EAAE;IACzB,OAAO;MACL+G,eAAe,EAAE,OAAO;MACxBC,YAAY,EAAE;IAChB,CAAC;EACH;EACA,IAAIhH,MAAM,KAAK,SAAS,EAAE;IACxB,OAAO;MACL+G,eAAe,EAAE,YAAY;MAC7BC,YAAY,EAAE;IAChB,CAAC;EACH;EACA,OAAO;IACLD,eAAe,EAAE,WAAW;IAC5BC,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,cAAc,EAAE;EACpE,IAAI/P,KAAK,GAAG8P,KAAK,CAAC9P,KAAK;IACrB8J,cAAc,GAAGgG,KAAK,CAAChG,cAAc;IACrCkG,cAAc,GAAGF,KAAK,CAACG,QAAQ;IAC/BA,QAAQ,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,cAAc;IAC1DE,cAAc,GAAGJ,KAAK,CAACK,QAAQ;IAC/BA,QAAQ,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,cAAc;EAC5D,IAAI/H,KAAK,GAAGnI,KAAK,CAACmI,KAAK;IACrBC,MAAM,GAAGpI,KAAK,CAACoI,MAAM;IACrBiE,QAAQ,GAAGrM,KAAK,CAACqM,QAAQ;EAC3B,IAAI+D,MAAM,GAAGpQ,KAAK,CAACoQ,MAAM,IAAI,CAAC,CAAC;EAC/B,IAAIhB,SAAS,GAAGrK,eAAe,CAACsH,QAAQ,EAAE/G,KAAK,CAAC;EAChD,IAAI+K,UAAU,GAAGtL,eAAe,CAACsH,QAAQ,EAAE3H,MAAM,CAAC;EAClD,IAAI4L,OAAO,GAAGlT,MAAM,CAACyF,IAAI,CAACsN,QAAQ,CAAC,CAACjG,MAAM,CAAC,UAAUC,MAAM,EAAEoG,EAAE,EAAE;IAC/D,IAAIrH,KAAK,GAAGiH,QAAQ,CAACI,EAAE,CAAC;IACxB,IAAI7B,WAAW,GAAGxF,KAAK,CAACwF,WAAW;IACnC,IAAI,CAACxF,KAAK,CAACsH,MAAM,IAAI,CAACtH,KAAK,CAAC6E,IAAI,EAAE;MAChC,OAAO/K,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmH,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEjH,eAAe,CAAC,CAAC,CAAC,EAAEwL,WAAW,EAAEvE,MAAM,CAACuE,WAAW,CAAC,GAAGxF,KAAK,CAACf,KAAK,CAAC,CAAC;IAC1H;IACA,OAAOgC,MAAM;EACf,CAAC,EAAE;IACDsG,IAAI,EAAEL,MAAM,CAACK,IAAI,IAAI,CAAC;IACtBC,KAAK,EAAEN,MAAM,CAACM,KAAK,IAAI;EACzB,CAAC,CAAC;EACF,IAAIC,OAAO,GAAGvT,MAAM,CAACyF,IAAI,CAACoN,QAAQ,CAAC,CAAC/F,MAAM,CAAC,UAAUC,MAAM,EAAEoG,EAAE,EAAE;IAC/D,IAAIrH,KAAK,GAAG+G,QAAQ,CAACM,EAAE,CAAC;IACxB,IAAI7B,WAAW,GAAGxF,KAAK,CAACwF,WAAW;IACnC,IAAI,CAACxF,KAAK,CAACsH,MAAM,IAAI,CAACtH,KAAK,CAAC6E,IAAI,EAAE;MAChC,OAAO/K,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmH,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEjH,eAAe,CAAC,CAAC,CAAC,EAAEwL,WAAW,EAAExK,GAAG,CAACiG,MAAM,EAAE,EAAE,CAACG,MAAM,CAACoE,WAAW,CAAC,CAAC,GAAGxF,KAAK,CAACd,MAAM,CAAC,CAAC;IAC3I;IACA,OAAO+B,MAAM;EACf,CAAC,EAAE;IACDyG,GAAG,EAAER,MAAM,CAACQ,GAAG,IAAI,CAAC;IACpBC,MAAM,EAAET,MAAM,CAACS,MAAM,IAAI;EAC3B,CAAC,CAAC;EACF,IAAIC,MAAM,GAAG9N,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2N,OAAO,CAAC,EAAEL,OAAO,CAAC;EAC/D,IAAIS,WAAW,GAAGD,MAAM,CAACD,MAAM;EAC/B,IAAIzB,SAAS,EAAE;IACb0B,MAAM,CAACD,MAAM,IAAIzB,SAAS,CAACpP,KAAK,CAACoI,MAAM,IAAI9C,KAAK,CAACqH,YAAY,CAACvE,MAAM;EACtE;EACA,IAAIiI,UAAU,IAAIN,cAAc,EAAE;IAChC;IACAe,MAAM,GAAGjL,oBAAoB,CAACiL,MAAM,EAAEhH,cAAc,EAAE9J,KAAK,EAAE+P,cAAc,CAAC;EAC9E;EACA,IAAIiB,WAAW,GAAG7I,KAAK,GAAG2I,MAAM,CAACL,IAAI,GAAGK,MAAM,CAACJ,KAAK;EACpD,IAAIO,YAAY,GAAG7I,MAAM,GAAG0I,MAAM,CAACF,GAAG,GAAGE,MAAM,CAACD,MAAM;EACtD,OAAO7N,aAAa,CAACA,aAAa,CAAC;IACjC+N,WAAW,EAAEA;EACf,CAAC,EAAED,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;IACd;IACA3I,KAAK,EAAE+I,IAAI,CAACC,GAAG,CAACH,WAAW,EAAE,CAAC,CAAC;IAC/B5I,MAAM,EAAE8I,IAAI,CAACC,GAAG,CAACF,YAAY,EAAE,CAAC;EAClC,CAAC,CAAC;AACJ,CAAC;AACD;AACA,IAAIG,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EAC1E,IAAIA,QAAQ,KAAK,OAAO,EAAE;IACxB,OAAOD,OAAO,CAACC,QAAQ,CAAC,CAACnJ,KAAK;EAChC;EACA,IAAImJ,QAAQ,KAAK,OAAO,EAAE;IACxB,OAAOD,OAAO,CAACC,QAAQ,CAAC,CAAClJ,MAAM;EACjC;EACA;EACA,OAAOqC,SAAS;AAClB,CAAC;AACD,OAAO,IAAI8G,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,KAAK,EAAE;EAC7E,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,cAAc,GAAGF,KAAK,CAACE,cAAc;IACrCC,qBAAqB,GAAGH,KAAK,CAACI,uBAAuB;IACrDA,uBAAuB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,qBAAqB;IAC3FE,qBAAqB,GAAGL,KAAK,CAACM,yBAAyB;IACvDA,yBAAyB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAGA,qBAAqB;IAC/FE,cAAc,GAAGP,KAAK,CAACO,cAAc;IACrCC,aAAa,GAAGR,KAAK,CAACQ,aAAa;IACnCC,aAAa,GAAGT,KAAK,CAACS,aAAa;IACnCtF,YAAY,GAAG6E,KAAK,CAAC7E,YAAY;EACnC,IAAIuF,cAAc,GAAG,SAASA,cAAcA,CAAClS,KAAK,EAAEmS,YAAY,EAAE;IAChE,IAAIrI,cAAc,GAAGqI,YAAY,CAACrI,cAAc;MAC9CsC,WAAW,GAAG+F,YAAY,CAAC/F,WAAW;MACtC0E,MAAM,GAAGqB,YAAY,CAACrB,MAAM;MAC5BsB,QAAQ,GAAGD,YAAY,CAACC,QAAQ;MAChCrI,cAAc,GAAGoI,YAAY,CAACpI,cAAc;MAC5CC,YAAY,GAAGmI,YAAY,CAACnI,YAAY;IAC1C,IAAIqI,OAAO,GAAGrS,KAAK,CAACqS,OAAO;MACzBzJ,MAAM,GAAG5I,KAAK,CAAC4I,MAAM;MACrB0J,MAAM,GAAGtS,KAAK,CAACsS,MAAM;MACrBC,cAAc,GAAGvS,KAAK,CAACuS,cAAc;MACrCC,gBAAgB,GAAGxS,KAAK,CAACyS,UAAU;IACrC,IAAIC,oBAAoB,GAAGhD,mBAAmB,CAAC9G,MAAM,CAAC;MACpD+G,eAAe,GAAG+C,oBAAoB,CAAC/C,eAAe;MACtDC,YAAY,GAAG8C,oBAAoB,CAAC9C,YAAY;IAClD,IAAI+C,MAAM,GAAGnD,mBAAmB,CAAC1F,cAAc,CAAC;IAChD,IAAI8I,cAAc,GAAG,EAAE;IACvB9I,cAAc,CAAC7G,OAAO,CAAC,UAAU+J,IAAI,EAAE3D,KAAK,EAAE;MAC5C,IAAI0B,aAAa,GAAGpB,gBAAgB,CAAC3J,KAAK,CAAC4J,IAAI,EAAE;QAC/CE,cAAc,EAAE,CAACkD,IAAI,CAAC;QACtBjD,cAAc,EAAEA,cAAc;QAC9BC,YAAY,EAAEA;MAChB,CAAC,CAAC;MACF,IAAI6I,SAAS,GAAG7F,IAAI,CAACN,IAAI,CAACC,YAAY,KAAKlC,SAAS,GAAGzH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgK,IAAI,CAACN,IAAI,CAACC,YAAY,CAAC,EAAEK,IAAI,CAAChN,KAAK,CAAC,GAAGgN,IAAI,CAAChN,KAAK;MACxI,IAAIkL,OAAO,GAAG2H,SAAS,CAAC3H,OAAO;QAC7B4H,eAAe,GAAGD,SAAS,CAACJ,UAAU;MACxC;MACA,IAAIM,aAAa,GAAGF,SAAS,CAAC,EAAE,CAACvI,MAAM,CAACqF,eAAe,EAAE,IAAI,CAAC,CAAC;MAC/D;MACA,IAAIqD,UAAU,GAAGH,SAAS,CAAC,EAAE,CAACvI,MAAM,CAACsF,YAAY,EAAE,IAAI,CAAC,CAAC;MACzD,IAAIqD,mBAAmB,GAAG,CAAC,CAAC;MAC5B,IAAI5B,OAAO,GAAGU,cAAc,CAAC7H,MAAM,CAAC,UAAUC,MAAM,EAAEjB,KAAK,EAAE;QAC3D,IAAIgK,qBAAqB,EAAEC,UAAU;QACrC;QACA,IAAIpE,OAAO,GAAGoD,YAAY,CAAC,EAAE,CAAC7H,MAAM,CAACpB,KAAK,CAACsB,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC5D;QACA,IAAI+F,EAAE,GAAGsC,SAAS,CAAC,EAAE,CAACvI,MAAM,CAACpB,KAAK,CAACsB,QAAQ,EAAE,IAAI,CAAC,CAAC;;QAEnD;AACR;AACA;AACA;AACA;QACQ,EAAEuE,OAAO,IAAIA,OAAO,CAACwB,EAAE,CAAC,IAAIrH,KAAK,CAACsB,QAAQ,KAAK,OAAO,CAAC,GAAG4I,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhP,SAAS,CAAC,KAAK,EAAE,kBAAkB,CAACgG,MAAM,CAACpB,KAAK,CAACsB,QAAQ,EAAE,8BAA8B,CAAC,CAACF,MAAM,CAACpB,KAAK,CAACsB;QAC1M;QAAA,EACE,yCAAyC,CAAC,CAACF,MAAM,CAAC,CAAC4I,qBAAqB,GAAGlG,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,IAAI,CAACmG,UAAU,GAAGnG,IAAI,CAACN,IAAI,MAAM,IAAI,IAAIyG,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACI,WAAW,MAAM,IAAI,IAAIL,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,EAAE,CAAC,CAAC,GAAG5O,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;;QAEtT;QACA,IAAIsH,IAAI,GAAGmD,OAAO,CAACwB,EAAE,CAAC;QACtB,OAAOvN,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmH,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEjH,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEgG,KAAK,CAACsB,QAAQ,EAAEoB,IAAI,CAAC,EAAE,EAAE,CAACtB,MAAM,CAACpB,KAAK,CAACsB,QAAQ,EAAE,OAAO,CAAC,EAAE9D,cAAc,CAACkF,IAAI,CAAC,CAAC,CAAC;MAC3K,CAAC,EAAEqH,mBAAmB,CAAC;MACvB,IAAIO,QAAQ,GAAGnC,OAAO,CAACzB,YAAY,CAAC;MACpC,IAAI6D,SAAS,GAAGpC,OAAO,CAAC,EAAE,CAAC/G,MAAM,CAACsF,YAAY,EAAE,OAAO,CAAC,CAAC;MACzD,IAAI8D,WAAW,GAAGtH,WAAW,IAAIA,WAAW,CAAC2G,aAAa,CAAC,IAAI3G,WAAW,CAAC2G,aAAa,CAAC,CAAC/E,QAAQ,IAAIxH,oBAAoB,CAACwG,IAAI,EAAEZ,WAAW,CAAC2G,aAAa,CAAC,CAAC3G,WAAW,CAAC;MACxK,IAAIuH,SAAS,GAAG3O,cAAc,CAACgI,IAAI,CAACN,IAAI,CAAC,CAAChN,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;MAC7D,IAAIkU,QAAQ,GAAG5N,iBAAiB,CAACwN,QAAQ,EAAEC,SAAS,CAAC;MACrD,IAAII,WAAW,GAAG,EAAE;MACpB,IAAIC,QAAQ,GAAGnB,MAAM,IAAIzM,cAAc,CAAC;QACtCmM,OAAO,EAAEA,OAAO;QAChBjG,WAAW,EAAEA,WAAW;QACxB2H,SAAS,EAAE3C,oBAAoB,CAACC,OAAO,EAAEzB,YAAY;MACvD,CAAC,CAAC;MACF,IAAI+D,SAAS,EAAE;QACb,IAAIK,KAAK,EAAEC,kBAAkB;QAC7B;QACA,IAAIxB,UAAU,GAAG1O,KAAK,CAAC+O,eAAe,CAAC,GAAGN,gBAAgB,GAAGM,eAAe;QAC5E,IAAIoB,WAAW,GAAG,CAACF,KAAK,GAAG,CAACC,kBAAkB,GAAGjO,iBAAiB,CAACwN,QAAQ,EAAEC,SAAS,EAAE,IAAI,CAAC,MAAM,IAAI,IAAIQ,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAGxB,UAAU,MAAM,IAAI,IAAIuB,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC;QACpNH,WAAW,GAAG5N,cAAc,CAAC;UAC3BqM,MAAM,EAAEA,MAAM;UACdC,cAAc,EAAEA,cAAc;UAC9BqB,QAAQ,EAAEM,WAAW,KAAKN,QAAQ,GAAGM,WAAW,GAAGN,QAAQ;UAC3DE,QAAQ,EAAEA,QAAQ,CAACd,UAAU,CAAC;UAC9BP,UAAU,EAAEA;QACd,CAAC,CAAC;QACF,IAAIyB,WAAW,KAAKN,QAAQ,EAAE;UAC5BC,WAAW,GAAGA,WAAW,CAACM,GAAG,CAAC,UAAU1I,GAAG,EAAE;YAC3C,OAAOzI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyI,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;cAC/C2I,QAAQ,EAAEpR,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyI,GAAG,CAAC2I,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC3DtD,MAAM,EAAErF,GAAG,CAAC2I,QAAQ,CAACtD,MAAM,GAAGoD,WAAW,GAAG;cAC9C,CAAC;YACH,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF;MACA;MACA,IAAIG,UAAU,GAAGrH,IAAI,IAAIA,IAAI,CAACN,IAAI,IAAIM,IAAI,CAACN,IAAI,CAAC4H,eAAe;MAC/D,IAAID,UAAU,EAAE;QACdzB,cAAc,CAAC3T,IAAI,CAAC;UAClBe,KAAK,EAAEgD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqR,UAAU,CAACrR,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqO,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9FtG,aAAa,EAAEA,aAAa;YAC5B/K,KAAK,EAAEA,KAAK;YACZkL,OAAO,EAAEA,OAAO;YAChB8B,IAAI,EAAEA,IAAI;YACV4G,QAAQ,EAAEA,QAAQ;YAClBC,WAAW,EAAEA,WAAW;YACxB/C,MAAM,EAAEA,MAAM;YACd4C,WAAW,EAAEA,WAAW;YACxB9K,MAAM,EAAEA,MAAM;YACdmB,cAAc,EAAEA,cAAc;YAC9BC,YAAY,EAAEA;UAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE9G,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC;YACxDtF,GAAG,EAAEoP,IAAI,CAACpP,GAAG,IAAI,OAAO,CAAC0M,MAAM,CAACjB,KAAK;UACvC,CAAC,EAAEsG,eAAe,EAAE0B,OAAO,CAAC1B,eAAe,CAAC,CAAC,EAAEC,YAAY,EAAEyB,OAAO,CAACzB,YAAY,CAAC,CAAC,EAAE,aAAa,EAAEwC,QAAQ,CAAC,CAAC;UAC9GmC,UAAU,EAAEpP,eAAe,CAAC6H,IAAI,EAAEhN,KAAK,CAACqM,QAAQ,CAAC;UACjDW,IAAI,EAAEA;QACR,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,OAAO4F,cAAc;EACvB,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAI4B,yCAAyC,GAAG,SAASA,yCAAyCA,CAACC,KAAK,EAAEC,SAAS,EAAE;IACnH,IAAI1U,KAAK,GAAGyU,KAAK,CAACzU,KAAK;MACrB+J,cAAc,GAAG0K,KAAK,CAAC1K,cAAc;MACrCC,YAAY,GAAGyK,KAAK,CAACzK,YAAY;MACjCoI,QAAQ,GAAGqC,KAAK,CAACrC,QAAQ;IAC3B,IAAI,CAAC/M,mBAAmB,CAAC;MACvBrF,KAAK,EAAEA;IACT,CAAC,CAAC,EAAE;MACF,OAAO,IAAI;IACb;IACA,IAAIqM,QAAQ,GAAGrM,KAAK,CAACqM,QAAQ;MAC3BzD,MAAM,GAAG5I,KAAK,CAAC4I,MAAM;MACrB0D,WAAW,GAAGtM,KAAK,CAACsM,WAAW;MAC/B1C,IAAI,GAAG5J,KAAK,CAAC4J,IAAI;MACjB+K,iBAAiB,GAAG3U,KAAK,CAAC2U,iBAAiB;IAC7C,IAAIC,qBAAqB,GAAGlF,mBAAmB,CAAC9G,MAAM,CAAC;MACrD+G,eAAe,GAAGiF,qBAAqB,CAACjF,eAAe;MACvDC,YAAY,GAAGgF,qBAAqB,CAAChF,YAAY;IACnD,IAAI9F,cAAc,GAAGhF,aAAa,CAACuH,QAAQ,EAAEqF,cAAc,CAAC;IAC5D,IAAItF,WAAW,GAAG3F,sBAAsB,CAACmD,IAAI,EAAEE,cAAc,EAAE,EAAE,CAACQ,MAAM,CAACqF,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAACrF,MAAM,CAACsF,YAAY,EAAE,IAAI,CAAC,EAAEtD,WAAW,EAAEqI,iBAAiB,CAAC;IAC/J,IAAItD,OAAO,GAAGU,cAAc,CAAC7H,MAAM,CAAC,UAAUC,MAAM,EAAEjB,KAAK,EAAE;MAC3D,IAAI5G,IAAI,GAAG,EAAE,CAACgI,MAAM,CAACpB,KAAK,CAACsB,QAAQ,EAAE,KAAK,CAAC;MAC3C,OAAOxH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmH,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEjH,eAAe,CAAC,CAAC,CAAC,EAAEZ,IAAI,EAAEqM,UAAU,CAAC3O,KAAK,EAAEgD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1IY,cAAc,EAAEA,cAAc;QAC9BsC,WAAW,EAAElD,KAAK,CAACsB,QAAQ,KAAKmF,eAAe,IAAIvD,WAAW;QAC9DrC,cAAc,EAAEA,cAAc;QAC9BC,YAAY,EAAEA;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,IAAI8G,MAAM,GAAGjB,eAAe,CAAC7M,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqO,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;MACzErR,KAAK,EAAEA,KAAK;MACZ8J,cAAc,EAAEA;IAClB,CAAC,CAAC,EAAE4K,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACG,UAAU,CAAC;IAC/EzX,MAAM,CAACyF,IAAI,CAACwO,OAAO,CAAC,CAACpO,OAAO,CAAC,UAAUrF,GAAG,EAAE;MAC1CyT,OAAO,CAACzT,GAAG,CAAC,GAAGqU,aAAa,CAACjS,KAAK,EAAEqR,OAAO,CAACzT,GAAG,CAAC,EAAEkT,MAAM,EAAElT,GAAG,CAACkX,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAErD,SAAS,CAAC;IAC9F,CAAC,CAAC;IACF,IAAIsD,WAAW,GAAG1D,OAAO,CAAC,EAAE,CAAC/G,MAAM,CAACsF,YAAY,EAAE,KAAK,CAAC,CAAC;IACzD,IAAIoF,QAAQ,GAAGhG,qBAAqB,CAAC+F,WAAW,CAAC;IACjD,IAAIE,uBAAuB,GAAG/C,cAAc,CAAClS,KAAK,EAAEgD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqO,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;MAChGtH,cAAc,EAAEA,cAAc;MAC9BC,YAAY,EAAEA,YAAY;MAC1BoI,QAAQ,EAAEA,QAAQ;MAClBtI,cAAc,EAAEA,cAAc;MAC9BsC,WAAW,EAAEA,WAAW;MACxB0E,MAAM,EAAEA;IACV,CAAC,CAAC,CAAC;IACH,OAAO9N,aAAa,CAACA,aAAa,CAAC;MACjCiS,uBAAuB,EAAEA,uBAAuB;MAChDnL,cAAc,EAAEA,cAAc;MAC9BgH,MAAM,EAAEA,MAAM;MACd1E,WAAW,EAAEA;IACf,CAAC,EAAE4I,QAAQ,CAAC,EAAE3D,OAAO,CAAC;EACxB,CAAC;EACD,IAAI6D,uBAAuB,GAAG,aAAa,UAAUC,UAAU,EAAE;IAC/D,SAASD,uBAAuBA,CAACE,MAAM,EAAE;MACvC,IAAIC,SAAS,EAAEC,oBAAoB;MACnC,IAAIC,KAAK;MACT3V,eAAe,CAAC,IAAI,EAAEsV,uBAAuB,CAAC;MAC9CK,KAAK,GAAG7U,UAAU,CAAC,IAAI,EAAEwU,uBAAuB,EAAE,CAACE,MAAM,CAAC,CAAC;MAC3DlS,eAAe,CAACqS,KAAK,EAAE,oBAAoB,EAAExY,MAAM,CAAC,sBAAsB,CAAC,CAAC;MAC5EmG,eAAe,CAACqS,KAAK,EAAE,sBAAsB,EAAE,IAAIhO,oBAAoB,CAAC,CAAC,CAAC;MAC1ErE,eAAe,CAACqS,KAAK,EAAE,wBAAwB,EAAE,UAAUC,GAAG,EAAE;QAC9D,IAAIA,GAAG,EAAE;UACP,IAAIC,WAAW,GAAGF,KAAK,CAAC5K,KAAK;YAC3BZ,cAAc,GAAG0L,WAAW,CAAC1L,cAAc;YAC3CC,YAAY,GAAGyL,WAAW,CAACzL,YAAY;YACvCoI,QAAQ,GAAGqD,WAAW,CAACrD,QAAQ;UACjCmD,KAAK,CAACG,QAAQ,CAAC1S,aAAa,CAAC;YAC3B6R,UAAU,EAAEW;UACd,CAAC,EAAEhB,yCAAyC,CAAC;YAC3CxU,KAAK,EAAEuV,KAAK,CAACvV,KAAK;YAClB+J,cAAc,EAAEA,cAAc;YAC9BC,YAAY,EAAEA,YAAY;YAC1BoI,QAAQ,EAAEA;UACZ,CAAC,EAAEpP,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuS,KAAK,CAAC5K,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACnDkK,UAAU,EAAEW;UACd,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;MACF,CAAC,CAAC;MACFtS,eAAe,CAACqS,KAAK,EAAE,wBAAwB,EAAE,UAAUI,GAAG,EAAE/L,IAAI,EAAEgM,OAAO,EAAE;QAC7E,IAAIL,KAAK,CAACvV,KAAK,CAAC6V,MAAM,KAAKF,GAAG,EAAE;UAC9B,IAAIC,OAAO,KAAKL,KAAK,CAACO,kBAAkB,IAAI,OAAOP,KAAK,CAACvV,KAAK,CAAC+V,UAAU,KAAK,UAAU,EAAE;YACxF;UACF;UACAR,KAAK,CAACS,cAAc,CAACpM,IAAI,CAAC;QAC5B;MACF,CAAC,CAAC;MACF1G,eAAe,CAACqS,KAAK,EAAE,mBAAmB,EAAE,UAAUU,KAAK,EAAE;QAC3D,IAAI5G,UAAU,GAAG4G,KAAK,CAAC5G,UAAU;UAC/BC,QAAQ,GAAG2G,KAAK,CAAC3G,QAAQ;QAC3B;QACA,IAAID,UAAU,KAAKkG,KAAK,CAAC5K,KAAK,CAACZ,cAAc,IAAIuF,QAAQ,KAAKiG,KAAK,CAAC5K,KAAK,CAACX,YAAY,EAAE;UACtF,IAAIoI,QAAQ,GAAGmD,KAAK,CAAC5K,KAAK,CAACyH,QAAQ;UACnCmD,KAAK,CAACG,QAAQ,CAAC,YAAY;YACzB,OAAO1S,aAAa,CAAC;cACnB+G,cAAc,EAAEsF,UAAU;cAC1BrF,YAAY,EAAEsF;YAChB,CAAC,EAAEkF,yCAAyC,CAAC;cAC3CxU,KAAK,EAAEuV,KAAK,CAACvV,KAAK;cAClB+J,cAAc,EAAEsF,UAAU;cAC1BrF,YAAY,EAAEsF,QAAQ;cACtB8C,QAAQ,EAAEA;YACZ,CAAC,EAAEmD,KAAK,CAAC5K,KAAK,CAAC,CAAC;UAClB,CAAC,CAAC;UACF4K,KAAK,CAACW,gBAAgB,CAAC;YACrBnM,cAAc,EAAEsF,UAAU;YAC1BrF,YAAY,EAAEsF;UAChB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MACF;AACN;AACA;AACA;AACA;MACMpM,eAAe,CAACqS,KAAK,EAAE,kBAAkB,EAAE,UAAU7W,CAAC,EAAE;QACtD,IAAIyX,KAAK,GAAGZ,KAAK,CAACa,YAAY,CAAC1X,CAAC,CAAC;QACjC,IAAIyX,KAAK,EAAE;UACT,IAAIE,UAAU,GAAGrT,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmT,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YAC3D5G,eAAe,EAAE;UACnB,CAAC,CAAC;UACFgG,KAAK,CAACG,QAAQ,CAACW,UAAU,CAAC;UAC1Bd,KAAK,CAACW,gBAAgB,CAACG,UAAU,CAAC;UAClC,IAAIC,YAAY,GAAGf,KAAK,CAACvV,KAAK,CAACsW,YAAY;UAC3C,IAAItS,UAAU,CAACsS,YAAY,CAAC,EAAE;YAC5BA,YAAY,CAACD,UAAU,EAAE3X,CAAC,CAAC;UAC7B;QACF;MACF,CAAC,CAAC;MACFwE,eAAe,CAACqS,KAAK,EAAE,yBAAyB,EAAE,UAAU7W,CAAC,EAAE;QAC7D,IAAIyX,KAAK,GAAGZ,KAAK,CAACa,YAAY,CAAC1X,CAAC,CAAC;QACjC,IAAI6X,SAAS,GAAGJ,KAAK,GAAGnT,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmT,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAClE5G,eAAe,EAAE;QACnB,CAAC,CAAC,GAAG;UACHA,eAAe,EAAE;QACnB,CAAC;QACDgG,KAAK,CAACG,QAAQ,CAACa,SAAS,CAAC;QACzBhB,KAAK,CAACW,gBAAgB,CAACK,SAAS,CAAC;QACjC,IAAIC,WAAW,GAAGjB,KAAK,CAACvV,KAAK,CAACwW,WAAW;QACzC,IAAIxS,UAAU,CAACwS,WAAW,CAAC,EAAE;UAC3BA,WAAW,CAACD,SAAS,EAAE7X,CAAC,CAAC;QAC3B;MACF,CAAC,CAAC;MACF;AACN;AACA;AACA;AACA;MACMwE,eAAe,CAACqS,KAAK,EAAE,sBAAsB,EAAE,UAAUkB,EAAE,EAAE;QAC3DlB,KAAK,CAACG,QAAQ,CAAC,YAAY;UACzB,OAAO;YACLnG,eAAe,EAAE,IAAI;YACrBmH,UAAU,EAAED,EAAE;YACd5K,aAAa,EAAE4K,EAAE,CAACE,cAAc;YAChC7K,gBAAgB,EAAE2K,EAAE,CAACG,eAAe,IAAI;cACtCtO,CAAC,EAAEmO,EAAE,CAAChN,EAAE;cACRlB,CAAC,EAAEkO,EAAE,CAAC/M;YACR;UACF,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;MACF;AACN;AACA;AACA;MACMxG,eAAe,CAACqS,KAAK,EAAE,sBAAsB,EAAE,YAAY;QACzDA,KAAK,CAACG,QAAQ,CAAC,YAAY;UACzB,OAAO;YACLnG,eAAe,EAAE;UACnB,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;MACF;AACN;AACA;AACA;AACA;MACMrM,eAAe,CAACqS,KAAK,EAAE,iBAAiB,EAAE,UAAU7W,CAAC,EAAE;QACrDA,CAAC,CAACmY,OAAO,CAAC,CAAC;QACXtB,KAAK,CAACuB,+BAA+B,CAACpY,CAAC,CAAC;MAC1C,CAAC,CAAC;MACF;AACN;AACA;AACA;AACA;MACMwE,eAAe,CAACqS,KAAK,EAAE,kBAAkB,EAAE,UAAU7W,CAAC,EAAE;QACtD6W,KAAK,CAACuB,+BAA+B,CAACC,MAAM,CAAC,CAAC;QAC9C,IAAIR,SAAS,GAAG;UACdhH,eAAe,EAAE;QACnB,CAAC;QACDgG,KAAK,CAACG,QAAQ,CAACa,SAAS,CAAC;QACzBhB,KAAK,CAACW,gBAAgB,CAACK,SAAS,CAAC;QACjC,IAAIS,YAAY,GAAGzB,KAAK,CAACvV,KAAK,CAACgX,YAAY;QAC3C,IAAIhT,UAAU,CAACgT,YAAY,CAAC,EAAE;UAC5BA,YAAY,CAACT,SAAS,EAAE7X,CAAC,CAAC;QAC5B;MACF,CAAC,CAAC;MACFwE,eAAe,CAACqS,KAAK,EAAE,kBAAkB,EAAE,UAAU7W,CAAC,EAAE;QACtD,IAAIuY,SAAS,GAAGhS,mBAAmB,CAACvG,CAAC,CAAC;QACtC,IAAIwY,KAAK,GAAGhT,GAAG,CAACqR,KAAK,CAACvV,KAAK,EAAE,EAAE,CAACsK,MAAM,CAAC2M,SAAS,CAAC,CAAC;QAClD,IAAIA,SAAS,IAAIjT,UAAU,CAACkT,KAAK,CAAC,EAAE;UAClC,IAAIC,MAAM;UACV,IAAIhB,KAAK;UACT,IAAI,YAAY,CAAC3T,IAAI,CAACyU,SAAS,CAAC,EAAE;YAChCd,KAAK,GAAGZ,KAAK,CAACa,YAAY,CAAC1X,CAAC,CAAC0Y,cAAc,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,MAAM;YACLjB,KAAK,GAAGZ,KAAK,CAACa,YAAY,CAAC1X,CAAC,CAAC;UAC/B;UACAwY,KAAK,CAAC,CAACC,MAAM,GAAGhB,KAAK,MAAM,IAAI,IAAIgB,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,CAAC,CAAC,EAAEzY,CAAC,CAAC;QACxE;MACF,CAAC,CAAC;MACFwE,eAAe,CAACqS,KAAK,EAAE,aAAa,EAAE,UAAU7W,CAAC,EAAE;QACjD,IAAIyX,KAAK,GAAGZ,KAAK,CAACa,YAAY,CAAC1X,CAAC,CAAC;QACjC,IAAIyX,KAAK,EAAE;UACT,IAAIkB,WAAW,GAAGrU,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmT,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YAC5D5G,eAAe,EAAE;UACnB,CAAC,CAAC;UACFgG,KAAK,CAACG,QAAQ,CAAC2B,WAAW,CAAC;UAC3B9B,KAAK,CAACW,gBAAgB,CAACmB,WAAW,CAAC;UACnC,IAAIC,OAAO,GAAG/B,KAAK,CAACvV,KAAK,CAACsX,OAAO;UACjC,IAAItT,UAAU,CAACsT,OAAO,CAAC,EAAE;YACvBA,OAAO,CAACD,WAAW,EAAE3Y,CAAC,CAAC;UACzB;QACF;MACF,CAAC,CAAC;MACFwE,eAAe,CAACqS,KAAK,EAAE,iBAAiB,EAAE,UAAU7W,CAAC,EAAE;QACrD,IAAI6Y,WAAW,GAAGhC,KAAK,CAACvV,KAAK,CAACuX,WAAW;QACzC,IAAIvT,UAAU,CAACuT,WAAW,CAAC,EAAE;UAC3B,IAAIC,WAAW,GAAGjC,KAAK,CAACa,YAAY,CAAC1X,CAAC,CAAC;UACvC6Y,WAAW,CAACC,WAAW,EAAE9Y,CAAC,CAAC;QAC7B;MACF,CAAC,CAAC;MACFwE,eAAe,CAACqS,KAAK,EAAE,eAAe,EAAE,UAAU7W,CAAC,EAAE;QACnD,IAAI+Y,SAAS,GAAGlC,KAAK,CAACvV,KAAK,CAACyX,SAAS;QACrC,IAAIzT,UAAU,CAACyT,SAAS,CAAC,EAAE;UACzB,IAAIC,WAAW,GAAGnC,KAAK,CAACa,YAAY,CAAC1X,CAAC,CAAC;UACvC+Y,SAAS,CAACC,WAAW,EAAEhZ,CAAC,CAAC;QAC3B;MACF,CAAC,CAAC;MACFwE,eAAe,CAACqS,KAAK,EAAE,iBAAiB,EAAE,UAAU7W,CAAC,EAAE;QACrD,IAAIA,CAAC,CAAC0Y,cAAc,IAAI,IAAI,IAAI1Y,CAAC,CAAC0Y,cAAc,CAAC1Z,MAAM,GAAG,CAAC,EAAE;UAC3D6X,KAAK,CAACuB,+BAA+B,CAACpY,CAAC,CAAC0Y,cAAc,CAAC,CAAC,CAAC,CAAC;QAC5D;MACF,CAAC,CAAC;MACFlU,eAAe,CAACqS,KAAK,EAAE,kBAAkB,EAAE,UAAU7W,CAAC,EAAE;QACtD,IAAIA,CAAC,CAAC0Y,cAAc,IAAI,IAAI,IAAI1Y,CAAC,CAAC0Y,cAAc,CAAC1Z,MAAM,GAAG,CAAC,EAAE;UAC3D6X,KAAK,CAACoC,eAAe,CAACjZ,CAAC,CAAC0Y,cAAc,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC;MACFlU,eAAe,CAACqS,KAAK,EAAE,gBAAgB,EAAE,UAAU7W,CAAC,EAAE;QACpD,IAAIA,CAAC,CAAC0Y,cAAc,IAAI,IAAI,IAAI1Y,CAAC,CAAC0Y,cAAc,CAAC1Z,MAAM,GAAG,CAAC,EAAE;UAC3D6X,KAAK,CAACqC,aAAa,CAAClZ,CAAC,CAAC0Y,cAAc,CAAC,CAAC,CAAC,CAAC;QAC1C;MACF,CAAC,CAAC;MACFlU,eAAe,CAACqS,KAAK,EAAE,mBAAmB,EAAE,UAAU7W,CAAC,EAAE;QACvD,IAAImZ,aAAa,GAAGtC,KAAK,CAACvV,KAAK,CAAC6X,aAAa;QAC7C,IAAI7T,UAAU,CAAC6T,aAAa,CAAC,EAAE;UAC7B,IAAIC,WAAW,GAAGvC,KAAK,CAACa,YAAY,CAAC1X,CAAC,CAAC;UACvCmZ,aAAa,CAACC,WAAW,EAAEpZ,CAAC,CAAC;QAC/B;MACF,CAAC,CAAC;MACFwE,eAAe,CAACqS,KAAK,EAAE,mBAAmB,EAAE,UAAU7W,CAAC,EAAE;QACvD,IAAIqZ,aAAa,GAAGxC,KAAK,CAACvV,KAAK,CAAC+X,aAAa;QAC7C,IAAI/T,UAAU,CAAC+T,aAAa,CAAC,EAAE;UAC7B,IAAIC,WAAW,GAAGzC,KAAK,CAACa,YAAY,CAAC1X,CAAC,CAAC;UACvCqZ,aAAa,CAACC,WAAW,EAAEtZ,CAAC,CAAC;QAC/B;MACF,CAAC,CAAC;MACFwE,eAAe,CAACqS,KAAK,EAAE,kBAAkB,EAAE,UAAU3L,IAAI,EAAE;QACzD,IAAI2L,KAAK,CAACvV,KAAK,CAAC6V,MAAM,KAAKpL,SAAS,EAAE;UACpCrD,WAAW,CAAC6Q,IAAI,CAAC5Q,UAAU,EAAEkO,KAAK,CAACvV,KAAK,CAAC6V,MAAM,EAAEjM,IAAI,EAAE2L,KAAK,CAACO,kBAAkB,CAAC;QAClF;MACF,CAAC,CAAC;MACF5S,eAAe,CAACqS,KAAK,EAAE,gBAAgB,EAAE,UAAU3L,IAAI,EAAE;QACvD,IAAIsO,WAAW,GAAG3C,KAAK,CAACvV,KAAK;UAC3B4I,MAAM,GAAGsP,WAAW,CAACtP,MAAM;UAC3BmN,UAAU,GAAGmC,WAAW,CAACnC,UAAU;QACrC,IAAI3D,QAAQ,GAAGmD,KAAK,CAAC5K,KAAK,CAACyH,QAAQ;QACnC,IAAIrI,cAAc,GAAGH,IAAI,CAACG,cAAc;UACtCC,YAAY,GAAGJ,IAAI,CAACI,YAAY;QAClC,IAAIJ,IAAI,CAACG,cAAc,KAAKU,SAAS,IAAIb,IAAI,CAACI,YAAY,KAAKS,SAAS,EAAE;UACxE8K,KAAK,CAACG,QAAQ,CAAC1S,aAAa,CAAC;YAC3B+G,cAAc,EAAEA,cAAc;YAC9BC,YAAY,EAAEA;UAChB,CAAC,EAAEwK,yCAAyC,CAAC;YAC3CxU,KAAK,EAAEuV,KAAK,CAACvV,KAAK;YAClB+J,cAAc,EAAEA,cAAc;YAC9BC,YAAY,EAAEA,YAAY;YAC1BoI,QAAQ,EAAEA;UACZ,CAAC,EAAEmD,KAAK,CAAC5K,KAAK,CAAC,CAAC,CAAC;QACnB,CAAC,MAAM,IAAIf,IAAI,CAACmC,kBAAkB,KAAKtB,SAAS,EAAE;UAChD,IAAIc,MAAM,GAAG3B,IAAI,CAAC2B,MAAM;YACtBC,MAAM,GAAG5B,IAAI,CAAC4B,MAAM;UACtB,IAAIO,kBAAkB,GAAGnC,IAAI,CAACmC,kBAAkB;UAChD,IAAIoM,YAAY,GAAG5C,KAAK,CAAC5K,KAAK;YAC5BmG,MAAM,GAAGqH,YAAY,CAACrH,MAAM;YAC5B9H,YAAY,GAAGmP,YAAY,CAACnP,YAAY;UAC1C,IAAI,CAAC8H,MAAM,EAAE;YACX;UACF;UACA,IAAI,OAAOiF,UAAU,KAAK,UAAU,EAAE;YACpC;YACAhK,kBAAkB,GAAGgK,UAAU,CAAC/M,YAAY,EAAEY,IAAI,CAAC;UACrD,CAAC,MAAM,IAAImM,UAAU,KAAK,OAAO,EAAE;YACjC;YACA;YACAhK,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;YACzB,KAAK,IAAIvO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwL,YAAY,CAACtL,MAAM,EAAEF,CAAC,EAAE,EAAE;cAC5C,IAAIwL,YAAY,CAACxL,CAAC,CAAC,CAAC0B,KAAK,KAAK0K,IAAI,CAACiB,WAAW,EAAE;gBAC9CkB,kBAAkB,GAAGvO,CAAC;gBACtB;cACF;YACF;UACF;UACA,IAAI4a,OAAO,GAAGpV,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8N,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;YACzDxI,CAAC,EAAEwI,MAAM,CAACL,IAAI;YACdlI,CAAC,EAAEuI,MAAM,CAACF;UACZ,CAAC,CAAC;UACF;UACA;UACA,IAAIyH,cAAc,GAAGnH,IAAI,CAACoH,GAAG,CAAC/M,MAAM,EAAE6M,OAAO,CAAC9P,CAAC,GAAG8P,OAAO,CAACjQ,KAAK,CAAC;UAChE,IAAIoQ,cAAc,GAAGrH,IAAI,CAACoH,GAAG,CAAC9M,MAAM,EAAE4M,OAAO,CAAC7P,CAAC,GAAG6P,OAAO,CAAChQ,MAAM,CAAC;UACjE,IAAIyC,WAAW,GAAG7B,YAAY,CAAC+C,kBAAkB,CAAC,IAAI/C,YAAY,CAAC+C,kBAAkB,CAAC,CAAC7M,KAAK;UAC5F,IAAI2M,aAAa,GAAGnB,iBAAiB,CAAC6K,KAAK,CAAC5K,KAAK,EAAE4K,KAAK,CAACvV,KAAK,CAAC4J,IAAI,EAAEmC,kBAAkB,CAAC;UACxF,IAAID,gBAAgB,GAAG9C,YAAY,CAAC+C,kBAAkB,CAAC,GAAG;YACxDzD,CAAC,EAAEM,MAAM,KAAK,YAAY,GAAGI,YAAY,CAAC+C,kBAAkB,CAAC,CAACzC,UAAU,GAAG+O,cAAc;YACzF9P,CAAC,EAAEK,MAAM,KAAK,YAAY,GAAG2P,cAAc,GAAGvP,YAAY,CAAC+C,kBAAkB,CAAC,CAACzC;UACjF,CAAC,GAAGjB,gBAAgB;UACpBkN,KAAK,CAACG,QAAQ,CAAC1S,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4G,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;YACxDiB,WAAW,EAAEA,WAAW;YACxBiB,gBAAgB,EAAEA,gBAAgB;YAClCD,aAAa,EAAEA,aAAa;YAC5BE,kBAAkB,EAAEA;UACtB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACLwJ,KAAK,CAACG,QAAQ,CAAC9L,IAAI,CAAC;QACtB;MACF,CAAC,CAAC;MACF1G,eAAe,CAACqS,KAAK,EAAE,cAAc,EAAE,UAAU9M,OAAO,EAAE;QACxD,IAAI+P,qBAAqB;QACzB,IAAIC,YAAY,GAAGlD,KAAK,CAAC5K,KAAK;UAC5B4E,eAAe,GAAGkJ,YAAY,CAAClJ,eAAe;UAC9CzD,gBAAgB,GAAG2M,YAAY,CAAC3M,gBAAgB;UAChDD,aAAa,GAAG4M,YAAY,CAAC5M,aAAa;UAC1CiF,MAAM,GAAG2H,YAAY,CAAC3H,MAAM;UAC5B/E,kBAAkB,GAAG0M,YAAY,CAAC1M,kBAAkB;UACpDkD,mBAAmB,GAAGwJ,YAAY,CAACxJ,mBAAmB;QACxD,IAAIyJ,gBAAgB,GAAGnD,KAAK,CAACoD,mBAAmB,CAAC,CAAC;QAClD;QACA,IAAIC,QAAQ,GAAG,CAACJ,qBAAqB,GAAG/P,OAAO,CAACzI,KAAK,CAAC6Y,MAAM,MAAM,IAAI,IAAIL,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGjJ,eAAe;QACpJ,IAAI3G,MAAM,GAAG2M,KAAK,CAACvV,KAAK,CAAC4I,MAAM;QAC/B,IAAIhL,GAAG,GAAG6K,OAAO,CAAC7K,GAAG,IAAI,kBAAkB;QAC3C,OAAO,aAAa8F,KAAK,CAACoV,aAAa,CAACjR,MAAM,EAAE;UAC9CjK,GAAG,EAAEA,GAAG;UACRkO,gBAAgB,EAAEA,gBAAgB;UAClCD,aAAa,EAAEA,aAAa;UAC5BE,kBAAkB,EAAEA,kBAAkB;UACtC0F,SAAS,EAAEA,SAAS;UACpBhJ,OAAO,EAAEA,OAAO;UAChBmQ,QAAQ,EAAEA,QAAQ;UAClBhQ,MAAM,EAAEA,MAAM;UACdkI,MAAM,EAAEA,MAAM;UACd7B,mBAAmB,EAAEA,mBAAmB;UACxCyJ,gBAAgB,EAAEA;QACpB,CAAC,CAAC;MACJ,CAAC,CAAC;MACFxV,eAAe,CAACqS,KAAK,EAAE,iBAAiB,EAAE,UAAU9M,OAAO,EAAE8K,WAAW,EAAElK,KAAK,EAAE;QAC/E,IAAImB,QAAQ,GAAGtG,GAAG,CAACuE,OAAO,EAAE,eAAe,CAAC;QAC5C,IAAIsG,OAAO,GAAG7K,GAAG,CAACqR,KAAK,CAAC5K,KAAK,EAAE,EAAE,CAACL,MAAM,CAACE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC1D,IAAIuO,mBAAmB,GAAGtQ,OAAO,CAACiE,IAAI,CAACC,YAAY;QACnD,IAAIqM,YAAY,GAAGD,mBAAmB,KAAKtO,SAAS,GAAGzH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+V,mBAAmB,CAAC,EAAEtQ,OAAO,CAACzI,KAAK,CAAC,GAAGyI,OAAO,CAACzI,KAAK;QAC3I,IAAIiZ,UAAU,GAAGlK,OAAO,IAAIA,OAAO,CAACiK,YAAY,CAAC,EAAE,CAAC1O,MAAM,CAACE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;QAC5E,OAAO,aAAa5G,YAAY,CAAC6E,OAAO,EAAEzF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiW,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;UACzFC,SAAS,EAAE7U,IAAI,CAACmG,QAAQ,EAAEyO,UAAU,CAACC,SAAS,CAAC;UAC/Ctb,GAAG,EAAE6K,OAAO,CAAC7K,GAAG,IAAI,EAAE,CAAC0M,MAAM,CAACiJ,WAAW,EAAE,GAAG,CAAC,CAACjJ,MAAM,CAACjB,KAAK,CAAC;UAC7DqC,KAAK,EAAEhF,cAAc,CAACuS,UAAU,EAAE,IAAI;QACxC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MACF/V,eAAe,CAACqS,KAAK,EAAE,iBAAiB,EAAE,UAAU9M,OAAO,EAAE;QAC3D,IAAI0Q,cAAc,GAAG1Q,OAAO,CAACzI,KAAK;UAChCoZ,WAAW,GAAGD,cAAc,CAACC,WAAW;UACxCC,WAAW,GAAGF,cAAc,CAACE,WAAW;UACxCC,WAAW,GAAGH,cAAc,CAACG,WAAW;QAC1C,IAAIC,YAAY,GAAGhE,KAAK,CAAC5K,KAAK;UAC5B6O,aAAa,GAAGD,YAAY,CAACC,aAAa;UAC1CC,YAAY,GAAGF,YAAY,CAACE,YAAY;QAC1C,IAAIC,UAAU,GAAGjU,qBAAqB,CAAC+T,aAAa,CAAC;QACrD,IAAIG,SAAS,GAAGlU,qBAAqB,CAACgU,YAAY,CAAC;QACnD,IAAIhQ,EAAE,GAAGkQ,SAAS,CAAClQ,EAAE;UACnBC,EAAE,GAAGiQ,SAAS,CAACjQ,EAAE;UACjBkQ,WAAW,GAAGD,SAAS,CAACC,WAAW;UACnCC,WAAW,GAAGF,SAAS,CAACE,WAAW;QACrC,OAAO,aAAajW,YAAY,CAAC6E,OAAO,EAAE;UACxC4Q,WAAW,EAAEla,KAAK,CAACC,OAAO,CAACia,WAAW,CAAC,GAAGA,WAAW,GAAG3S,cAAc,CAACiT,SAAS,EAAE,IAAI,CAAC,CAACxF,GAAG,CAAC,UAAUjL,KAAK,EAAE;YAC3G,OAAOA,KAAK,CAACI,UAAU;UACzB,CAAC,CAAC;UACFgQ,WAAW,EAAEna,KAAK,CAACC,OAAO,CAACka,WAAW,CAAC,GAAGA,WAAW,GAAG5S,cAAc,CAACgT,UAAU,EAAE,IAAI,CAAC,CAACvF,GAAG,CAAC,UAAUjL,KAAK,EAAE;YAC5G,OAAOA,KAAK,CAACI,UAAU;UACzB,CAAC,CAAC;UACFG,EAAE,EAAEA,EAAE;UACNC,EAAE,EAAEA,EAAE;UACNkQ,WAAW,EAAEA,WAAW;UACxBC,WAAW,EAAEA,WAAW;UACxBjc,GAAG,EAAE6K,OAAO,CAAC7K,GAAG,IAAI,YAAY;UAChCwb,WAAW,EAAEA;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;MACF;AACN;AACA;AACA;MACMlW,eAAe,CAACqS,KAAK,EAAE,cAAc,EAAE,YAAY;QACjD,IAAIN,uBAAuB,GAAGM,KAAK,CAAC5K,KAAK,CAACsK,uBAAuB;QACjE,IAAI6E,YAAY,GAAGvE,KAAK,CAACvV,KAAK;UAC5BqM,QAAQ,GAAGyN,YAAY,CAACzN,QAAQ;UAChClE,KAAK,GAAG2R,YAAY,CAAC3R,KAAK;UAC1BC,MAAM,GAAG0R,YAAY,CAAC1R,MAAM;QAC9B,IAAIgI,MAAM,GAAGmF,KAAK,CAACvV,KAAK,CAACoQ,MAAM,IAAI,CAAC,CAAC;QACrC,IAAI2J,WAAW,GAAG5R,KAAK,IAAIiI,MAAM,CAACK,IAAI,IAAI,CAAC,CAAC,IAAIL,MAAM,CAACM,KAAK,IAAI,CAAC,CAAC;QAClE,IAAI1Q,KAAK,GAAGsG,cAAc,CAAC;UACzB+F,QAAQ,EAAEA,QAAQ;UAClB4I,uBAAuB,EAAEA,uBAAuB;UAChD8E,WAAW,EAAEA,WAAW;UACxB/H,aAAa,EAAEA;QACjB,CAAC,CAAC;QACF,IAAI,CAAChS,KAAK,EAAE;UACV,OAAO,IAAI;QACb;QACA,IAAIgN,IAAI,GAAGhN,KAAK,CAACgN,IAAI;UACnBgN,UAAU,GAAG3a,wBAAwB,CAACW,KAAK,EAAErD,SAAS,CAAC;QACzD,OAAO,aAAaiH,YAAY,CAACoJ,IAAI,EAAEhK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgX,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;UACtFC,UAAU,EAAE9R,KAAK;UACjB+R,WAAW,EAAE9R,MAAM;UACnBgI,MAAM,EAAEA,MAAM;UACd+J,YAAY,EAAE5E,KAAK,CAAC6E;QACtB,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MACF;AACN;AACA;AACA;MACMlX,eAAe,CAACqS,KAAK,EAAE,eAAe,EAAE,YAAY;QAClD,IAAI8E,qBAAqB;QACzB,IAAIC,YAAY,GAAG/E,KAAK,CAACvV,KAAK;UAC5BqM,QAAQ,GAAGiO,YAAY,CAACjO,QAAQ;UAChCkO,kBAAkB,GAAGD,YAAY,CAACC,kBAAkB;QACtD,IAAIC,WAAW,GAAGzV,eAAe,CAACsH,QAAQ,EAAE5H,OAAO,CAAC;QACpD,IAAI,CAAC+V,WAAW,EAAE;UAChB,OAAO,IAAI;QACb;QACA,IAAIC,YAAY,GAAGlF,KAAK,CAAC5K,KAAK;UAC5B4E,eAAe,GAAGkL,YAAY,CAAClL,eAAe;UAC9CzD,gBAAgB,GAAG2O,YAAY,CAAC3O,gBAAgB;UAChDD,aAAa,GAAG4O,YAAY,CAAC5O,aAAa;UAC1ChB,WAAW,GAAG4P,YAAY,CAAC5P,WAAW;UACtCiG,MAAM,GAAG2J,YAAY,CAAC3J,MAAM;;QAE9B;QACA;QACA;QACA,IAAI8H,QAAQ,GAAG,CAACyB,qBAAqB,GAAGG,WAAW,CAACxa,KAAK,CAAC6Y,MAAM,MAAM,IAAI,IAAIwB,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG9K,eAAe;QACxJ,OAAO,aAAa3L,YAAY,CAAC4W,WAAW,EAAE;UAC5CpC,OAAO,EAAEpV,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8N,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;YACpDxI,CAAC,EAAEwI,MAAM,CAACL,IAAI;YACdlI,CAAC,EAAEuI,MAAM,CAACF;UACZ,CAAC,CAAC;UACFiI,MAAM,EAAED,QAAQ;UAChB8B,KAAK,EAAE7P,WAAW;UAClBI,OAAO,EAAE2N,QAAQ,GAAG/M,aAAa,GAAG,EAAE;UACtCvC,UAAU,EAAEwC,gBAAgB;UAC5ByO,kBAAkB,EAAEA;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC;MACFrX,eAAe,CAACqS,KAAK,EAAE,aAAa,EAAE,UAAU9M,OAAO,EAAE;QACvD,IAAIkS,YAAY,GAAGpF,KAAK,CAACvV,KAAK;UAC5BoQ,MAAM,GAAGuK,YAAY,CAACvK,MAAM;UAC5BxG,IAAI,GAAG+Q,YAAY,CAAC/Q,IAAI;QAC1B,IAAIgR,YAAY,GAAGrF,KAAK,CAAC5K,KAAK;UAC5BmG,MAAM,GAAG8J,YAAY,CAAC9J,MAAM;UAC5B/G,cAAc,GAAG6Q,YAAY,CAAC7Q,cAAc;UAC5CC,YAAY,GAAG4Q,YAAY,CAAC5Q,YAAY;UACxCoI,QAAQ,GAAGwI,YAAY,CAACxI,QAAQ;;QAElC;QACA,OAAO,aAAaxO,YAAY,CAAC6E,OAAO,EAAE;UACxC7K,GAAG,EAAE6K,OAAO,CAAC7K,GAAG,IAAI,iBAAiB;UACrCid,QAAQ,EAAE9U,oBAAoB,CAACwP,KAAK,CAACuF,iBAAiB,EAAErS,OAAO,CAACzI,KAAK,CAAC6a,QAAQ,CAAC;UAC/EjR,IAAI,EAAEA,IAAI;UACVtB,CAAC,EAAE3C,QAAQ,CAAC8C,OAAO,CAACzI,KAAK,CAACsI,CAAC,CAAC,GAAGG,OAAO,CAACzI,KAAK,CAACsI,CAAC,GAAGwI,MAAM,CAACL,IAAI;UAC5DlI,CAAC,EAAE5C,QAAQ,CAAC8C,OAAO,CAACzI,KAAK,CAACuI,CAAC,CAAC,GAAGE,OAAO,CAACzI,KAAK,CAACuI,CAAC,GAAGuI,MAAM,CAACF,GAAG,GAAGE,MAAM,CAAC1I,MAAM,GAAG0I,MAAM,CAACC,WAAW,IAAIX,MAAM,CAACS,MAAM,IAAI,CAAC,CAAC;UACvH1I,KAAK,EAAExC,QAAQ,CAAC8C,OAAO,CAACzI,KAAK,CAACmI,KAAK,CAAC,GAAGM,OAAO,CAACzI,KAAK,CAACmI,KAAK,GAAG2I,MAAM,CAAC3I,KAAK;UACzEkH,UAAU,EAAEtF,cAAc;UAC1BuF,QAAQ,EAAEtF,YAAY;UACtBoI,QAAQ,EAAE,QAAQ,CAAC9H,MAAM,CAAC8H,QAAQ;QACpC,CAAC,CAAC;MACJ,CAAC,CAAC;MACFlP,eAAe,CAACqS,KAAK,EAAE,wBAAwB,EAAE,UAAU9M,OAAO,EAAE8K,WAAW,EAAElK,KAAK,EAAE;QACtF,IAAI,CAACZ,OAAO,EAAE;UACZ,OAAO,IAAI;QACb;QACA,IAAIsS,MAAM,GAAGxF,KAAK;UAChByF,UAAU,GAAGD,MAAM,CAACC,UAAU;QAChC,IAAIC,YAAY,GAAG1F,KAAK,CAAC5K,KAAK;UAC5BsF,QAAQ,GAAGgL,YAAY,CAAChL,QAAQ;UAChCE,QAAQ,GAAG8K,YAAY,CAAC9K,QAAQ;UAChCW,MAAM,GAAGmK,YAAY,CAACnK,MAAM;QAC9B,IAAIiI,mBAAmB,GAAGtQ,OAAO,CAACiE,IAAI,CAACC,YAAY,IAAI,CAAC,CAAC;QACzD,IAAIuO,eAAe,GAAGzS,OAAO,CAACzI,KAAK;UACjCmb,qBAAqB,GAAGD,eAAe,CAACE,OAAO;UAC/CA,OAAO,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGpC,mBAAmB,CAACqC,OAAO,GAAGD,qBAAqB;UAChGE,qBAAqB,GAAGH,eAAe,CAACI,OAAO;UAC/CA,OAAO,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGtC,mBAAmB,CAACuC,OAAO,GAAGD,qBAAqB;QAClG,OAAO,aAAazX,YAAY,CAAC6E,OAAO,EAAE;UACxC7K,GAAG,EAAE6K,OAAO,CAAC7K,GAAG,IAAI,EAAE,CAAC0M,MAAM,CAACiJ,WAAW,EAAE,GAAG,CAAC,CAACjJ,MAAM,CAACjB,KAAK,CAAC;UAC7DrB,KAAK,EAAEiI,QAAQ,CAACmL,OAAO,CAAC;UACxBnT,KAAK,EAAEkI,QAAQ,CAACmL,OAAO,CAAC;UACxBlD,OAAO,EAAE;YACP9P,CAAC,EAAEwI,MAAM,CAACL,IAAI;YACdlI,CAAC,EAAEuI,MAAM,CAACF,GAAG;YACbzI,KAAK,EAAE2I,MAAM,CAAC3I,KAAK;YACnBC,MAAM,EAAE0I,MAAM,CAAC1I;UACjB,CAAC;UACD4S,UAAU,EAAEA;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;MACF9X,eAAe,CAACqS,KAAK,EAAE,oBAAoB,EAAE,UAAUgG,MAAM,EAAE;QAC7D,IAAIvO,IAAI,GAAGuO,MAAM,CAACvO,IAAI;UACpBwO,WAAW,GAAGD,MAAM,CAACC,WAAW;UAChCC,SAAS,GAAGF,MAAM,CAACE,SAAS;UAC5BlH,UAAU,GAAGgH,MAAM,CAAChH,UAAU;UAC9BmH,OAAO,GAAGH,MAAM,CAACG,OAAO;QAC1B,IAAIvR,MAAM,GAAG,EAAE;QACf;QACA,IAAIvM,GAAG,GAAGoP,IAAI,CAAChN,KAAK,CAACpC,GAAG;QACxB,IAAI+d,aAAa,GAAG3O,IAAI,CAACA,IAAI,CAACN,IAAI,CAACC,YAAY,KAAKlC,SAAS,GAAGzH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgK,IAAI,CAACA,IAAI,CAACN,IAAI,CAACC,YAAY,CAAC,EAAEK,IAAI,CAACA,IAAI,CAAChN,KAAK,CAAC,GAAGgN,IAAI,CAACA,IAAI,CAAChN,KAAK;QAChK,IAAI4b,SAAS,GAAGD,aAAa,CAACC,SAAS;UACrC1Q,OAAO,GAAGyQ,aAAa,CAACzQ,OAAO;QACjC,IAAI2Q,QAAQ,GAAG7Y,aAAa,CAACA,aAAa,CAAC;UACzCqG,KAAK,EAAEkL,UAAU;UACjBrJ,OAAO,EAAEA,OAAO;UAChBzB,EAAE,EAAE+R,WAAW,CAAClT,CAAC;UACjBoB,EAAE,EAAE8R,WAAW,CAACjT,CAAC;UACjBhK,CAAC,EAAE,CAAC;UACJud,IAAI,EAAEvV,yBAAyB,CAACyG,IAAI,CAACA,IAAI,CAAC;UAC1C+O,WAAW,EAAE,CAAC;UACdC,MAAM,EAAE,MAAM;UACd/Q,OAAO,EAAEuQ,WAAW,CAACvQ,OAAO;UAC5B/L,KAAK,EAAEsc,WAAW,CAACtc;QACrB,CAAC,EAAE2F,WAAW,CAAC+W,SAAS,EAAE,KAAK,CAAC,CAAC,EAAEtU,kBAAkB,CAACsU,SAAS,CAAC,CAAC;QACjEzR,MAAM,CAAClL,IAAI,CAACiW,uBAAuB,CAAC+G,eAAe,CAACL,SAAS,EAAEC,QAAQ,EAAE,EAAE,CAACvR,MAAM,CAAC1M,GAAG,EAAE,eAAe,CAAC,CAAC0M,MAAM,CAACiK,UAAU,CAAC,CAAC,CAAC;QAC7H,IAAIkH,SAAS,EAAE;UACbtR,MAAM,CAAClL,IAAI,CAACiW,uBAAuB,CAAC+G,eAAe,CAACL,SAAS,EAAE5Y,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6Y,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;YAC5GpS,EAAE,EAAEgS,SAAS,CAACnT,CAAC;YACfoB,EAAE,EAAE+R,SAAS,CAAClT;UAChB,CAAC,CAAC,EAAE,EAAE,CAAC+B,MAAM,CAAC1M,GAAG,EAAE,aAAa,CAAC,CAAC0M,MAAM,CAACiK,UAAU,CAAC,CAAC,CAAC;QACxD,CAAC,MAAM,IAAImH,OAAO,EAAE;UAClBvR,MAAM,CAAClL,IAAI,CAAC,IAAI,CAAC;QACnB;QACA,OAAOkL,MAAM;MACf,CAAC,CAAC;MACFjH,eAAe,CAACqS,KAAK,EAAE,oBAAoB,EAAE,UAAU9M,OAAO,EAAE8K,WAAW,EAAElK,KAAK,EAAE;QAClF,IAAI2D,IAAI,GAAGuI,KAAK,CAAC2G,gBAAgB,CAACzT,OAAO,EAAE8K,WAAW,EAAElK,KAAK,CAAC;QAC9D,IAAI,CAAC2D,IAAI,EAAE;UACT,OAAO,IAAI;QACb;QACA,IAAI0L,gBAAgB,GAAGnD,KAAK,CAACoD,mBAAmB,CAAC,CAAC;QAClD,IAAIwD,YAAY,GAAG5G,KAAK,CAAC5K,KAAK;UAC5B4E,eAAe,GAAG4M,YAAY,CAAC5M,eAAe;UAC9CzE,WAAW,GAAGqR,YAAY,CAACrR,WAAW;UACtCiB,kBAAkB,GAAGoQ,YAAY,CAACpQ,kBAAkB;UACpDlB,WAAW,GAAGsR,YAAY,CAACtR,WAAW;QACxC,IAAIwB,QAAQ,GAAGkJ,KAAK,CAACvV,KAAK,CAACqM,QAAQ;QACnC,IAAImO,WAAW,GAAGzV,eAAe,CAACsH,QAAQ,EAAE5H,OAAO,CAAC;QACpD;QACA,IAAI2X,WAAW,GAAGpP,IAAI,CAAChN,KAAK;UAC1Bqc,MAAM,GAAGD,WAAW,CAACC,MAAM;UAC3BX,OAAO,GAAGU,WAAW,CAACV,OAAO;UAC7BY,QAAQ,GAAGF,WAAW,CAACE,QAAQ;QACjC,IAAIX,aAAa,GAAG3O,IAAI,CAACA,IAAI,CAACN,IAAI,CAACC,YAAY,KAAKlC,SAAS,GAAGzH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgK,IAAI,CAACA,IAAI,CAACN,IAAI,CAACC,YAAY,CAAC,EAAEK,IAAI,CAACA,IAAI,CAAChN,KAAK,CAAC,GAAGgN,IAAI,CAACA,IAAI,CAAChN,KAAK;QAChK,IAAI4b,SAAS,GAAGD,aAAa,CAACC,SAAS;UACrC7N,IAAI,GAAG4N,aAAa,CAAC5N,IAAI;UACzBwO,SAAS,GAAGZ,aAAa,CAACY,SAAS;UACnCC,WAAW,GAAGb,aAAa,CAACa,WAAW;QACzC,IAAIC,SAAS,GAAGtb,OAAO,CAAC,CAAC4M,IAAI,IAAIwB,eAAe,IAAIiL,WAAW,KAAKoB,SAAS,IAAIW,SAAS,IAAIC,WAAW,CAAC,CAAC;QAC3G,IAAIE,UAAU,GAAG,CAAC,CAAC;QACnB,IAAIhE,gBAAgB,KAAK,MAAM,IAAI8B,WAAW,IAAIA,WAAW,CAACxa,KAAK,CAAC2c,OAAO,KAAK,OAAO,EAAE;UACvFD,UAAU,GAAG;YACXpF,OAAO,EAAEvR,oBAAoB,CAACwP,KAAK,CAACqH,oBAAoB,EAAEnU,OAAO,CAACzI,KAAK,CAACsX,OAAO;UACjF,CAAC;QACH,CAAC,MAAM,IAAIoB,gBAAgB,KAAK,MAAM,EAAE;UACtCgE,UAAU,GAAG;YACX1F,YAAY,EAAEjR,oBAAoB,CAACwP,KAAK,CAACsH,oBAAoB,EAAEpU,OAAO,CAACzI,KAAK,CAACgX,YAAY,CAAC;YAC1FV,YAAY,EAAEvQ,oBAAoB,CAACwP,KAAK,CAACqH,oBAAoB,EAAEnU,OAAO,CAACzI,KAAK,CAACsW,YAAY;UAC3F,CAAC;QACH;QACA,IAAIwG,aAAa,GAAG,aAAalZ,YAAY,CAAC6E,OAAO,EAAEzF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgK,IAAI,CAAChN,KAAK,CAAC,EAAE0c,UAAU,CAAC,CAAC;QAChH,SAASK,eAAeA,CAAC7T,KAAK,EAAE;UAC9B;UACA,OAAO,OAAO4B,WAAW,CAACI,OAAO,KAAK,UAAU,GAAGJ,WAAW,CAACI,OAAO,CAAChC,KAAK,CAAC+B,OAAO,CAAC,GAAG,IAAI;QAC9F;QACA,IAAIwR,SAAS,EAAE;UACb,IAAI1Q,kBAAkB,IAAI,CAAC,EAAE;YAC3B,IAAIyP,WAAW,EAAEC,SAAS;YAC1B,IAAI3Q,WAAW,CAACI,OAAO,IAAI,CAACJ,WAAW,CAACK,uBAAuB,EAAE;cAC/D;cACA,IAAI6R,YAAY,GAAG,OAAOlS,WAAW,CAACI,OAAO,KAAK,UAAU,GAAG6R,eAAe,GAAG,UAAU,CAACzS,MAAM,CAACQ,WAAW,CAACI,OAAO,CAAC9I,QAAQ,CAAC,CAAC,CAAC;cAClIoZ,WAAW,GAAGhW,gBAAgB,CAAC6W,MAAM,EAAEW,YAAY,EAAEnS,WAAW,CAAC;cACjE4Q,SAAS,GAAGC,OAAO,IAAIY,QAAQ,IAAI9W,gBAAgB,CAAC8W,QAAQ,EAAEU,YAAY,EAAEnS,WAAW,CAAC;YAC1F,CAAC,MAAM;cACL2Q,WAAW,GAAGa,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACtQ,kBAAkB,CAAC;cACxF0P,SAAS,GAAGC,OAAO,IAAIY,QAAQ,IAAIA,QAAQ,CAACvQ,kBAAkB,CAAC;YACjE;YACA,IAAIyQ,WAAW,IAAID,SAAS,EAAE;cAC5B,IAAItT,WAAW,GAAGR,OAAO,CAACzI,KAAK,CAACiJ,WAAW,KAAKwB,SAAS,GAAGhC,OAAO,CAACzI,KAAK,CAACiJ,WAAW,GAAG8C,kBAAkB;cAC1G,OAAO,CAAC,aAAanI,YAAY,CAAC6E,OAAO,EAAEzF,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgK,IAAI,CAAChN,KAAK,CAAC,EAAE0c,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;gBACrHzT,WAAW,EAAEA;cACf,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;YAClB;YACA,IAAI,CAAClF,KAAK,CAACyX,WAAW,CAAC,EAAE;cACvB,OAAO,CAACsB,aAAa,CAAC,CAACxS,MAAM,CAACxI,kBAAkB,CAACyT,KAAK,CAAC0H,kBAAkB,CAAC;gBACxEjQ,IAAI,EAAEA,IAAI;gBACVwO,WAAW,EAAEA,WAAW;gBACxBC,SAAS,EAAEA,SAAS;gBACpBlH,UAAU,EAAExI,kBAAkB;gBAC9B2P,OAAO,EAAEA;cACX,CAAC,CAAC,CAAC,CAAC;YACN;UACF,CAAC,MAAM;YACL,IAAIwB,iBAAiB;YACrB;AACZ;AACA;AACA;AACA;AACA;AACA;YACY,IAAIC,MAAM,GAAG,CAACD,iBAAiB,GAAG3H,KAAK,CAAC6H,WAAW,CAAC7H,KAAK,CAAC5K,KAAK,CAACmB,gBAAgB,CAAC,MAAM,IAAI,IAAIoR,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAG;gBAC9IJ,aAAa,EAAEA;cACjB,CAAC;cACDO,oBAAoB,GAAGF,MAAM,CAACL,aAAa;cAC3CQ,qBAAqB,GAAGD,oBAAoB,CAACrQ,IAAI;cACjDuQ,MAAM,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG7U,OAAO,GAAG6U,qBAAqB;cAC3E/I,UAAU,GAAG8I,oBAAoB,CAAC9I,UAAU;YAC9C,IAAIyE,YAAY,GAAGhW,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgK,IAAI,CAAChN,KAAK,CAAC,EAAE0c,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;cAC7FzT,WAAW,EAAEsL;YACf,CAAC,CAAC;YACF,OAAO,CAAC,aAAa3Q,YAAY,CAAC2Z,MAAM,EAAEvE,YAAY,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;UACtE;QACF;QACA,IAAI0C,OAAO,EAAE;UACX,OAAO,CAACoB,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC;QACpC;QACA,OAAO,CAACA,aAAa,EAAE,IAAI,CAAC;MAC9B,CAAC,CAAC;MACF5Z,eAAe,CAACqS,KAAK,EAAE,kBAAkB,EAAE,UAAU9M,OAAO,EAAE8K,WAAW,EAAElK,KAAK,EAAE;QAChF,OAAO,aAAazF,YAAY,CAAC6E,OAAO,EAAEzF,aAAa,CAACA,aAAa,CAAC;UACpEpF,GAAG,EAAE,sBAAsB,CAAC0M,MAAM,CAACjB,KAAK;QAC1C,CAAC,EAAEkM,KAAK,CAACvV,KAAK,CAAC,EAAEuV,KAAK,CAAC5K,KAAK,CAAC,CAAC;MAChC,CAAC,CAAC;MACFzH,eAAe,CAACqS,KAAK,EAAE,WAAW,EAAE;QAClCiI,aAAa,EAAE;UACbC,OAAO,EAAEjV,UAAU;UACnBkV,IAAI,EAAE;QACR,CAAC;QACDC,aAAa,EAAE;UACbF,OAAO,EAAElI,KAAK,CAACqI;QACjB,CAAC;QACDC,aAAa,EAAE;UACbJ,OAAO,EAAEjV;QACX,CAAC;QACDsV,YAAY,EAAE;UACZL,OAAO,EAAElI,KAAK,CAACqI;QACjB,CAAC;QACDG,KAAK,EAAE;UACLN,OAAO,EAAEjV;QACX,CAAC;QACDwV,KAAK,EAAE;UACLP,OAAO,EAAEjV;QACX,CAAC;QACDlD,KAAK,EAAE;UACLmY,OAAO,EAAElI,KAAK,CAAC0I,WAAW;UAC1BP,IAAI,EAAE;QACR,CAAC;QACDQ,GAAG,EAAE;UACHT,OAAO,EAAElI,KAAK,CAAC4I;QACjB,CAAC;QACDC,IAAI,EAAE;UACJX,OAAO,EAAElI,KAAK,CAAC4I;QACjB,CAAC;QACDE,IAAI,EAAE;UACJZ,OAAO,EAAElI,KAAK,CAAC4I;QACjB,CAAC;QACDG,KAAK,EAAE;UACLb,OAAO,EAAElI,KAAK,CAAC4I;QACjB,CAAC;QACDI,SAAS,EAAE;UACTd,OAAO,EAAElI,KAAK,CAAC4I;QACjB,CAAC;QACDK,OAAO,EAAE;UACPf,OAAO,EAAElI,KAAK,CAAC4I;QACjB,CAAC;QACDM,GAAG,EAAE;UACHhB,OAAO,EAAElI,KAAK,CAAC4I;QACjB,CAAC;QACDO,MAAM,EAAE;UACNjB,OAAO,EAAElI,KAAK,CAAC4I;QACjB,CAAC;QACD1Z,OAAO,EAAE;UACPgZ,OAAO,EAAElI,KAAK,CAACoJ,YAAY;UAC3BjB,IAAI,EAAE;QACR,CAAC;QACDkB,SAAS,EAAE;UACTnB,OAAO,EAAElI,KAAK,CAACsJ,eAAe;UAC9BnB,IAAI,EAAE;QACR,CAAC;QACDoB,cAAc,EAAE;UACdrB,OAAO,EAAElI,KAAK,CAACwJ;QACjB,CAAC;QACDC,eAAe,EAAE;UACfvB,OAAO,EAAElI,KAAK,CAACwJ;QACjB,CAAC;QACDE,UAAU,EAAE;UACVxB,OAAO,EAAElI,KAAK,CAAC2J;QACjB;MACF,CAAC,CAAC;MACF3J,KAAK,CAACyF,UAAU,GAAG,EAAE,CAAC1Q,MAAM,CAAC,CAAC+K,SAAS,GAAGD,MAAM,CAAC7E,EAAE,MAAM,IAAI,IAAI8E,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAGzP,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC;;MAElI;MACA2P,KAAK,CAACuB,+BAA+B,GAAG1S,QAAQ,CAACmR,KAAK,CAAC4J,uBAAuB,EAAE,CAAC7J,oBAAoB,GAAGF,MAAM,CAACgK,aAAa,MAAM,IAAI,IAAI9J,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;MAC7MC,KAAK,CAAC5K,KAAK,GAAG,CAAC,CAAC;MAChB,OAAO4K,KAAK;IACd;IACA/T,SAAS,CAAC0T,uBAAuB,EAAEC,UAAU,CAAC;IAC9C,OAAO5U,YAAY,CAAC2U,uBAAuB,EAAE,CAAC;MAC5CtX,GAAG,EAAE,mBAAmB;MACxBsB,KAAK,EAAE,SAASmgB,iBAAiBA,CAAA,EAAG;QAClC,IAAIC,qBAAqB,EAAEC,qBAAqB;QAChD,IAAI,CAACC,WAAW,CAAC,CAAC;QAClB,IAAI,CAACC,oBAAoB,CAACC,UAAU,CAAC;UACnCC,SAAS,EAAE,IAAI,CAACA,SAAS;UACzB7O,MAAM,EAAE;YACNL,IAAI,EAAE,CAAC6O,qBAAqB,GAAG,IAAI,CAACtf,KAAK,CAACoQ,MAAM,CAACK,IAAI,MAAM,IAAI,IAAI6O,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAAC;YAC/H1O,GAAG,EAAE,CAAC2O,qBAAqB,GAAG,IAAI,CAACvf,KAAK,CAACoQ,MAAM,CAACQ,GAAG,MAAM,IAAI,IAAI2O,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG;UAC9H,CAAC;UACDK,cAAc,EAAE,IAAI,CAACjV,KAAK,CAAC3B,YAAY;UACvC6W,oBAAoB,EAAE,IAAI,CAACV,uBAAuB;UAClDvW,MAAM,EAAE,IAAI,CAAC5I,KAAK,CAAC4I;QACrB,CAAC,CAAC;QACF,IAAI,CAACkX,qBAAqB,CAAC,CAAC;MAC9B;IACF,CAAC,EAAE;MACDliB,GAAG,EAAE,uBAAuB;MAC5BsB,KAAK,EAAE,SAAS4gB,qBAAqBA,CAAA,EAAG;QACtC,IAAIC,YAAY,GAAG,IAAI,CAAC/f,KAAK;UAC3BqM,QAAQ,GAAG0T,YAAY,CAAC1T,QAAQ;UAChCzC,IAAI,GAAGmW,YAAY,CAACnW,IAAI;UACxBxB,MAAM,GAAG2X,YAAY,CAAC3X,MAAM;UAC5BQ,MAAM,GAAGmX,YAAY,CAACnX,MAAM;QAC9B,IAAIoX,WAAW,GAAGjb,eAAe,CAACsH,QAAQ,EAAE5H,OAAO,CAAC;QACpD;QACA,IAAI,CAACub,WAAW,EAAE;UAChB;QACF;QACA,IAAIC,YAAY,GAAGD,WAAW,CAAChgB,KAAK,CAACigB,YAAY;;QAEjD;QACA,IAAI,OAAOA,YAAY,KAAK,QAAQ,IAAIA,YAAY,GAAG,CAAC,IAAIA,YAAY,GAAG,IAAI,CAACtV,KAAK,CAAC3B,YAAY,CAACtL,MAAM,GAAG,CAAC,EAAE;UAC7G;QACF;QACA,IAAImN,WAAW,GAAG,IAAI,CAACF,KAAK,CAAC3B,YAAY,CAACiX,YAAY,CAAC,IAAI,IAAI,CAACtV,KAAK,CAAC3B,YAAY,CAACiX,YAAY,CAAC,CAAC/gB,KAAK;QACtG,IAAI2M,aAAa,GAAGnB,iBAAiB,CAAC,IAAI,CAACC,KAAK,EAAEf,IAAI,EAAEqW,YAAY,EAAEpV,WAAW,CAAC;QAClF,IAAIqV,oBAAoB,GAAG,IAAI,CAACvV,KAAK,CAAC3B,YAAY,CAACiX,YAAY,CAAC,CAAC3W,UAAU;QAC3E,IAAI6W,kBAAkB,GAAG,CAAC,IAAI,CAACxV,KAAK,CAACmG,MAAM,CAACF,GAAG,GAAGxI,MAAM,IAAI,CAAC;QAC7D,IAAIgY,YAAY,GAAGxX,MAAM,KAAK,YAAY;QAC1C,IAAIkD,gBAAgB,GAAGsU,YAAY,GAAG;UACpC9X,CAAC,EAAE4X,oBAAoB;UACvB3X,CAAC,EAAE4X;QACL,CAAC,GAAG;UACF5X,CAAC,EAAE2X,oBAAoB;UACvB5X,CAAC,EAAE6X;QACL,CAAC;;QAED;QACA;QACA;QACA,IAAIE,kBAAkB,GAAG,IAAI,CAAC1V,KAAK,CAACsK,uBAAuB,CAAC9L,IAAI,CAAC,UAAUmX,MAAM,EAAE;UACjF,IAAItT,IAAI,GAAGsT,MAAM,CAACtT,IAAI;UACtB,OAAOA,IAAI,CAACN,IAAI,CAACpK,IAAI,KAAK,SAAS;QACrC,CAAC,CAAC;QACF,IAAI+d,kBAAkB,EAAE;UACtBvU,gBAAgB,GAAG9I,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8I,gBAAgB,CAAC,EAAEuU,kBAAkB,CAACrgB,KAAK,CAACqc,MAAM,CAAC4D,YAAY,CAAC,CAACrJ,eAAe,CAAC;UACpI/K,aAAa,GAAGwU,kBAAkB,CAACrgB,KAAK,CAACqc,MAAM,CAAC4D,YAAY,CAAC,CAACtJ,cAAc;QAC9E;QACA,IAAIJ,SAAS,GAAG;UACdxK,kBAAkB,EAAEkU,YAAY;UAChC1Q,eAAe,EAAE,IAAI;UACrB1E,WAAW,EAAEA,WAAW;UACxBgB,aAAa,EAAEA,aAAa;UAC5BC,gBAAgB,EAAEA;QACpB,CAAC;QACD,IAAI,CAAC4J,QAAQ,CAACa,SAAS,CAAC;QACxB,IAAI,CAACoI,YAAY,CAACqB,WAAW,CAAC;;QAE9B;QACA;QACA,IAAI,CAACP,oBAAoB,CAACc,QAAQ,CAACN,YAAY,CAAC;MAClD;IACF,CAAC,EAAE;MACDriB,GAAG,EAAE,yBAAyB;MAC9BsB,KAAK,EAAE,SAASshB,uBAAuBA,CAACC,SAAS,EAAE/L,SAAS,EAAE;QAC5D,IAAI,CAAC,IAAI,CAAC1U,KAAK,CAACua,kBAAkB,EAAE;UAClC,OAAO,IAAI;QACb;QACA,IAAI,IAAI,CAAC5P,KAAK,CAAC3B,YAAY,KAAK0L,SAAS,CAAC1L,YAAY,EAAE;UACtD,IAAI,CAACyW,oBAAoB,CAACC,UAAU,CAAC;YACnCE,cAAc,EAAE,IAAI,CAACjV,KAAK,CAAC3B;UAC7B,CAAC,CAAC;QACJ;QACA,IAAI,IAAI,CAAChJ,KAAK,CAAC4I,MAAM,KAAK6X,SAAS,CAAC7X,MAAM,EAAE;UAC1C,IAAI,CAAC6W,oBAAoB,CAACC,UAAU,CAAC;YACnC9W,MAAM,EAAE,IAAI,CAAC5I,KAAK,CAAC4I;UACrB,CAAC,CAAC;QACJ;QACA,IAAI,IAAI,CAAC5I,KAAK,CAACoQ,MAAM,KAAKqQ,SAAS,CAACrQ,MAAM,EAAE;UAC1C,IAAIsQ,sBAAsB,EAAEC,sBAAsB;UAClD,IAAI,CAAClB,oBAAoB,CAACC,UAAU,CAAC;YACnC5O,MAAM,EAAE;cACNL,IAAI,EAAE,CAACiQ,sBAAsB,GAAG,IAAI,CAAC1gB,KAAK,CAACoQ,MAAM,CAACK,IAAI,MAAM,IAAI,IAAIiQ,sBAAsB,KAAK,KAAK,CAAC,GAAGA,sBAAsB,GAAG,CAAC;cAClI9P,GAAG,EAAE,CAAC+P,sBAAsB,GAAG,IAAI,CAAC3gB,KAAK,CAACoQ,MAAM,CAACQ,GAAG,MAAM,IAAI,IAAI+P,sBAAsB,KAAK,KAAK,CAAC,GAAGA,sBAAsB,GAAG;YACjI;UACF,CAAC,CAAC;QACJ;;QAEA;QACA,OAAO,IAAI;MACb;IACF,CAAC,EAAE;MACD/iB,GAAG,EAAE,oBAAoB;MACzBsB,KAAK,EAAE,SAAS0hB,kBAAkBA,CAACH,SAAS,EAAE;QAC5C;QACA,IAAI,CAACvb,eAAe,CAAC,CAACH,eAAe,CAAC0b,SAAS,CAACpU,QAAQ,EAAE5H,OAAO,CAAC,CAAC,EAAE,CAACM,eAAe,CAAC,IAAI,CAAC/E,KAAK,CAACqM,QAAQ,EAAE5H,OAAO,CAAC,CAAC,CAAC,EAAE;UACrH,IAAI,CAACqb,qBAAqB,CAAC,CAAC;QAC9B;MACF;IACF,CAAC,EAAE;MACDliB,GAAG,EAAE,sBAAsB;MAC3BsB,KAAK,EAAE,SAAS2hB,oBAAoBA,CAAA,EAAG;QACrC,IAAI,CAACC,cAAc,CAAC,CAAC;QACrB,IAAI,CAAChK,+BAA+B,CAACC,MAAM,CAAC,CAAC;MAC/C;IACF,CAAC,EAAE;MACDnZ,GAAG,EAAE,qBAAqB;MAC1BsB,KAAK,EAAE,SAASyZ,mBAAmBA,CAAA,EAAG;QACpC,IAAI6B,WAAW,GAAGzV,eAAe,CAAC,IAAI,CAAC/E,KAAK,CAACqM,QAAQ,EAAE5H,OAAO,CAAC;QAC/D,IAAI+V,WAAW,IAAI,OAAOA,WAAW,CAACxa,KAAK,CAAC+gB,MAAM,KAAK,SAAS,EAAE;UAChE,IAAIC,SAAS,GAAGxG,WAAW,CAACxa,KAAK,CAAC+gB,MAAM,GAAG,MAAM,GAAG,MAAM;UAC1D,OAAOjP,yBAAyB,CAACpS,OAAO,CAACshB,SAAS,CAAC,IAAI,CAAC,GAAGA,SAAS,GAAGpP,uBAAuB;QAChG;QACA,OAAOA,uBAAuB;MAChC;;MAEA;AACN;AACA;AACA;AACA;IACI,CAAC,EAAE;MACDhU,GAAG,EAAE,cAAc;MACnBsB,KAAK,EAAE,SAASkX,YAAYA,CAACc,KAAK,EAAE;QAClC,IAAI,CAAC,IAAI,CAACyI,SAAS,EAAE;UACnB,OAAO,IAAI;QACb;QACA,IAAIlX,OAAO,GAAG,IAAI,CAACkX,SAAS;QAC5B,IAAIsB,YAAY,GAAGxY,OAAO,CAACyY,qBAAqB,CAAC,CAAC;QAClD,IAAIC,eAAe,GAAG5b,SAAS,CAAC0b,YAAY,CAAC;QAC7C,IAAIviB,CAAC,GAAG;UACN6M,MAAM,EAAE2F,IAAI,CAACkQ,KAAK,CAAClK,KAAK,CAACmK,KAAK,GAAGF,eAAe,CAAC1Q,IAAI,CAAC;UACtDjF,MAAM,EAAE0F,IAAI,CAACkQ,KAAK,CAAClK,KAAK,CAACoK,KAAK,GAAGH,eAAe,CAACvQ,GAAG;QACtD,CAAC;QACD,IAAI/D,KAAK,GAAGoU,YAAY,CAAC9Y,KAAK,GAAGM,OAAO,CAACuI,WAAW,IAAI,CAAC;QACzD,IAAIrI,QAAQ,GAAG,IAAI,CAAC4Y,OAAO,CAAC7iB,CAAC,CAAC6M,MAAM,EAAE7M,CAAC,CAAC8M,MAAM,EAAEqB,KAAK,CAAC;QACtD,IAAI,CAAClE,QAAQ,EAAE;UACb,OAAO,IAAI;QACb;QACA,IAAI6Y,YAAY,GAAG,IAAI,CAAC7W,KAAK;UAC3BsF,QAAQ,GAAGuR,YAAY,CAACvR,QAAQ;UAChCE,QAAQ,GAAGqR,YAAY,CAACrR,QAAQ;QAClC,IAAIuI,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;QACjD,IAAI8I,WAAW,GAAGpW,cAAc,CAAC,IAAI,CAACV,KAAK,EAAE,IAAI,CAAC3K,KAAK,CAAC4J,IAAI,EAAE,IAAI,CAAC5J,KAAK,CAAC4I,MAAM,EAAED,QAAQ,CAAC;QAC1F,IAAI+P,gBAAgB,KAAK,MAAM,IAAIzI,QAAQ,IAAIE,QAAQ,EAAE;UACvD,IAAIuR,MAAM,GAAGjc,qBAAqB,CAACwK,QAAQ,CAAC,CAACpD,KAAK;UAClD,IAAI8U,MAAM,GAAGlc,qBAAqB,CAAC0K,QAAQ,CAAC,CAACtD,KAAK;UAClD,IAAI+U,MAAM,GAAGF,MAAM,IAAIA,MAAM,CAACG,MAAM,GAAGH,MAAM,CAACG,MAAM,CAACnjB,CAAC,CAAC6M,MAAM,CAAC,GAAG,IAAI;UACrE,IAAIuW,MAAM,GAAGH,MAAM,IAAIA,MAAM,CAACE,MAAM,GAAGF,MAAM,CAACE,MAAM,CAACnjB,CAAC,CAAC8M,MAAM,CAAC,GAAG,IAAI;UACrE,OAAOxI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEtE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC7CkjB,MAAM,EAAEA,MAAM;YACdE,MAAM,EAAEA;UACV,CAAC,EAAEL,WAAW,CAAC;QACjB;QACA,IAAIA,WAAW,EAAE;UACf,OAAOze,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEtE,CAAC,CAAC,EAAE+iB,WAAW,CAAC;QACzD;QACA,OAAO,IAAI;MACb;IACF,CAAC,EAAE;MACD7jB,GAAG,EAAE,SAAS;MACdsB,KAAK,EAAE,SAASqiB,OAAOA,CAACjZ,CAAC,EAAEC,CAAC,EAAE;QAC5B,IAAIsE,KAAK,GAAGpP,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKgN,SAAS,GAAGhN,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;QACjF,IAAImL,MAAM,GAAG,IAAI,CAAC5I,KAAK,CAAC4I,MAAM;QAC9B,IAAImZ,OAAO,GAAGzZ,CAAC,GAAGuE,KAAK;UACrBmV,OAAO,GAAGzZ,CAAC,GAAGsE,KAAK;QACrB,IAAIjE,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,EAAE;UACpD,IAAIkI,MAAM,GAAG,IAAI,CAACnG,KAAK,CAACmG,MAAM;UAC9B,IAAImR,SAAS,GAAGF,OAAO,IAAIjR,MAAM,CAACL,IAAI,IAAIsR,OAAO,IAAIjR,MAAM,CAACL,IAAI,GAAGK,MAAM,CAAC3I,KAAK,IAAI6Z,OAAO,IAAIlR,MAAM,CAACF,GAAG,IAAIoR,OAAO,IAAIlR,MAAM,CAACF,GAAG,GAAGE,MAAM,CAAC1I,MAAM;UACjJ,OAAO6Z,SAAS,GAAG;YACjB3Z,CAAC,EAAEyZ,OAAO;YACVxZ,CAAC,EAAEyZ;UACL,CAAC,GAAG,IAAI;QACV;QACA,IAAIE,aAAa,GAAG,IAAI,CAACvX,KAAK;UAC5B8O,YAAY,GAAGyI,aAAa,CAACzI,YAAY;UACzCD,aAAa,GAAG0I,aAAa,CAAC1I,aAAa;QAC7C,IAAIC,YAAY,IAAID,aAAa,EAAE;UACjC,IAAIG,SAAS,GAAGlU,qBAAqB,CAACgU,YAAY,CAAC;UACnD,OAAOxS,eAAe,CAAC;YACrBqB,CAAC,EAAEyZ,OAAO;YACVxZ,CAAC,EAAEyZ;UACL,CAAC,EAAErI,SAAS,CAAC;QACf;QACA,OAAO,IAAI;MACb;IACF,CAAC,EAAE;MACD/b,GAAG,EAAE,sBAAsB;MAC3BsB,KAAK,EAAE,SAASijB,oBAAoBA,CAAA,EAAG;QACrC,IAAI9V,QAAQ,GAAG,IAAI,CAACrM,KAAK,CAACqM,QAAQ;QAClC,IAAIqM,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;QACjD,IAAI6B,WAAW,GAAGzV,eAAe,CAACsH,QAAQ,EAAE5H,OAAO,CAAC;QACpD,IAAI2d,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI5H,WAAW,IAAI9B,gBAAgB,KAAK,MAAM,EAAE;UAC9C,IAAI8B,WAAW,CAACxa,KAAK,CAAC2c,OAAO,KAAK,OAAO,EAAE;YACzCyF,aAAa,GAAG;cACd9K,OAAO,EAAE,IAAI,CAAC+K;YAChB,CAAC;UACH,CAAC,MAAM;YACLD,aAAa,GAAG;cACd9L,YAAY,EAAE,IAAI,CAACgM,gBAAgB;cACnCzK,aAAa,EAAE,IAAI,CAAC0K,iBAAiB;cACrC/L,WAAW,EAAE,IAAI,CAACgM,eAAe;cACjCxL,YAAY,EAAE,IAAI,CAACyL,gBAAgB;cACnCC,WAAW,EAAE,IAAI,CAACC,eAAe;cACjCC,YAAY,EAAE,IAAI,CAACC,gBAAgB;cACnCC,UAAU,EAAE,IAAI,CAACC,cAAc;cAC/BhL,aAAa,EAAE,IAAI,CAACiL;YACtB,CAAC;UACH;QACF;;QAEA;QACA,IAAIC,WAAW,GAAG3b,kBAAkB,CAAC,IAAI,CAACtH,KAAK,EAAE,IAAI,CAACkjB,gBAAgB,CAAC;QACvE,OAAOlgB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEigB,WAAW,CAAC,EAAEb,aAAa,CAAC;MACrE;IACF,CAAC,EAAE;MACDxkB,GAAG,EAAE,aAAa;MAClBsB,KAAK,EAAE,SAASsgB,WAAWA,CAAA,EAAG;QAC5BpY,WAAW,CAAC+b,EAAE,CAAC9b,UAAU,EAAE,IAAI,CAAC+b,sBAAsB,CAAC;MACzD;IACF,CAAC,EAAE;MACDxlB,GAAG,EAAE,gBAAgB;MACrBsB,KAAK,EAAE,SAAS4hB,cAAcA,CAAA,EAAG;QAC/B1Z,WAAW,CAAC0Z,cAAc,CAACzZ,UAAU,EAAE,IAAI,CAAC+b,sBAAsB,CAAC;MACrE;IACF,CAAC,EAAE;MACDxlB,GAAG,EAAE,kBAAkB;MACvBsB,KAAK,EAAE,SAASgd,gBAAgBA,CAAClP,IAAI,EAAEuG,WAAW,EAAEgB,UAAU,EAAE;QAC9D,IAAIU,uBAAuB,GAAG,IAAI,CAACtK,KAAK,CAACsK,uBAAuB;QAChE,KAAK,IAAIzX,CAAC,GAAG,CAAC,EAAEkF,GAAG,GAAGuS,uBAAuB,CAACvX,MAAM,EAAEF,CAAC,GAAGkF,GAAG,EAAElF,CAAC,EAAE,EAAE;UAClE,IAAI0L,KAAK,GAAG+L,uBAAuB,CAACzX,CAAC,CAAC;UACtC,IAAI0L,KAAK,CAAC8D,IAAI,KAAKA,IAAI,IAAI9D,KAAK,CAAClJ,KAAK,CAACpC,GAAG,KAAKoP,IAAI,CAACpP,GAAG,IAAI2V,WAAW,KAAKvO,cAAc,CAACkE,KAAK,CAAC8D,IAAI,CAACN,IAAI,CAAC,IAAI6H,UAAU,KAAKrL,KAAK,CAACqL,UAAU,EAAE;YAC7I,OAAOrL,KAAK;UACd;QACF;QACA,OAAO,IAAI;MACb;IACF,CAAC,EAAE;MACDtL,GAAG,EAAE,gBAAgB;MACrBsB,KAAK,EAAE,SAASmkB,cAAcA,CAAA,EAAG;QAC/B,IAAIrI,UAAU,GAAG,IAAI,CAACA,UAAU;QAChC,IAAIsI,kBAAkB,GAAG,IAAI,CAAC3Y,KAAK,CAACmG,MAAM;UACxCL,IAAI,GAAG6S,kBAAkB,CAAC7S,IAAI;UAC9BG,GAAG,GAAG0S,kBAAkB,CAAC1S,GAAG;UAC5BxI,MAAM,GAAGkb,kBAAkB,CAAClb,MAAM;UAClCD,KAAK,GAAGmb,kBAAkB,CAACnb,KAAK;QAClC,OAAO,aAAazE,KAAK,CAACoV,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAapV,KAAK,CAACoV,aAAa,CAAC,UAAU,EAAE;UACjGvI,EAAE,EAAEyK;QACN,CAAC,EAAE,aAAatX,KAAK,CAACoV,aAAa,CAAC,MAAM,EAAE;UAC1CxQ,CAAC,EAAEmI,IAAI;UACPlI,CAAC,EAAEqI,GAAG;UACNxI,MAAM,EAAEA,MAAM;UACdD,KAAK,EAAEA;QACT,CAAC,CAAC,CAAC,CAAC;MACN;IACF,CAAC,EAAE;MACDvK,GAAG,EAAE,YAAY;MACjBsB,KAAK,EAAE,SAASqkB,UAAUA,CAAA,EAAG;QAC3B,IAAItT,QAAQ,GAAG,IAAI,CAACtF,KAAK,CAACsF,QAAQ;QAClC,OAAOA,QAAQ,GAAG7S,MAAM,CAACgO,OAAO,CAAC6E,QAAQ,CAAC,CAAC/F,MAAM,CAAC,UAAUsZ,GAAG,EAAEC,MAAM,EAAE;UACvE,IAAIC,MAAM,GAAG1lB,cAAc,CAACylB,MAAM,EAAE,CAAC,CAAC;YACpC1W,MAAM,GAAG2W,MAAM,CAAC,CAAC,CAAC;YAClBC,SAAS,GAAGD,MAAM,CAAC,CAAC,CAAC;UACvB,OAAO1gB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwgB,GAAG,CAAC,EAAE,CAAC,CAAC,EAAEtgB,eAAe,CAAC,CAAC,CAAC,EAAE6J,MAAM,EAAE4W,SAAS,CAAC9W,KAAK,CAAC,CAAC;QAChG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI;MACf;IACF,CAAC,EAAE;MACDjP,GAAG,EAAE,YAAY;MACjBsB,KAAK,EAAE,SAAS0kB,UAAUA,CAAA,EAAG;QAC3B,IAAIzT,QAAQ,GAAG,IAAI,CAACxF,KAAK,CAACwF,QAAQ;QAClC,OAAOA,QAAQ,GAAG/S,MAAM,CAACgO,OAAO,CAAC+E,QAAQ,CAAC,CAACjG,MAAM,CAAC,UAAUsZ,GAAG,EAAEK,MAAM,EAAE;UACvE,IAAIC,MAAM,GAAG9lB,cAAc,CAAC6lB,MAAM,EAAE,CAAC,CAAC;YACpC9W,MAAM,GAAG+W,MAAM,CAAC,CAAC,CAAC;YAClBH,SAAS,GAAGG,MAAM,CAAC,CAAC,CAAC;UACvB,OAAO9gB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwgB,GAAG,CAAC,EAAE,CAAC,CAAC,EAAEtgB,eAAe,CAAC,CAAC,CAAC,EAAE6J,MAAM,EAAE4W,SAAS,CAAC9W,KAAK,CAAC,CAAC;QAChG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI;MACf;IACF,CAAC,EAAE;MACDjP,GAAG,EAAE,mBAAmB;MACxBsB,KAAK,EAAE,SAAS6kB,iBAAiBA,CAAChX,MAAM,EAAE;QACxC,IAAIiX,oBAAoB;QACxB,OAAO,CAACA,oBAAoB,GAAG,IAAI,CAACrZ,KAAK,CAACsF,QAAQ,MAAM,IAAI,IAAI+T,oBAAoB,KAAK,KAAK,CAAC,IAAI,CAACA,oBAAoB,GAAGA,oBAAoB,CAACjX,MAAM,CAAC,MAAM,IAAI,IAAIiX,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACnX,KAAK;MAC5O;IACF,CAAC,EAAE;MACDjP,GAAG,EAAE,mBAAmB;MACxBsB,KAAK,EAAE,SAAS+kB,iBAAiBA,CAAClX,MAAM,EAAE;QACxC,IAAImX,oBAAoB;QACxB,OAAO,CAACA,oBAAoB,GAAG,IAAI,CAACvZ,KAAK,CAACwF,QAAQ,MAAM,IAAI,IAAI+T,oBAAoB,KAAK,KAAK,CAAC,IAAI,CAACA,oBAAoB,GAAGA,oBAAoB,CAACnX,MAAM,CAAC,MAAM,IAAI,IAAImX,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACrX,KAAK;MAC5O;IACF,CAAC,EAAE;MACDjP,GAAG,EAAE,aAAa;MAClBsB,KAAK,EAAE,SAASke,WAAWA,CAAC+G,OAAO,EAAE;QACnC,IAAIC,aAAa,GAAG,IAAI,CAACzZ,KAAK;UAC5BsK,uBAAuB,GAAGmP,aAAa,CAACnP,uBAAuB;UAC/DyB,UAAU,GAAG0N,aAAa,CAAC1N,UAAU;QACvC,IAAIzB,uBAAuB,IAAIA,uBAAuB,CAACvX,MAAM,EAAE;UAC7D,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEkF,GAAG,GAAGuS,uBAAuB,CAACvX,MAAM,EAAEF,CAAC,GAAGkF,GAAG,EAAElF,CAAC,EAAE,EAAE;YAClE,IAAIsf,aAAa,GAAG7H,uBAAuB,CAACzX,CAAC,CAAC;YAC9C;YACA,IAAIwC,KAAK,GAAG8c,aAAa,CAAC9c,KAAK;cAC7BgN,IAAI,GAAG8P,aAAa,CAAC9P,IAAI;YAC3B,IAAI6F,SAAS,GAAG7F,IAAI,CAACN,IAAI,CAACC,YAAY,KAAKlC,SAAS,GAAGzH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgK,IAAI,CAACN,IAAI,CAACC,YAAY,CAAC,EAAEK,IAAI,CAAChN,KAAK,CAAC,GAAGgN,IAAI,CAAChN,KAAK;YACxI,IAAIqkB,eAAe,GAAGrf,cAAc,CAACgI,IAAI,CAACN,IAAI,CAAC;YAC/C,IAAI2X,eAAe,KAAK,KAAK,EAAE;cAC7B,IAAIC,aAAa,GAAG,CAACtkB,KAAK,CAAC4J,IAAI,IAAI,EAAE,EAAET,IAAI,CAAC,UAAUD,KAAK,EAAE;gBAC3D,OAAOtE,aAAa,CAACuf,OAAO,EAAEjb,KAAK,CAAC;cACtC,CAAC,CAAC;cACF,IAAIob,aAAa,EAAE;gBACjB,OAAO;kBACLxH,aAAa,EAAEA,aAAa;kBAC5B7R,OAAO,EAAEqZ;gBACX,CAAC;cACH;YACF,CAAC,MAAM,IAAID,eAAe,KAAK,WAAW,EAAE;cAC1C,IAAIE,cAAc,GAAG,CAACvkB,KAAK,CAAC4J,IAAI,IAAI,EAAE,EAAET,IAAI,CAAC,UAAUD,KAAK,EAAE;gBAC5D,OAAOjC,eAAe,CAACkd,OAAO,EAAEjb,KAAK,CAAC;cACxC,CAAC,CAAC;cACF,IAAIqb,cAAc,EAAE;gBAClB,OAAO;kBACLzH,aAAa,EAAEA,aAAa;kBAC5B7R,OAAO,EAAEsZ;gBACX,CAAC;cACH;YACF,CAAC,MAAM,IAAI7c,QAAQ,CAACoV,aAAa,EAAEpG,UAAU,CAAC,IAAI/O,KAAK,CAACmV,aAAa,EAAEpG,UAAU,CAAC,IAAI9O,SAAS,CAACkV,aAAa,EAAEpG,UAAU,CAAC,EAAE;cAC1H,IAAIzN,WAAW,GAAGxB,6BAA6B,CAAC;gBAC9CqV,aAAa,EAAEA,aAAa;gBAC5B0H,iBAAiB,EAAE9N,UAAU;gBAC7BrM,QAAQ,EAAEwI,SAAS,CAACjJ;cACtB,CAAC,CAAC;cACF,IAAI2K,UAAU,GAAG1B,SAAS,CAAC5J,WAAW,KAAKwB,SAAS,GAAGxB,WAAW,GAAG4J,SAAS,CAAC5J,WAAW;cAC1F,OAAO;gBACL6T,aAAa,EAAE9Z,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8Z,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;kBACjEvI,UAAU,EAAEA;gBACd,CAAC,CAAC;gBACFtJ,OAAO,EAAErD,SAAS,CAACkV,aAAa,EAAEpG,UAAU,CAAC,GAAG7D,SAAS,CAACjJ,IAAI,CAACX,WAAW,CAAC,GAAG6T,aAAa,CAAC9c,KAAK,CAAC4J,IAAI,CAACX,WAAW;cACpH,CAAC;YACH;UACF;QACF;QACA,OAAO,IAAI;MACb;IACF,CAAC,EAAE;MACDrL,GAAG,EAAE,QAAQ;MACbsB,KAAK,EAAE,SAASulB,MAAMA,CAAA,EAAG;QACvB,IAAIC,MAAM,GAAG,IAAI;QACjB,IAAI,CAACrf,mBAAmB,CAAC,IAAI,CAAC,EAAE;UAC9B,OAAO,IAAI;QACb;QACA,IAAIsf,YAAY,GAAG,IAAI,CAAC3kB,KAAK;UAC3BqM,QAAQ,GAAGsY,YAAY,CAACtY,QAAQ;UAChC6M,SAAS,GAAGyL,YAAY,CAACzL,SAAS;UAClC/Q,KAAK,GAAGwc,YAAY,CAACxc,KAAK;UAC1BC,MAAM,GAAGuc,YAAY,CAACvc,MAAM;UAC5Bwc,KAAK,GAAGD,YAAY,CAACC,KAAK;UAC1BC,OAAO,GAAGF,YAAY,CAACE,OAAO;UAC9BC,KAAK,GAAGH,YAAY,CAACG,KAAK;UAC1BC,IAAI,GAAGJ,YAAY,CAACI,IAAI;UACxBC,MAAM,GAAG3lB,wBAAwB,CAACslB,YAAY,EAAE/nB,UAAU,CAAC;QAC7D,IAAIqoB,KAAK,GAAGpgB,WAAW,CAACmgB,MAAM,EAAE,KAAK,CAAC;;QAEtC;QACA,IAAIH,OAAO,EAAE;UACX,OAAO,aAAanhB,KAAK,CAACoV,aAAa,CAAChR,0BAA0B,EAAE;YAClE6C,KAAK,EAAE,IAAI,CAACA,KAAK;YACjBxC,KAAK,EAAE,IAAI,CAACnI,KAAK,CAACmI,KAAK;YACvBC,MAAM,EAAE,IAAI,CAACpI,KAAK,CAACoI,MAAM;YACzB4S,UAAU,EAAE,IAAI,CAACA;UACnB,CAAC,EAAE,aAAatX,KAAK,CAACoV,aAAa,CAACvU,OAAO,EAAEpH,QAAQ,CAAC,CAAC,CAAC,EAAE8nB,KAAK,EAAE;YAC/D9c,KAAK,EAAEA,KAAK;YACZC,MAAM,EAAEA,MAAM;YACd0c,KAAK,EAAEA,KAAK;YACZC,IAAI,EAAEA;UACR,CAAC,CAAC,EAAE,IAAI,CAAC1B,cAAc,CAAC,CAAC,EAAEje,aAAa,CAACiH,QAAQ,EAAE,IAAI,CAAC6Y,SAAS,CAAC,CAAC,CAAC;QACtE;QACA,IAAI,IAAI,CAACllB,KAAK,CAACua,kBAAkB,EAAE;UACjC,IAAI4K,oBAAoB,EAAEC,gBAAgB;UAC1C;UACAH,KAAK,CAACI,QAAQ,GAAG,CAACF,oBAAoB,GAAG,IAAI,CAACnlB,KAAK,CAACqlB,QAAQ,MAAM,IAAI,IAAIF,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAG,CAAC;UACpI;UACAF,KAAK,CAACK,IAAI,GAAG,CAACF,gBAAgB,GAAG,IAAI,CAACplB,KAAK,CAACslB,IAAI,MAAM,IAAI,IAAIF,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG,aAAa;UAC5HH,KAAK,CAACM,SAAS,GAAG,UAAU7mB,CAAC,EAAE;YAC7BgmB,MAAM,CAACjF,oBAAoB,CAAC+F,aAAa,CAAC9mB,CAAC,CAAC;YAC5C;YACA;UACF,CAAC;UACDumB,KAAK,CAACQ,OAAO,GAAG,YAAY;YAC1Bf,MAAM,CAACjF,oBAAoB,CAACiG,KAAK,CAAC,CAAC;YACnC;YACA;UACF,CAAC;QACH;QACA,IAAIC,MAAM,GAAG,IAAI,CAACxD,oBAAoB,CAAC,CAAC;QACxC,OAAO,aAAaze,KAAK,CAACoV,aAAa,CAAChR,0BAA0B,EAAE;UAClE6C,KAAK,EAAE,IAAI,CAACA,KAAK;UACjBxC,KAAK,EAAE,IAAI,CAACnI,KAAK,CAACmI,KAAK;UACvBC,MAAM,EAAE,IAAI,CAACpI,KAAK,CAACoI,MAAM;UACzB4S,UAAU,EAAE,IAAI,CAACA;QACnB,CAAC,EAAE,aAAatX,KAAK,CAACoV,aAAa,CAAC,KAAK,EAAE3b,QAAQ,CAAC;UAClD+b,SAAS,EAAE7U,IAAI,CAAC,kBAAkB,EAAE6U,SAAS,CAAC;UAC9C0L,KAAK,EAAE5hB,aAAa,CAAC;YACnBoR,QAAQ,EAAE,UAAU;YACpBwR,MAAM,EAAE,SAAS;YACjBzd,KAAK,EAAEA,KAAK;YACZC,MAAM,EAAEA;UACV,CAAC,EAAEwc,KAAK;QACV,CAAC,EAAEe,MAAM,EAAE;UACTE,GAAG,EAAE,SAASA,GAAGA,CAACC,IAAI,EAAE;YACtBpB,MAAM,CAAC/E,SAAS,GAAGmG,IAAI;UACzB;QACF,CAAC,CAAC,EAAE,aAAapiB,KAAK,CAACoV,aAAa,CAACvU,OAAO,EAAEpH,QAAQ,CAAC,CAAC,CAAC,EAAE8nB,KAAK,EAAE;UAChE9c,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA,MAAM;UACd0c,KAAK,EAAEA,KAAK;UACZC,IAAI,EAAEA,IAAI;UACVH,KAAK,EAAE1c;QACT,CAAC,CAAC,EAAE,IAAI,CAACmb,cAAc,CAAC,CAAC,EAAEje,aAAa,CAACiH,QAAQ,EAAE,IAAI,CAAC6Y,SAAS,CAAC,CAAC,EAAE,IAAI,CAACa,YAAY,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAAC;MAClH;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAACriB,SAAS,CAAC;EACZT,eAAe,CAACgS,uBAAuB,EAAE,aAAa,EAAEzD,SAAS,CAAC;EAClE;EACAvO,eAAe,CAACgS,uBAAuB,EAAE,cAAc,EAAElS,aAAa,CAAC;IACrE4F,MAAM,EAAE,YAAY;IACpB0D,WAAW,EAAE,MAAM;IACnBiG,cAAc,EAAE,KAAK;IACrBD,MAAM,EAAE,CAAC;IACTlC,MAAM,EAAE;MACNQ,GAAG,EAAE,CAAC;MACNF,KAAK,EAAE,CAAC;MACRG,MAAM,EAAE,CAAC;MACTJ,IAAI,EAAE;IACR,CAAC;IACDkE,iBAAiB,EAAE,KAAK;IACxBoB,UAAU,EAAE;EACd,CAAC,EAAEpJ,YAAY,CAAC,CAAC;EACjBzJ,eAAe,CAACgS,uBAAuB,EAAE,0BAA0B,EAAE,UAAU+Q,SAAS,EAAEvR,SAAS,EAAE;IACnG,IAAIxJ,OAAO,GAAG+a,SAAS,CAAC/a,OAAO;MAC7BtB,IAAI,GAAGqc,SAAS,CAACrc,IAAI;MACrByC,QAAQ,GAAG4Z,SAAS,CAAC5Z,QAAQ;MAC7BlE,KAAK,GAAG8d,SAAS,CAAC9d,KAAK;MACvBC,MAAM,GAAG6d,SAAS,CAAC7d,MAAM;MACzBQ,MAAM,GAAGqd,SAAS,CAACrd,MAAM;MACzB0D,WAAW,GAAG2Z,SAAS,CAAC3Z,WAAW;MACnC8D,MAAM,GAAG6V,SAAS,CAAC7V,MAAM;IAC3B,IAAIrG,cAAc,GAAG2K,SAAS,CAAC3K,cAAc;MAC3CC,YAAY,GAAG0K,SAAS,CAAC1K,YAAY;IACvC,IAAI0K,SAAS,CAACtC,QAAQ,KAAK3H,SAAS,EAAE;MACpC,IAAIyb,YAAY,GAAGhX,kBAAkB,CAAC+W,SAAS,CAAC;MAChD,OAAOjjB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkjB,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;QACtE9T,QAAQ,EAAE;MACZ,CAAC,EAAEoC,yCAAyC,CAACxR,aAAa,CAACA,aAAa,CAAC;QACvEhD,KAAK,EAAEimB;MACT,CAAC,EAAEC,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;QACpB9T,QAAQ,EAAE;MACZ,CAAC,CAAC,EAAEsC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACnByR,WAAW,EAAEjb,OAAO;QACpBkb,QAAQ,EAAExc,IAAI;QACdyc,SAAS,EAAEle,KAAK;QAChBme,UAAU,EAAEle,MAAM;QAClBme,UAAU,EAAE3d,MAAM;QAClB4d,eAAe,EAAEla,WAAW;QAC5Bma,UAAU,EAAErW,MAAM;QAClBsW,YAAY,EAAEra;MAChB,CAAC,CAAC;IACJ;IACA,IAAInB,OAAO,KAAKwJ,SAAS,CAACyR,WAAW,IAAIvc,IAAI,KAAK8K,SAAS,CAAC0R,QAAQ,IAAIje,KAAK,KAAKuM,SAAS,CAAC2R,SAAS,IAAIje,MAAM,KAAKsM,SAAS,CAAC4R,UAAU,IAAI1d,MAAM,KAAK8L,SAAS,CAAC6R,UAAU,IAAIja,WAAW,KAAKoI,SAAS,CAAC8R,eAAe,IAAI,CAACrf,YAAY,CAACiJ,MAAM,EAAEsE,SAAS,CAAC+R,UAAU,CAAC,EAAE;MACvQ,IAAIE,aAAa,GAAGzX,kBAAkB,CAAC+W,SAAS,CAAC;;MAEjD;MACA,IAAIW,iBAAiB,GAAG;QACtB;QACA;QACArb,MAAM,EAAEmJ,SAAS,CAACnJ,MAAM;QACxBC,MAAM,EAAEkJ,SAAS,CAAClJ,MAAM;QACxB;QACA;QACA+D,eAAe,EAAEmF,SAAS,CAACnF;MAC7B,CAAC;MACD,IAAIsX,cAAc,GAAG7jB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqI,cAAc,CAACqJ,SAAS,EAAE9K,IAAI,EAAEhB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACjGwJ,QAAQ,EAAEsC,SAAS,CAACtC,QAAQ,GAAG;MACjC,CAAC,CAAC;MACF,IAAI0U,QAAQ,GAAG9jB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2jB,aAAa,CAAC,EAAEC,iBAAiB,CAAC,EAAEC,cAAc,CAAC;MAChH,OAAO7jB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8jB,QAAQ,CAAC,EAAEtS,yCAAyC,CAACxR,aAAa,CAAC;QACtHhD,KAAK,EAAEimB;MACT,CAAC,EAAEa,QAAQ,CAAC,EAAEpS,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7ByR,WAAW,EAAEjb,OAAO;QACpBkb,QAAQ,EAAExc,IAAI;QACdyc,SAAS,EAAEle,KAAK;QAChBme,UAAU,EAAEle,MAAM;QAClBme,UAAU,EAAE3d,MAAM;QAClB4d,eAAe,EAAEla,WAAW;QAC5Bma,UAAU,EAAErW,MAAM;QAClBsW,YAAY,EAAEra;MAChB,CAAC,CAAC;IACJ;IACA,IAAI,CAACnH,eAAe,CAACmH,QAAQ,EAAEqI,SAAS,CAACgS,YAAY,CAAC,EAAE;MACtD,IAAIK,qBAAqB,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,aAAa;MAC7E;MACA,IAAIC,KAAK,GAAGpiB,eAAe,CAACsH,QAAQ,EAAE/G,KAAK,CAAC;MAC5C,IAAI+J,UAAU,GAAG8X,KAAK,GAAG,CAACJ,qBAAqB,GAAG,CAACC,YAAY,GAAGG,KAAK,CAACnnB,KAAK,MAAM,IAAI,IAAIgnB,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC3X,UAAU,MAAM,IAAI,IAAI0X,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGhd,cAAc,GAAGA,cAAc;MAC7P,IAAIuF,QAAQ,GAAG6X,KAAK,GAAG,CAACF,qBAAqB,GAAG,CAACC,aAAa,GAAGC,KAAK,CAACnnB,KAAK,MAAM,IAAI,IAAIknB,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC5X,QAAQ,MAAM,IAAI,IAAI2X,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGjd,YAAY,GAAGA,YAAY;MACxP,IAAIod,2BAA2B,GAAG/X,UAAU,KAAKtF,cAAc,IAAIuF,QAAQ,KAAKtF,YAAY;;MAE5F;MACA,IAAIqd,aAAa,GAAG,CAACtjB,KAAK,CAAC6F,IAAI,CAAC;MAChC,IAAI0d,WAAW,GAAGD,aAAa,IAAI,CAACD,2BAA2B,GAAG1S,SAAS,CAACtC,QAAQ,GAAGsC,SAAS,CAACtC,QAAQ,GAAG,CAAC;MAC7G,OAAOpP,aAAa,CAACA,aAAa,CAAC;QACjCoP,QAAQ,EAAEkV;MACZ,CAAC,EAAE9S,yCAAyC,CAACxR,aAAa,CAACA,aAAa,CAAC;QACvEhD,KAAK,EAAEimB;MACT,CAAC,EAAEvR,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;QACjBtC,QAAQ,EAAEkV,WAAW;QACrBvd,cAAc,EAAEsF,UAAU;QAC1BrF,YAAY,EAAEsF;MAChB,CAAC,CAAC,EAAEoF,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACnBgS,YAAY,EAAEra,QAAQ;QACtBtC,cAAc,EAAEsF,UAAU;QAC1BrF,YAAY,EAAEsF;MAChB,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACFpM,eAAe,CAACgS,uBAAuB,EAAE,iBAAiB,EAAE,UAAUqS,MAAM,EAAEvnB,KAAK,EAAEpC,GAAG,EAAE;IACxF,IAAI4pB,GAAG;IACP,IAAK,aAAa3jB,cAAc,CAAC0jB,MAAM,CAAC,EAAE;MACxCC,GAAG,GAAG,aAAa5jB,YAAY,CAAC2jB,MAAM,EAAEvnB,KAAK,CAAC;IAChD,CAAC,MAAM,IAAIgE,UAAU,CAACujB,MAAM,CAAC,EAAE;MAC7BC,GAAG,GAAGD,MAAM,CAACvnB,KAAK,CAAC;IACrB,CAAC,MAAM;MACLwnB,GAAG,GAAG,aAAa9jB,KAAK,CAACoV,aAAa,CAACnU,GAAG,EAAE3E,KAAK,CAAC;IACpD;IACA,OAAO,aAAa0D,KAAK,CAACoV,aAAa,CAACtU,KAAK,EAAE;MAC7C0U,SAAS,EAAE,qBAAqB;MAChCtb,GAAG,EAAEA;IACP,CAAC,EAAE4pB,GAAG,CAAC;EACT,CAAC,CAAC;EACF,IAAIC,gBAAgB,GAAG,aAAa3jB,UAAU,CAAC,SAAS2jB,gBAAgBA,CAACznB,KAAK,EAAE6lB,GAAG,EAAE;IACnF,OAAO,aAAaniB,KAAK,CAACoV,aAAa,CAAC5D,uBAAuB,EAAE/X,QAAQ,CAAC,CAAC,CAAC,EAAE6C,KAAK,EAAE;MACnF6lB,GAAG,EAAEA;IACP,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF4B,gBAAgB,CAAClU,WAAW,GAAG2B,uBAAuB,CAAC3B,WAAW;EAClE,OAAOkU,gBAAgB;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}