{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n/**\n * @fileOverview Reference Line\n */\nimport React from 'react';\nimport isFunction from 'lodash/isFunction';\nimport some from 'lodash/some';\nimport clsx from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Label } from '../component/Label';\nimport { ifOverflowMatches } from '../util/IfOverflowMatches';\nimport { isNumOrStr } from '../util/DataUtils';\nimport { createLabeledScales, rectWithCoords } from '../util/CartesianUtils';\nimport { warn } from '../util/LogUtils';\nimport { filterProps } from '../util/ReactUtils';\nimport { useClipPathId, useViewBox, useXAxisOrThrow, useYAxisOrThrow } from '../context/chartLayoutContext';\n\n/**\n * This excludes `viewBox` prop from svg for two reasons:\n * 1. The components wants viewBox of object type, and svg wants string\n *    - so there's a conflict, and the component will throw if it gets string\n * 2. Internally the component calls `filterProps` which filters the viewBox away anyway\n */\n\nvar renderLine = function renderLine(option, props) {\n  var line;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    line = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (isFunction(option)) {\n    line = option(props);\n  } else {\n    line = /*#__PURE__*/React.createElement(\"line\", _extends({}, props, {\n      className: \"recharts-reference-line-line\"\n    }));\n  }\n  return line;\n};\n// TODO: ScaleHelper\nexport var getEndPoints = function getEndPoints(scales, isFixedX, isFixedY, isSegment, viewBox, position, xAxisOrientation, yAxisOrientation, props) {\n  var x = viewBox.x,\n    y = viewBox.y,\n    width = viewBox.width,\n    height = viewBox.height;\n  if (isFixedY) {\n    var yCoord = props.y;\n    var coord = scales.y.apply(yCoord, {\n      position: position\n    });\n    if (ifOverflowMatches(props, 'discard') && !scales.y.isInRange(coord)) {\n      return null;\n    }\n    var points = [{\n      x: x + width,\n      y: coord\n    }, {\n      x: x,\n      y: coord\n    }];\n    return yAxisOrientation === 'left' ? points.reverse() : points;\n  }\n  if (isFixedX) {\n    var xCoord = props.x;\n    var _coord = scales.x.apply(xCoord, {\n      position: position\n    });\n    if (ifOverflowMatches(props, 'discard') && !scales.x.isInRange(_coord)) {\n      return null;\n    }\n    var _points = [{\n      x: _coord,\n      y: y + height\n    }, {\n      x: _coord,\n      y: y\n    }];\n    return xAxisOrientation === 'top' ? _points.reverse() : _points;\n  }\n  if (isSegment) {\n    var segment = props.segment;\n    var _points2 = segment.map(function (p) {\n      return scales.apply(p, {\n        position: position\n      });\n    });\n    if (ifOverflowMatches(props, 'discard') && some(_points2, function (p) {\n      return !scales.isInRange(p);\n    })) {\n      return null;\n    }\n    return _points2;\n  }\n  return null;\n};\nfunction ReferenceLineImpl(props) {\n  var fixedX = props.x,\n    fixedY = props.y,\n    segment = props.segment,\n    xAxisId = props.xAxisId,\n    yAxisId = props.yAxisId,\n    shape = props.shape,\n    className = props.className,\n    alwaysShow = props.alwaysShow;\n  var clipPathId = useClipPathId();\n  var xAxis = useXAxisOrThrow(xAxisId);\n  var yAxis = useYAxisOrThrow(yAxisId);\n  var viewBox = useViewBox();\n  if (!clipPathId || !viewBox) {\n    return null;\n  }\n  warn(alwaysShow === undefined, 'The alwaysShow prop is deprecated. Please use ifOverflow=\"extendDomain\" instead.');\n  var scales = createLabeledScales({\n    x: xAxis.scale,\n    y: yAxis.scale\n  });\n  var isX = isNumOrStr(fixedX);\n  var isY = isNumOrStr(fixedY);\n  var isSegment = segment && segment.length === 2;\n  var endPoints = getEndPoints(scales, isX, isY, isSegment, viewBox, props.position, xAxis.orientation, yAxis.orientation, props);\n  if (!endPoints) {\n    return null;\n  }\n  var _endPoints = _slicedToArray(endPoints, 2),\n    _endPoints$ = _endPoints[0],\n    x1 = _endPoints$.x,\n    y1 = _endPoints$.y,\n    _endPoints$2 = _endPoints[1],\n    x2 = _endPoints$2.x,\n    y2 = _endPoints$2.y;\n  var clipPath = ifOverflowMatches(props, 'hidden') ? \"url(#\".concat(clipPathId, \")\") : undefined;\n  var lineProps = _objectSpread(_objectSpread({\n    clipPath: clipPath\n  }, filterProps(props, true)), {}, {\n    x1: x1,\n    y1: y1,\n    x2: x2,\n    y2: y2\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: clsx('recharts-reference-line', className)\n  }, renderLine(shape, lineProps), Label.renderCallByParent(props, rectWithCoords({\n    x1: x1,\n    y1: y1,\n    x2: x2,\n    y2: y2\n  })));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function -- requires static defaultProps\nexport var ReferenceLine = /*#__PURE__*/function (_React$Component) {\n  function ReferenceLine() {\n    _classCallCheck(this, ReferenceLine);\n    return _callSuper(this, ReferenceLine, arguments);\n  }\n  _inherits(ReferenceLine, _React$Component);\n  return _createClass(ReferenceLine, [{\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(ReferenceLineImpl, this.props);\n    }\n  }]);\n}(React.Component);\n_defineProperty(ReferenceLine, \"displayName\", 'ReferenceLine');\n_defineProperty(ReferenceLine, \"defaultProps\", {\n  isFront: false,\n  ifOverflow: 'discard',\n  xAxisId: 0,\n  yAxisId: 0,\n  fill: 'none',\n  stroke: '#ccc',\n  fillOpacity: 1,\n  strokeWidth: 1,\n  position: 'middle'\n});", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "protoProps", "staticProps", "_callSuper", "t", "e", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "apply", "self", "call", "_assertThisInitialized", "ReferenceError", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "bind", "__proto__", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "p", "ownKeys", "r", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "push", "_objectSpread", "arguments", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "obj", "_toPrimitive", "toPrimitive", "String", "Number", "_slicedToArray", "arr", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "minLen", "_arrayLikeToArray", "n", "toString", "slice", "name", "Array", "from", "test", "len", "arr2", "l", "u", "a", "f", "next", "done", "isArray", "_extends", "assign", "source", "hasOwnProperty", "React", "isFunction", "some", "clsx", "Layer", "Label", "ifOverflowMatches", "isNumOrStr", "createLabeledScales", "rectWithCoords", "warn", "filterProps", "useClipPathId", "useViewBox", "useXAxisOrThrow", "useYAxisOrThrow", "renderLine", "option", "line", "isValidElement", "cloneElement", "createElement", "className", "getEndPoints", "scales", "isFixedX", "isFixedY", "isSegment", "viewBox", "position", "xAxisOrientation", "yAxisOrientation", "x", "y", "width", "height", "yCoord", "coord", "isInRange", "points", "reverse", "xCoord", "_coord", "_points", "segment", "_points2", "map", "ReferenceLineImpl", "fixedX", "fixedY", "xAxisId", "yAxisId", "shape", "alwaysShow", "clipPathId", "xAxis", "yAxis", "undefined", "scale", "isX", "isY", "endPoints", "orientation", "_endPoints", "_endPoints$", "x1", "y1", "_endPoints$2", "x2", "y2", "clipPath", "concat", "lineProps", "renderCallByParent", "ReferenceLine", "_React$Component", "render", "Component", "isFront", "ifOverflow", "fill", "stroke", "fillOpacity", "strokeWidth"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/recharts/es6/cartesian/ReferenceLine.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n/**\n * @fileOverview Reference Line\n */\nimport React from 'react';\nimport isFunction from 'lodash/isFunction';\nimport some from 'lodash/some';\nimport clsx from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Label } from '../component/Label';\nimport { ifOverflowMatches } from '../util/IfOverflowMatches';\nimport { isNumOrStr } from '../util/DataUtils';\nimport { createLabeledScales, rectWithCoords } from '../util/CartesianUtils';\nimport { warn } from '../util/LogUtils';\nimport { filterProps } from '../util/ReactUtils';\nimport { useClipPathId, useViewBox, useXAxisOrThrow, useYAxisOrThrow } from '../context/chartLayoutContext';\n\n/**\n * This excludes `viewBox` prop from svg for two reasons:\n * 1. The components wants viewBox of object type, and svg wants string\n *    - so there's a conflict, and the component will throw if it gets string\n * 2. Internally the component calls `filterProps` which filters the viewBox away anyway\n */\n\nvar renderLine = function renderLine(option, props) {\n  var line;\n  if ( /*#__PURE__*/React.isValidElement(option)) {\n    line = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (isFunction(option)) {\n    line = option(props);\n  } else {\n    line = /*#__PURE__*/React.createElement(\"line\", _extends({}, props, {\n      className: \"recharts-reference-line-line\"\n    }));\n  }\n  return line;\n};\n// TODO: ScaleHelper\nexport var getEndPoints = function getEndPoints(scales, isFixedX, isFixedY, isSegment, viewBox, position, xAxisOrientation, yAxisOrientation, props) {\n  var x = viewBox.x,\n    y = viewBox.y,\n    width = viewBox.width,\n    height = viewBox.height;\n  if (isFixedY) {\n    var yCoord = props.y;\n    var coord = scales.y.apply(yCoord, {\n      position: position\n    });\n    if (ifOverflowMatches(props, 'discard') && !scales.y.isInRange(coord)) {\n      return null;\n    }\n    var points = [{\n      x: x + width,\n      y: coord\n    }, {\n      x: x,\n      y: coord\n    }];\n    return yAxisOrientation === 'left' ? points.reverse() : points;\n  }\n  if (isFixedX) {\n    var xCoord = props.x;\n    var _coord = scales.x.apply(xCoord, {\n      position: position\n    });\n    if (ifOverflowMatches(props, 'discard') && !scales.x.isInRange(_coord)) {\n      return null;\n    }\n    var _points = [{\n      x: _coord,\n      y: y + height\n    }, {\n      x: _coord,\n      y: y\n    }];\n    return xAxisOrientation === 'top' ? _points.reverse() : _points;\n  }\n  if (isSegment) {\n    var segment = props.segment;\n    var _points2 = segment.map(function (p) {\n      return scales.apply(p, {\n        position: position\n      });\n    });\n    if (ifOverflowMatches(props, 'discard') && some(_points2, function (p) {\n      return !scales.isInRange(p);\n    })) {\n      return null;\n    }\n    return _points2;\n  }\n  return null;\n};\nfunction ReferenceLineImpl(props) {\n  var fixedX = props.x,\n    fixedY = props.y,\n    segment = props.segment,\n    xAxisId = props.xAxisId,\n    yAxisId = props.yAxisId,\n    shape = props.shape,\n    className = props.className,\n    alwaysShow = props.alwaysShow;\n  var clipPathId = useClipPathId();\n  var xAxis = useXAxisOrThrow(xAxisId);\n  var yAxis = useYAxisOrThrow(yAxisId);\n  var viewBox = useViewBox();\n  if (!clipPathId || !viewBox) {\n    return null;\n  }\n  warn(alwaysShow === undefined, 'The alwaysShow prop is deprecated. Please use ifOverflow=\"extendDomain\" instead.');\n  var scales = createLabeledScales({\n    x: xAxis.scale,\n    y: yAxis.scale\n  });\n  var isX = isNumOrStr(fixedX);\n  var isY = isNumOrStr(fixedY);\n  var isSegment = segment && segment.length === 2;\n  var endPoints = getEndPoints(scales, isX, isY, isSegment, viewBox, props.position, xAxis.orientation, yAxis.orientation, props);\n  if (!endPoints) {\n    return null;\n  }\n  var _endPoints = _slicedToArray(endPoints, 2),\n    _endPoints$ = _endPoints[0],\n    x1 = _endPoints$.x,\n    y1 = _endPoints$.y,\n    _endPoints$2 = _endPoints[1],\n    x2 = _endPoints$2.x,\n    y2 = _endPoints$2.y;\n  var clipPath = ifOverflowMatches(props, 'hidden') ? \"url(#\".concat(clipPathId, \")\") : undefined;\n  var lineProps = _objectSpread(_objectSpread({\n    clipPath: clipPath\n  }, filterProps(props, true)), {}, {\n    x1: x1,\n    y1: y1,\n    x2: x2,\n    y2: y2\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: clsx('recharts-reference-line', className)\n  }, renderLine(shape, lineProps), Label.renderCallByParent(props, rectWithCoords({\n    x1: x1,\n    y1: y1,\n    x2: x2,\n    y2: y2\n  })));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function -- requires static defaultProps\nexport var ReferenceLine = /*#__PURE__*/function (_React$Component) {\n  function ReferenceLine() {\n    _classCallCheck(this, ReferenceLine);\n    return _callSuper(this, ReferenceLine, arguments);\n  }\n  _inherits(ReferenceLine, _React$Component);\n  return _createClass(ReferenceLine, [{\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(ReferenceLineImpl, this.props);\n    }\n  }]);\n}(React.Component);\n_defineProperty(ReferenceLine, \"displayName\", 'ReferenceLine');\n_defineProperty(ReferenceLine, \"defaultProps\", {\n  isFront: false,\n  ifOverflow: 'discard',\n  xAxisId: 0,\n  yAxisId: 0,\n  fill: 'none',\n  stroke: '#ccc',\n  fillOpacity: 1,\n  strokeWidth: 1,\n  position: 'middle'\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEU,cAAc,CAACN,UAAU,CAACO,GAAG,CAAC,EAAEP,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASQ,YAAYA,CAACf,WAAW,EAAEgB,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEd,iBAAiB,CAACF,WAAW,CAACH,SAAS,EAAEmB,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEf,iBAAiB,CAACF,WAAW,EAAEiB,WAAW,CAAC;EAAEN,MAAM,CAACC,cAAc,CAACZ,WAAW,EAAE,WAAW,EAAE;IAAEU,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOV,WAAW;AAAE;AAC5R,SAASkB,UAAUA,CAACC,CAAC,EAAE1B,CAAC,EAAE2B,CAAC,EAAE;EAAE,OAAO3B,CAAC,GAAG4B,eAAe,CAAC5B,CAAC,CAAC,EAAE6B,0BAA0B,CAACH,CAAC,EAAEI,yBAAyB,CAAC,CAAC,GAAGC,OAAO,CAACC,SAAS,CAAChC,CAAC,EAAE2B,CAAC,IAAI,EAAE,EAAEC,eAAe,CAACF,CAAC,CAAC,CAACvB,WAAW,CAAC,GAAGH,CAAC,CAACiC,KAAK,CAACP,CAAC,EAAEC,CAAC,CAAC,CAAC;AAAE;AAC1M,SAASE,0BAA0BA,CAACK,IAAI,EAAEC,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKpC,OAAO,CAACoC,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAI3B,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAO4B,sBAAsB,CAACF,IAAI,CAAC;AAAE;AAC/R,SAASE,sBAAsBA,CAACF,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIG,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AACrK,SAASJ,yBAAyBA,CAAA,EAAG;EAAE,IAAI;IAAE,IAAIJ,CAAC,GAAG,CAACY,OAAO,CAAClC,SAAS,CAACmC,OAAO,CAACJ,IAAI,CAACJ,OAAO,CAACC,SAAS,CAACM,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,OAAOZ,CAAC,EAAE,CAAC;EAAE,OAAO,CAACI,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IAAE,OAAO,CAAC,CAACJ,CAAC;EAAE,CAAC,EAAE,CAAC;AAAE;AAClP,SAASE,eAAeA,CAAC5B,CAAC,EAAE;EAAE4B,eAAe,GAAGV,MAAM,CAACsB,cAAc,GAAGtB,MAAM,CAACuB,cAAc,CAACC,IAAI,CAAC,CAAC,GAAG,SAASd,eAAeA,CAAC5B,CAAC,EAAE;IAAE,OAAOA,CAAC,CAAC2C,SAAS,IAAIzB,MAAM,CAACuB,cAAc,CAACzC,CAAC,CAAC;EAAE,CAAC;EAAE,OAAO4B,eAAe,CAAC5B,CAAC,CAAC;AAAE;AACnN,SAAS4C,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAItC,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEqC,QAAQ,CAACzC,SAAS,GAAGc,MAAM,CAAC6B,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC1C,SAAS,EAAE;IAAED,WAAW,EAAE;MAAE6C,KAAK,EAAEH,QAAQ;MAAE5B,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEE,MAAM,CAACC,cAAc,CAAC0B,QAAQ,EAAE,WAAW,EAAE;IAAE5B,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAI6B,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACjD,CAAC,EAAEkD,CAAC,EAAE;EAAED,eAAe,GAAG/B,MAAM,CAACsB,cAAc,GAAGtB,MAAM,CAACsB,cAAc,CAACE,IAAI,CAAC,CAAC,GAAG,SAASO,eAAeA,CAACjD,CAAC,EAAEkD,CAAC,EAAE;IAAElD,CAAC,CAAC2C,SAAS,GAAGO,CAAC;IAAE,OAAOlD,CAAC;EAAE,CAAC;EAAE,OAAOiD,eAAe,CAACjD,CAAC,EAAEkD,CAAC,CAAC;AAAE;AACvM,SAASC,OAAOA,CAACxB,CAAC,EAAEyB,CAAC,EAAE;EAAE,IAAI1B,CAAC,GAAGR,MAAM,CAACmC,IAAI,CAAC1B,CAAC,CAAC;EAAE,IAAIT,MAAM,CAACoC,qBAAqB,EAAE;IAAE,IAAItD,CAAC,GAAGkB,MAAM,CAACoC,qBAAqB,CAAC3B,CAAC,CAAC;IAAEyB,CAAC,KAAKpD,CAAC,GAAGA,CAAC,CAACuD,MAAM,CAAC,UAAUH,CAAC,EAAE;MAAE,OAAOlC,MAAM,CAACsC,wBAAwB,CAAC7B,CAAC,EAAEyB,CAAC,CAAC,CAACrC,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEW,CAAC,CAAC+B,IAAI,CAACxB,KAAK,CAACP,CAAC,EAAE1B,CAAC,CAAC;EAAE;EAAE,OAAO0B,CAAC;AAAE;AAC9P,SAASgC,aAAaA,CAAC/B,CAAC,EAAE;EAAE,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,SAAS,CAAC9C,MAAM,EAAEuC,CAAC,EAAE,EAAE;IAAE,IAAI1B,CAAC,GAAG,IAAI,IAAIiC,SAAS,CAACP,CAAC,CAAC,GAAGO,SAAS,CAACP,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGD,OAAO,CAACjC,MAAM,CAACQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACkC,OAAO,CAAC,UAAUR,CAAC,EAAE;MAAES,eAAe,CAAClC,CAAC,EAAEyB,CAAC,EAAE1B,CAAC,CAAC0B,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGlC,MAAM,CAAC4C,yBAAyB,GAAG5C,MAAM,CAAC6C,gBAAgB,CAACpC,CAAC,EAAET,MAAM,CAAC4C,yBAAyB,CAACpC,CAAC,CAAC,CAAC,GAAGyB,OAAO,CAACjC,MAAM,CAACQ,CAAC,CAAC,CAAC,CAACkC,OAAO,CAAC,UAAUR,CAAC,EAAE;MAAElC,MAAM,CAACC,cAAc,CAACQ,CAAC,EAAEyB,CAAC,EAAElC,MAAM,CAACsC,wBAAwB,CAAC9B,CAAC,EAAE0B,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOzB,CAAC;AAAE;AACtb,SAASkC,eAAeA,CAACG,GAAG,EAAE3C,GAAG,EAAE2B,KAAK,EAAE;EAAE3B,GAAG,GAAGD,cAAc,CAACC,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAI2C,GAAG,EAAE;IAAE9C,MAAM,CAACC,cAAc,CAAC6C,GAAG,EAAE3C,GAAG,EAAE;MAAE2B,KAAK,EAAEA,KAAK;MAAEjC,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE+C,GAAG,CAAC3C,GAAG,CAAC,GAAG2B,KAAK;EAAE;EAAE,OAAOgB,GAAG;AAAE;AAC3O,SAAS5C,cAAcA,CAACM,CAAC,EAAE;EAAE,IAAId,CAAC,GAAGqD,YAAY,CAACvC,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI3B,OAAO,CAACa,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASqD,YAAYA,CAACvC,CAAC,EAAE0B,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIrD,OAAO,CAAC2B,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIC,CAAC,GAAGD,CAAC,CAACzB,MAAM,CAACiE,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKvC,CAAC,EAAE;IAAE,IAAIf,CAAC,GAAGe,CAAC,CAACQ,IAAI,CAACT,CAAC,EAAE0B,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIrD,OAAO,CAACa,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIJ,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK4C,CAAC,GAAGe,MAAM,GAAGC,MAAM,EAAE1C,CAAC,CAAC;AAAE;AAC3T,SAAS2C,cAAcA,CAACC,GAAG,EAAE1D,CAAC,EAAE;EAAE,OAAO2D,eAAe,CAACD,GAAG,CAAC,IAAIE,qBAAqB,CAACF,GAAG,EAAE1D,CAAC,CAAC,IAAI6D,2BAA2B,CAACH,GAAG,EAAE1D,CAAC,CAAC,IAAI8D,gBAAgB,CAAC,CAAC;AAAE;AAC7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIlE,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAChM,SAASiE,2BAA2BA,CAACzE,CAAC,EAAE2E,MAAM,EAAE;EAAE,IAAI,CAAC3E,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAO4E,iBAAiB,CAAC5E,CAAC,EAAE2E,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAG3D,MAAM,CAACd,SAAS,CAAC0E,QAAQ,CAAC3C,IAAI,CAACnC,CAAC,CAAC,CAAC+E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIF,CAAC,KAAK,QAAQ,IAAI7E,CAAC,CAACG,WAAW,EAAE0E,CAAC,GAAG7E,CAAC,CAACG,WAAW,CAAC6E,IAAI;EAAE,IAAIH,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOI,KAAK,CAACC,IAAI,CAAClF,CAAC,CAAC;EAAE,IAAI6E,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACM,IAAI,CAACN,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAAC5E,CAAC,EAAE2E,MAAM,CAAC;AAAE;AAC/Z,SAASC,iBAAiBA,CAACN,GAAG,EAAEc,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGd,GAAG,CAACzD,MAAM,EAAEuE,GAAG,GAAGd,GAAG,CAACzD,MAAM;EAAE,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEyE,IAAI,GAAG,IAAIJ,KAAK,CAACG,GAAG,CAAC,EAAExE,CAAC,GAAGwE,GAAG,EAAExE,CAAC,EAAE,EAAEyE,IAAI,CAACzE,CAAC,CAAC,GAAG0D,GAAG,CAAC1D,CAAC,CAAC;EAAE,OAAOyE,IAAI;AAAE;AAClL,SAASb,qBAAqBA,CAACpB,CAAC,EAAEkC,CAAC,EAAE;EAAE,IAAI5D,CAAC,GAAG,IAAI,IAAI0B,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOnD,MAAM,IAAImD,CAAC,CAACnD,MAAM,CAACC,QAAQ,CAAC,IAAIkD,CAAC,CAAC,YAAY,CAAC;EAAE,IAAI,IAAI,IAAI1B,CAAC,EAAE;IAAE,IAAIC,CAAC;MAAEkD,CAAC;MAAEjE,CAAC;MAAE2E,CAAC;MAAEC,CAAC,GAAG,EAAE;MAAEC,CAAC,GAAG,CAAC,CAAC;MAAEzF,CAAC,GAAG,CAAC,CAAC;IAAE,IAAI;MAAE,IAAIY,CAAC,GAAG,CAACc,CAAC,GAAGA,CAAC,CAACS,IAAI,CAACiB,CAAC,CAAC,EAAEsC,IAAI,EAAE,CAAC,KAAKJ,CAAC,EAAE;QAAE,IAAIpE,MAAM,CAACQ,CAAC,CAAC,KAAKA,CAAC,EAAE;QAAQ+D,CAAC,GAAG,CAAC,CAAC;MAAE,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAAC9D,CAAC,GAAGf,CAAC,CAACuB,IAAI,CAACT,CAAC,CAAC,EAAEiE,IAAI,CAAC,KAAKH,CAAC,CAAC/B,IAAI,CAAC9B,CAAC,CAACqB,KAAK,CAAC,EAAEwC,CAAC,CAAC3E,MAAM,KAAKyE,CAAC,CAAC,EAAEG,CAAC,GAAG,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,OAAOrC,CAAC,EAAE;MAAEpD,CAAC,GAAG,CAAC,CAAC,EAAE6E,CAAC,GAAGzB,CAAC;IAAE,CAAC,SAAS;MAAE,IAAI;QAAE,IAAI,CAACqC,CAAC,IAAI,IAAI,IAAI/D,CAAC,CAAC,QAAQ,CAAC,KAAK6D,CAAC,GAAG7D,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAER,MAAM,CAACqE,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MAAQ,CAAC,SAAS;QAAE,IAAIvF,CAAC,EAAE,MAAM6E,CAAC;MAAE;IAAE;IAAE,OAAOW,CAAC;EAAE;AAAE;AACzhB,SAASjB,eAAeA,CAACD,GAAG,EAAE;EAAE,IAAIW,KAAK,CAACW,OAAO,CAACtB,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AACpE,SAASuB,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAG3E,MAAM,CAAC4E,MAAM,GAAG5E,MAAM,CAAC4E,MAAM,CAACpD,IAAI,CAAC,CAAC,GAAG,UAAUhC,MAAM,EAAE;IAAE,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+C,SAAS,CAAC9C,MAAM,EAAED,CAAC,EAAE,EAAE;MAAE,IAAImF,MAAM,GAAGpC,SAAS,CAAC/C,CAAC,CAAC;MAAE,KAAK,IAAIS,GAAG,IAAI0E,MAAM,EAAE;QAAE,IAAI7E,MAAM,CAACd,SAAS,CAAC4F,cAAc,CAAC7D,IAAI,CAAC4D,MAAM,EAAE1E,GAAG,CAAC,EAAE;UAAEX,MAAM,CAACW,GAAG,CAAC,GAAG0E,MAAM,CAAC1E,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOX,MAAM;EAAE,CAAC;EAAE,OAAOmF,QAAQ,CAAC5D,KAAK,CAAC,IAAI,EAAE0B,SAAS,CAAC;AAAE;AAClV;AACA;AACA;AACA,OAAOsC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,mBAAmB,EAAEC,cAAc,QAAQ,wBAAwB;AAC5E,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,aAAa,EAAEC,UAAU,EAAEC,eAAe,EAAEC,eAAe,QAAQ,+BAA+B;;AAE3G;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,MAAM,EAAEvG,KAAK,EAAE;EAClD,IAAIwG,IAAI;EACR,IAAK,aAAalB,KAAK,CAACmB,cAAc,CAACF,MAAM,CAAC,EAAE;IAC9CC,IAAI,GAAG,aAAalB,KAAK,CAACoB,YAAY,CAACH,MAAM,EAAEvG,KAAK,CAAC;EACvD,CAAC,MAAM,IAAIuF,UAAU,CAACgB,MAAM,CAAC,EAAE;IAC7BC,IAAI,GAAGD,MAAM,CAACvG,KAAK,CAAC;EACtB,CAAC,MAAM;IACLwG,IAAI,GAAG,aAAalB,KAAK,CAACqB,aAAa,CAAC,MAAM,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAElF,KAAK,EAAE;MAClE4G,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;EACL;EACA,OAAOJ,IAAI;AACb,CAAC;AACD;AACA,OAAO,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAErH,KAAK,EAAE;EACnJ,IAAIsH,CAAC,GAAGJ,OAAO,CAACI,CAAC;IACfC,CAAC,GAAGL,OAAO,CAACK,CAAC;IACbC,KAAK,GAAGN,OAAO,CAACM,KAAK;IACrBC,MAAM,GAAGP,OAAO,CAACO,MAAM;EACzB,IAAIT,QAAQ,EAAE;IACZ,IAAIU,MAAM,GAAG1H,KAAK,CAACuH,CAAC;IACpB,IAAII,KAAK,GAAGb,MAAM,CAACS,CAAC,CAACjG,KAAK,CAACoG,MAAM,EAAE;MACjCP,QAAQ,EAAEA;IACZ,CAAC,CAAC;IACF,IAAIvB,iBAAiB,CAAC5F,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC8G,MAAM,CAACS,CAAC,CAACK,SAAS,CAACD,KAAK,CAAC,EAAE;MACrE,OAAO,IAAI;IACb;IACA,IAAIE,MAAM,GAAG,CAAC;MACZP,CAAC,EAAEA,CAAC,GAAGE,KAAK;MACZD,CAAC,EAAEI;IACL,CAAC,EAAE;MACDL,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEI;IACL,CAAC,CAAC;IACF,OAAON,gBAAgB,KAAK,MAAM,GAAGQ,MAAM,CAACC,OAAO,CAAC,CAAC,GAAGD,MAAM;EAChE;EACA,IAAId,QAAQ,EAAE;IACZ,IAAIgB,MAAM,GAAG/H,KAAK,CAACsH,CAAC;IACpB,IAAIU,MAAM,GAAGlB,MAAM,CAACQ,CAAC,CAAChG,KAAK,CAACyG,MAAM,EAAE;MAClCZ,QAAQ,EAAEA;IACZ,CAAC,CAAC;IACF,IAAIvB,iBAAiB,CAAC5F,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC8G,MAAM,CAACQ,CAAC,CAACM,SAAS,CAACI,MAAM,CAAC,EAAE;MACtE,OAAO,IAAI;IACb;IACA,IAAIC,OAAO,GAAG,CAAC;MACbX,CAAC,EAAEU,MAAM;MACTT,CAAC,EAAEA,CAAC,GAAGE;IACT,CAAC,EAAE;MACDH,CAAC,EAAEU,MAAM;MACTT,CAAC,EAAEA;IACL,CAAC,CAAC;IACF,OAAOH,gBAAgB,KAAK,KAAK,GAAGa,OAAO,CAACH,OAAO,CAAC,CAAC,GAAGG,OAAO;EACjE;EACA,IAAIhB,SAAS,EAAE;IACb,IAAIiB,OAAO,GAAGlI,KAAK,CAACkI,OAAO;IAC3B,IAAIC,QAAQ,GAAGD,OAAO,CAACE,GAAG,CAAC,UAAU7F,CAAC,EAAE;MACtC,OAAOuE,MAAM,CAACxF,KAAK,CAACiB,CAAC,EAAE;QACrB4E,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIvB,iBAAiB,CAAC5F,KAAK,EAAE,SAAS,CAAC,IAAIwF,IAAI,CAAC2C,QAAQ,EAAE,UAAU5F,CAAC,EAAE;MACrE,OAAO,CAACuE,MAAM,CAACc,SAAS,CAACrF,CAAC,CAAC;IAC7B,CAAC,CAAC,EAAE;MACF,OAAO,IAAI;IACb;IACA,OAAO4F,QAAQ;EACjB;EACA,OAAO,IAAI;AACb,CAAC;AACD,SAASE,iBAAiBA,CAACrI,KAAK,EAAE;EAChC,IAAIsI,MAAM,GAAGtI,KAAK,CAACsH,CAAC;IAClBiB,MAAM,GAAGvI,KAAK,CAACuH,CAAC;IAChBW,OAAO,GAAGlI,KAAK,CAACkI,OAAO;IACvBM,OAAO,GAAGxI,KAAK,CAACwI,OAAO;IACvBC,OAAO,GAAGzI,KAAK,CAACyI,OAAO;IACvBC,KAAK,GAAG1I,KAAK,CAAC0I,KAAK;IACnB9B,SAAS,GAAG5G,KAAK,CAAC4G,SAAS;IAC3B+B,UAAU,GAAG3I,KAAK,CAAC2I,UAAU;EAC/B,IAAIC,UAAU,GAAG1C,aAAa,CAAC,CAAC;EAChC,IAAI2C,KAAK,GAAGzC,eAAe,CAACoC,OAAO,CAAC;EACpC,IAAIM,KAAK,GAAGzC,eAAe,CAACoC,OAAO,CAAC;EACpC,IAAIvB,OAAO,GAAGf,UAAU,CAAC,CAAC;EAC1B,IAAI,CAACyC,UAAU,IAAI,CAAC1B,OAAO,EAAE;IAC3B,OAAO,IAAI;EACb;EACAlB,IAAI,CAAC2C,UAAU,KAAKI,SAAS,EAAE,kFAAkF,CAAC;EAClH,IAAIjC,MAAM,GAAGhB,mBAAmB,CAAC;IAC/BwB,CAAC,EAAEuB,KAAK,CAACG,KAAK;IACdzB,CAAC,EAAEuB,KAAK,CAACE;EACX,CAAC,CAAC;EACF,IAAIC,GAAG,GAAGpD,UAAU,CAACyC,MAAM,CAAC;EAC5B,IAAIY,GAAG,GAAGrD,UAAU,CAAC0C,MAAM,CAAC;EAC5B,IAAItB,SAAS,GAAGiB,OAAO,IAAIA,OAAO,CAAChI,MAAM,KAAK,CAAC;EAC/C,IAAIiJ,SAAS,GAAGtC,YAAY,CAACC,MAAM,EAAEmC,GAAG,EAAEC,GAAG,EAAEjC,SAAS,EAAEC,OAAO,EAAElH,KAAK,CAACmH,QAAQ,EAAE0B,KAAK,CAACO,WAAW,EAAEN,KAAK,CAACM,WAAW,EAAEpJ,KAAK,CAAC;EAC/H,IAAI,CAACmJ,SAAS,EAAE;IACd,OAAO,IAAI;EACb;EACA,IAAIE,UAAU,GAAG3F,cAAc,CAACyF,SAAS,EAAE,CAAC,CAAC;IAC3CG,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC3BE,EAAE,GAAGD,WAAW,CAAChC,CAAC;IAClBkC,EAAE,GAAGF,WAAW,CAAC/B,CAAC;IAClBkC,YAAY,GAAGJ,UAAU,CAAC,CAAC,CAAC;IAC5BK,EAAE,GAAGD,YAAY,CAACnC,CAAC;IACnBqC,EAAE,GAAGF,YAAY,CAAClC,CAAC;EACrB,IAAIqC,QAAQ,GAAGhE,iBAAiB,CAAC5F,KAAK,EAAE,QAAQ,CAAC,GAAG,OAAO,CAAC6J,MAAM,CAACjB,UAAU,EAAE,GAAG,CAAC,GAAGG,SAAS;EAC/F,IAAIe,SAAS,GAAG/G,aAAa,CAACA,aAAa,CAAC;IAC1C6G,QAAQ,EAAEA;EACZ,CAAC,EAAE3D,WAAW,CAACjG,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChCuJ,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA,EAAE;IACNE,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA;EACN,CAAC,CAAC;EACF,OAAO,aAAarE,KAAK,CAACqB,aAAa,CAACjB,KAAK,EAAE;IAC7CkB,SAAS,EAAEnB,IAAI,CAAC,yBAAyB,EAAEmB,SAAS;EACtD,CAAC,EAAEN,UAAU,CAACoC,KAAK,EAAEoB,SAAS,CAAC,EAAEnE,KAAK,CAACoE,kBAAkB,CAAC/J,KAAK,EAAE+F,cAAc,CAAC;IAC9EwD,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA,EAAE;IACNE,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA;EACN,CAAC,CAAC,CAAC,CAAC;AACN;;AAEA;AACA,OAAO,IAAIK,aAAa,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAClE,SAASD,aAAaA,CAAA,EAAG;IACvBtK,eAAe,CAAC,IAAI,EAAEsK,aAAa,CAAC;IACpC,OAAOlJ,UAAU,CAAC,IAAI,EAAEkJ,aAAa,EAAEhH,SAAS,CAAC;EACnD;EACAf,SAAS,CAAC+H,aAAa,EAAEC,gBAAgB,CAAC;EAC1C,OAAOtJ,YAAY,CAACqJ,aAAa,EAAE,CAAC;IAClCtJ,GAAG,EAAE,QAAQ;IACb2B,KAAK,EAAE,SAAS6H,MAAMA,CAAA,EAAG;MACvB,OAAO,aAAa5E,KAAK,CAACqB,aAAa,CAAC0B,iBAAiB,EAAE,IAAI,CAACrI,KAAK,CAAC;IACxE;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAACsF,KAAK,CAAC6E,SAAS,CAAC;AAClBjH,eAAe,CAAC8G,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC;AAC9D9G,eAAe,CAAC8G,aAAa,EAAE,cAAc,EAAE;EAC7CI,OAAO,EAAE,KAAK;EACdC,UAAU,EAAE,SAAS;EACrB7B,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACV6B,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE,CAAC;EACdC,WAAW,EAAE,CAAC;EACdtD,QAAQ,EAAE;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}