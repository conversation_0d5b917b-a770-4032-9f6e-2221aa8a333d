{"ast": null, "code": "var SetCache = require('./_SetCache'),\n  arrayIncludes = require('./_arrayIncludes'),\n  arrayIncludesWith = require('./_arrayIncludesWith'),\n  cacheHas = require('./_cacheHas'),\n  createSet = require('./_createSet'),\n  setToArray = require('./_setToArray');\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/**\n * The base implementation of `_.uniqBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new duplicate free array.\n */\nfunction baseUniq(array, iteratee, comparator) {\n  var index = -1,\n    includes = arrayIncludes,\n    length = array.length,\n    isCommon = true,\n    result = [],\n    seen = result;\n  if (comparator) {\n    isCommon = false;\n    includes = arrayIncludesWith;\n  } else if (length >= LARGE_ARRAY_SIZE) {\n    var set = iteratee ? null : createSet(array);\n    if (set) {\n      return setToArray(set);\n    }\n    isCommon = false;\n    includes = cacheHas;\n    seen = new SetCache();\n  } else {\n    seen = iteratee ? [] : result;\n  }\n  outer: while (++index < length) {\n    var value = array[index],\n      computed = iteratee ? iteratee(value) : value;\n    value = comparator || value !== 0 ? value : 0;\n    if (isCommon && computed === computed) {\n      var seenIndex = seen.length;\n      while (seenIndex--) {\n        if (seen[seenIndex] === computed) {\n          continue outer;\n        }\n      }\n      if (iteratee) {\n        seen.push(computed);\n      }\n      result.push(value);\n    } else if (!includes(seen, computed, comparator)) {\n      if (seen !== result) {\n        seen.push(computed);\n      }\n      result.push(value);\n    }\n  }\n  return result;\n}\nmodule.exports = baseUniq;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "arrayIncludes", "arrayIncludesWith", "cacheHas", "createSet", "setToArray", "LARGE_ARRAY_SIZE", "baseUniq", "array", "iteratee", "comparator", "index", "includes", "length", "isCommon", "result", "seen", "set", "outer", "value", "computed", "seenIndex", "push", "module", "exports"], "sources": ["D:/MobioProjects/iot-poc/frontend/node_modules/lodash/_baseUniq.js"], "sourcesContent": ["var SetCache = require('./_SetCache'),\n    arrayIncludes = require('./_arrayIncludes'),\n    arrayIncludesWith = require('./_arrayIncludesWith'),\n    cacheHas = require('./_cacheHas'),\n    createSet = require('./_createSet'),\n    setToArray = require('./_setToArray');\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/**\n * The base implementation of `_.uniqBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new duplicate free array.\n */\nfunction baseUniq(array, iteratee, comparator) {\n  var index = -1,\n      includes = arrayIncludes,\n      length = array.length,\n      isCommon = true,\n      result = [],\n      seen = result;\n\n  if (comparator) {\n    isCommon = false;\n    includes = arrayIncludesWith;\n  }\n  else if (length >= LARGE_ARRAY_SIZE) {\n    var set = iteratee ? null : createSet(array);\n    if (set) {\n      return setToArray(set);\n    }\n    isCommon = false;\n    includes = cacheHas;\n    seen = new SetCache;\n  }\n  else {\n    seen = iteratee ? [] : result;\n  }\n  outer:\n  while (++index < length) {\n    var value = array[index],\n        computed = iteratee ? iteratee(value) : value;\n\n    value = (comparator || value !== 0) ? value : 0;\n    if (isCommon && computed === computed) {\n      var seenIndex = seen.length;\n      while (seenIndex--) {\n        if (seen[seenIndex] === computed) {\n          continue outer;\n        }\n      }\n      if (iteratee) {\n        seen.push(computed);\n      }\n      result.push(value);\n    }\n    else if (!includes(seen, computed, comparator)) {\n      if (seen !== result) {\n        seen.push(computed);\n      }\n      result.push(value);\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseUniq;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACjCC,aAAa,GAAGD,OAAO,CAAC,kBAAkB,CAAC;EAC3CE,iBAAiB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;EACnDG,QAAQ,GAAGH,OAAO,CAAC,aAAa,CAAC;EACjCI,SAAS,GAAGJ,OAAO,CAAC,cAAc,CAAC;EACnCK,UAAU,GAAGL,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA,IAAIM,gBAAgB,GAAG,GAAG;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EAC7C,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,QAAQ,GAAGX,aAAa;IACxBY,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,QAAQ,GAAG,IAAI;IACfC,MAAM,GAAG,EAAE;IACXC,IAAI,GAAGD,MAAM;EAEjB,IAAIL,UAAU,EAAE;IACdI,QAAQ,GAAG,KAAK;IAChBF,QAAQ,GAAGV,iBAAiB;EAC9B,CAAC,MACI,IAAIW,MAAM,IAAIP,gBAAgB,EAAE;IACnC,IAAIW,GAAG,GAAGR,QAAQ,GAAG,IAAI,GAAGL,SAAS,CAACI,KAAK,CAAC;IAC5C,IAAIS,GAAG,EAAE;MACP,OAAOZ,UAAU,CAACY,GAAG,CAAC;IACxB;IACAH,QAAQ,GAAG,KAAK;IAChBF,QAAQ,GAAGT,QAAQ;IACnBa,IAAI,GAAG,IAAIjB,QAAQ,CAAD,CAAC;EACrB,CAAC,MACI;IACHiB,IAAI,GAAGP,QAAQ,GAAG,EAAE,GAAGM,MAAM;EAC/B;EACAG,KAAK,EACL,OAAO,EAAEP,KAAK,GAAGE,MAAM,EAAE;IACvB,IAAIM,KAAK,GAAGX,KAAK,CAACG,KAAK,CAAC;MACpBS,QAAQ,GAAGX,QAAQ,GAAGA,QAAQ,CAACU,KAAK,CAAC,GAAGA,KAAK;IAEjDA,KAAK,GAAIT,UAAU,IAAIS,KAAK,KAAK,CAAC,GAAIA,KAAK,GAAG,CAAC;IAC/C,IAAIL,QAAQ,IAAIM,QAAQ,KAAKA,QAAQ,EAAE;MACrC,IAAIC,SAAS,GAAGL,IAAI,CAACH,MAAM;MAC3B,OAAOQ,SAAS,EAAE,EAAE;QAClB,IAAIL,IAAI,CAACK,SAAS,CAAC,KAAKD,QAAQ,EAAE;UAChC,SAASF,KAAK;QAChB;MACF;MACA,IAAIT,QAAQ,EAAE;QACZO,IAAI,CAACM,IAAI,CAACF,QAAQ,CAAC;MACrB;MACAL,MAAM,CAACO,IAAI,CAACH,KAAK,CAAC;IACpB,CAAC,MACI,IAAI,CAACP,QAAQ,CAACI,IAAI,EAAEI,QAAQ,EAAEV,UAAU,CAAC,EAAE;MAC9C,IAAIM,IAAI,KAAKD,MAAM,EAAE;QACnBC,IAAI,CAACM,IAAI,CAACF,QAAQ,CAAC;MACrB;MACAL,MAAM,CAACO,IAAI,CAACH,KAAK,CAAC;IACpB;EACF;EACA,OAAOJ,MAAM;AACf;AAEAQ,MAAM,CAACC,OAAO,GAAGjB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}