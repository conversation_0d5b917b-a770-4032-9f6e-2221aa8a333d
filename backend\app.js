const express = require("express");
const cors = require("cors");
const pool = require("./db");
require("./mqttHandler");

const app = express();
app.use(cors());

app.get("/api/data", async (req, res) => {
  const result = await pool.query(
    "SELECT * FROM sensor_data ORDER BY timestamp DESC LIMIT 50"
  );
  res.json(result.rows);
});

app.listen(5000, () => console.log("Backend running on port 5000"));
